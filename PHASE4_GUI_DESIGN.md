# Phase 4 GUI 设计文档

## 🎯 设计目标

为狼人杀AI游戏创建现代化、直观的图形用户界面，提升用户体验。

## 🛠️ 技术选择

### GUI框架：tkinter
- **优势**：Python内置，无需额外依赖，跨平台支持
- **适用性**：适合桌面应用，支持复杂布局和事件处理
- **扩展性**：可配合ttk主题，支持现代化UI设计

### 架构模式：MVC (Model-View-Controller)
- **Model**：现有的游戏引擎和数据模型
- **View**：GUI组件和界面显示
- **Controller**：GUI事件处理和游戏逻辑交互

## 🏗️ 整体架构

```
src/ui/
├── gui/                    # GUI组件目录
│   ├── __init__.py
│   ├── main_window.py      # 主窗口
│   ├── game_board.py       # 游戏面板
│   ├── player_panel.py     # 玩家信息面板
│   ├── chat_panel.py       # 聊天/发言面板
│   ├── control_panel.py    # 控制面板
│   ├── settings_dialog.py  # 设置对话框
│   ├── tutorial_dialog.py  # 教程对话框
│   └── components/         # 通用组件
│       ├── __init__.py
│       ├── player_card.py  # 玩家卡片
│       ├── vote_dialog.py  # 投票对话框
│       ├── action_dialog.py # 行动对话框
│       └── status_bar.py   # 状态栏
├── assets/                 # 资源文件
│   ├── images/            # 图片资源
│   ├── sounds/            # 音效资源
│   └── themes/            # 主题配置
└── styles/                # 样式定义
    ├── __init__.py
    ├── colors.py          # 颜色定义
    ├── fonts.py           # 字体定义
    └── themes.py          # 主题管理
```

## 🎨 界面设计

### 主窗口布局
```
┌─────────────────────────────────────────────────────────┐
│ 菜单栏 [文件] [游戏] [设置] [帮助]                        │
├─────────────────────────────────────────────────────────┤
│ ┌─────────────────┐ ┌─────────────────────────────────┐ │
│ │   玩家信息面板   │ │         游戏主面板              │ │
│ │                │ │                                │ │
│ │ 玩家1 [村民]    │ │    ┌─────────────────────┐    │ │
│ │ 玩家2 [狼人]    │ │    │                     │    │ │
│ │ 玩家3 [预言家]  │ │    │     游戏状态显示     │    │ │
│ │ ...            │ │    │                     │    │ │
│ │                │ │    └─────────────────────┘    │ │
│ │ [当前阶段]      │ │                                │ │
│ │ [回合信息]      │ │    ┌─────────────────────┐    │ │
│ └─────────────────┘ │    │                     │    │ │
│                     │    │     操作区域        │    │ │
│ ┌─────────────────┐ │    │                     │    │ │
│ │   聊天/发言面板  │ │    └─────────────────────┘    │ │
│ │                │ └─────────────────────────────────┘ │
│ │ [发言历史]      │                                     │
│ │                │                                     │
│ │ [输入框]        │                                     │
│ │ [发送按钮]      │                                     │
│ └─────────────────┘                                     │
├─────────────────────────────────────────────────────────┤
│ 状态栏: [游戏状态] [连接状态] [时间]                      │
└─────────────────────────────────────────────────────────┘
```

## 🎮 核心组件设计

### 1. MainWindow (主窗口)
- **职责**：整体布局管理，菜单处理，窗口事件
- **组件**：菜单栏、工具栏、状态栏、主面板容器

### 2. GameBoard (游戏面板)
- **职责**：游戏状态显示，操作区域管理
- **组件**：阶段显示、回合信息、操作按钮

### 3. PlayerPanel (玩家面板)
- **职责**：玩家信息显示，状态更新
- **组件**：玩家卡片列表、角色图标、生存状态

### 4. ChatPanel (聊天面板)
- **职责**：发言显示，消息输入
- **组件**：消息列表、输入框、发送按钮

### 5. ControlPanel (控制面板)
- **职责**：游戏控制，快捷操作
- **组件**：开始/暂停、设置、帮助按钮

## 🎨 视觉设计

### 颜色方案
- **主色调**：深蓝色 (#2C3E50)
- **辅助色**：橙色 (#E67E22)
- **背景色**：浅灰色 (#ECF0F1)
- **文字色**：深灰色 (#2C3E50)
- **强调色**：红色 (#E74C3C) - 危险操作
- **成功色**：绿色 (#27AE60) - 成功状态

### 字体设计
- **标题字体**：微软雅黑 Bold 16px
- **正文字体**：微软雅黑 Regular 12px
- **按钮字体**：微软雅黑 Regular 10px

### 图标设计
- **角色图标**：使用Unicode符号或简单图形
- **状态图标**：生存/死亡状态指示器
- **操作图标**：投票、发言、技能使用等

## 🔄 交互设计

### 游戏流程交互
1. **游戏开始**：显示角色分配，进入夜晚阶段
2. **夜晚阶段**：特殊角色操作界面，其他玩家等待
3. **白天阶段**：发言讨论，投票界面
4. **结果显示**：阶段结果，游戏状态更新

### 用户操作流程
1. **投票操作**：点击玩家 → 确认对话框 → 提交投票
2. **发言操作**：输入文字 → 点击发送 → 显示在聊天面板
3. **技能使用**：选择目标 → 确认操作 → 执行技能

## 📱 响应式设计

### 窗口大小适配
- **最小尺寸**：1024x768
- **推荐尺寸**：1280x800
- **最大尺寸**：自适应屏幕

### 组件自适应
- **玩家面板**：固定宽度，高度自适应
- **游戏面板**：宽度自适应，高度固定
- **聊天面板**：固定宽度，高度自适应

## 🎵 音效集成点

### 音效触发时机
- **游戏开始**：背景音乐开始
- **阶段切换**：提示音效
- **投票操作**：点击音效
- **技能使用**：特殊音效
- **游戏结束**：结束音效

### 音效管理
- **音量控制**：全局音量、音效音量分离
- **音效开关**：可选择关闭音效
- **音效预加载**：提升响应速度

## 🎬 动画效果

### 过渡动画
- **面板切换**：淡入淡出效果
- **状态变化**：颜色渐变
- **按钮交互**：悬停效果

### 游戏动画
- **玩家状态变化**：死亡动画
- **投票过程**：投票统计动画
- **技能效果**：技能使用视觉反馈

## 🔧 技术实现要点

### 事件处理
- **游戏事件**：监听游戏引擎事件，更新UI
- **用户事件**：处理用户操作，调用游戏引擎
- **定时器事件**：定期更新游戏状态

### 数据绑定
- **单向绑定**：游戏状态 → UI显示
- **双向绑定**：用户输入 ↔ 游戏数据
- **事件通知**：状态变化自动更新UI

### 性能优化
- **延迟加载**：按需加载组件
- **虚拟化**：大量数据的高效显示
- **缓存机制**：减少重复计算

## 📋 开发优先级

### 第一阶段：基础框架
1. 主窗口和基础布局
2. 玩家信息显示
3. 基本游戏状态显示

### 第二阶段：核心功能
1. 投票界面
2. 发言系统
3. 游戏流程集成

### 第三阶段：增强功能
1. 音效系统
2. 动画效果
3. 设置系统

### 第四阶段：完善体验
1. 教程系统
2. 主题定制
3. 性能优化