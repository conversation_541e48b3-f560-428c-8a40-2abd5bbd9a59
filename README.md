# 狼人杀AI游戏

一个基于Python的狼人杀AI游戏实现，支持多种AI策略和人机对战。

## Phase 1: 基础架构 ✅

Phase 1 已完成，包含以下核心功能：

### 已实现功能

1. **游戏状态数据结构**
   - 玩家信息和状态管理
   - 游戏配置和回合信息
   - 投票和行动记录系统

2. **基础游戏引擎**
   - 回合管理和状态转换
   - 事件驱动架构
   - 投票和特殊行动处理

3. **角色系统**
   - 完整的角色枚举（村民、狼人、预言家、女巫、守卫、猎人等）
   - 阵营划分和胜负条件
   - 角色特殊能力框架

4. **AI玩家系统**
   - 抽象玩家基类
   - AI玩家基础实现
   - 个性化特征和策略框架

5. **控制台界面**
   - 简单的文本界面
   - 游戏状态显示
   - 基础交互功能

## Phase 2: 核心游戏逻辑 ✅

Phase 2 已完成，大幅增强了游戏功能：

### 新增功能

1. **详细的白天阶段逻辑**
   - 完整的讨论阶段管理
   - 高级投票机制（支持弃权、平票处理）
   - 发言记录和时间限制
   - 猎人复仇等特殊事件处理

2. **完善的夜晚阶段逻辑**
   - 狼人协作杀人机制
   - 预言家查验系统
   - 女巫救人/毒人逻辑
   - 守卫保护机制
   - 行动顺序和冲突解决

3. **精确的胜负条件判定**
   - 多种胜利条件检查
   - 特殊角色死亡影响
   - 边界情况处理
   - 胜利概率计算
   - 游戏平衡分析

4. **游戏历史记录和事件日志**
   - 详细的事件记录系统
   - 游戏回放数据生成
   - 统计分析功能
   - JSON格式导出
   - 时间线追踪

5. **高级游戏配置系统**
   - 多种预设游戏模式
   - 自定义配置验证
   - 平衡性评估
   - 动态角色分配
   - 规则变体支持

## Phase 3: AI策略开发 ✅

Phase 3 已完成，实现了完整的AI策略系统：

### 新增功能

1. **大模型集成组件**
   - 支持OpenAI GPT等大模型服务
   - **集成Qwen3-30B本地大模型服务**
   - 模拟LLM提供者用于测试
   - 智能发言内容生成
   - 策略分析和推理
   - 可扩展的提供者架构

2. **角色专用AI策略**
   - **村民AI**: 投票策略、发言逻辑、推理能力
   - **狼人AI**: 伪装策略、队友配合、欺骗能力
   - **预言家AI**: 查验策略、信息披露、领导能力
   - **女巫AI**: 救人/毒人决策、药剂管理
   - **守卫AI**: 保护策略、目标优先级
   - **猎人AI**: 复仇决策、威胁评估

3. **AI难度等级系统**
   - 6个难度等级：新手→简单→普通→困难→专家→大师
   - 动态参数调整：决策准确性、策略思维、记忆保持
   - 个性化行为模式
   - 适应性学习机制

4. **个性化参数系统**
   - 8种个性类型：攻击型、防守型、分析型、直觉型、社交型、欺骗型、忠诚型、混乱型
   - 16个个性特征维度
   - 动态行为调整
   - 团队平衡分析

5. **统一AI策略管理**
   - 增强AI玩家类
   - 策略管理器
   - 团队创建和分析
   - 实时状态监控
   - 事件驱动学习

## 项目结构

```
wolfkill/
├── src/
│   ├── models/           # 数据模型
│   │   ├── enums.py     # 游戏枚举定义
│   │   ├── player.py    # 玩家相关数据结构
│   │   └── game_state.py # 游戏状态管理
│   ├── engine/          # 游戏引擎
│   │   └── game_engine.py # 核心游戏逻辑
│   ├── players/         # 玩家实现
│   │   ├── base_player.py # 玩家抽象基类
│   │   └── ai_player.py   # AI玩家实现
│   └── ui/              # 用户界面
│       └── console_ui.py  # 控制台界面
├── main.py              # 主程序入口
├── test_basic.py        # 基础功能测试
├── todo.md              # 开发计划
└── README.md            # 项目说明
```

## 快速开始

### Qwen3-30B配置

本项目已集成Qwen3-30B大模型，提供更智能的AI行为。

**配置要求:**
- Qwen3-30B服务运行在 `http://localhost:8005/v1`
- API密钥设置为 `EMPTY`
- 模型名称: `ckpt/Qwen/Qwen3-30B-A3B`

**配置文件位置:** `src/config/llm_config.py`

### 运行游戏

```bash
python main.py
```

### 运行测试

**Phase 1 基础功能测试:**
```bash
python test_basic.py
```

**Phase 2 完整功能测试:**
```bash
python test_phase2.py
```

**Phase 3 AI策略测试:**
```bash
python test_phase3.py
```

### 运行演示

**Phase 1 演示:**
```bash
python demo.py
```

**Phase 2 演示:**
```bash
python demo_phase2.py
```

**Phase 3 演示:**
```bash
python demo_phase3.py
```

**Qwen3-30B集成演示:**
```bash
# 测试Qwen3-30B集成
python test_qwen3_integration.py

# 完整的Qwen3-30B游戏演示
python demo_qwen3_game.py
```

## 游戏特性

### 支持的角色

- **村民**: 普通村民，参与讨论和投票
- **狼人**: 夜晚杀人，白天伪装
- **预言家**: 每晚查验一个玩家身份
- **女巫**: 拥有解药和毒药各一瓶
- **守卫**: 每晚保护一个玩家
- **猎人**: 死亡时可以开枪带走一人

### AI特性

- **个性化特征**: 每个AI都有独特的性格特征
- **动态策略**: 基于游戏状态和历史信息做决策
- **知识管理**: 维护对其他玩家的认知和信任度
- **事件响应**: 根据游戏事件调整策略

### 游戏流程

1. **游戏设置**: 配置玩家数量和角色分配
2. **夜晚阶段**: 狼人杀人，特殊角色使用技能
3. **白天讨论**: 所有玩家发言讨论
4. **投票阶段**: 投票淘汰可疑玩家
5. **胜负判定**: 检查是否达成胜利条件

## 技术架构

### 设计模式

- **事件驱动**: 游戏引擎使用事件系统解耦组件
- **策略模式**: AI玩家使用可插拔的策略系统
- **状态机**: 游戏阶段转换使用状态机模式

### 核心组件

1. **GameEngine**: 游戏核心逻辑和流程控制
2. **GameState**: 游戏状态管理和数据持久化
3. **BasePlayer**: 玩家抽象接口定义
4. **AIPlayer**: AI玩家策略实现

## 开发计划

- [x] **Phase 1**: 基础架构 (已完成)
- [x] **Phase 2**: 核心游戏逻辑 (已完成)
- [x] **Phase 3**: AI策略开发 (已完成)
- [ ] **Phase 4**: 高级功能
- [ ] **Phase 5**: 用户体验
- [ ] **Phase 6**: 测试和优化

详细计划请查看 [todo.md](todo.md)

## 示例用法

### 创建游戏

```python
from src.models.game_state import GameConfig
from src.models.enums import Role
from src.engine.game_engine import GameEngine

# 创建游戏配置
config = GameConfig(
    total_players=6,
    role_distribution={
        Role.VILLAGER: 2,
        Role.WEREWOLF: 2,
        Role.SEER: 1,
        Role.WITCH: 1
    }
)

# 创建游戏引擎
engine = GameEngine(config)

# 创建游戏
player_names = ["Alice", "Bob", "Charlie", "David", "Eve", "Frank"]
game_state = engine.create_game("my_game", player_names)

# 开始游戏
engine.start_game()
```

### 创建AI玩家

```python
from src.players.ai_player import AIPlayer
from src.models.enums import Role

# 创建AI玩家
ai_player = AIPlayer(1, "智能玩家1", difficulty="hard")
ai_player.set_role(Role.VILLAGER)

# AI做决策
vote_target = ai_player.make_vote_decision(game_state, VoteType.ELIMINATION)
speech = ai_player.generate_speech(game_state, GamePhase.DAY_DISCUSSION)
```

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 许可证

MIT License
