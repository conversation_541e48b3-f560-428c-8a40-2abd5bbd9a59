# 狼人杀AI游戏 - 部署指南

## 🎮 项目概述

这是一个基于Python后端和React前端的狼人杀AI游戏实现，支持多种AI策略和实时对战。

### 技术栈

**后端:**
- Python 3.8+
- Flask (Web框架)
- Flask-SocketIO (WebSocket支持)
- 自定义游戏引擎

**前端:**
- React 18 + TypeScript
- Styled-components (样式)
- Socket.io-client (实时通信)
- 动画系统

## 🚀 快速开始

### 环境要求

- Python 3.8 或更高版本
- Node.js 16 或更高版本
- npm 或 yarn

### 1. 克隆项目

```bash
git clone <repository-url>
cd wolfkill
```

### 2. 启动后端服务器

```bash
# 安装Python依赖
cd backend
pip install -r requirements.txt

# 启动后端服务器
python start_backend.py
```

后端服务器将在 http://localhost:8000 启动

### 3. 启动前端服务器

```bash
# 在新的终端窗口中
chmod +x start_frontend.sh
./start_frontend.sh
```

前端应用将在 http://localhost:3000 启动

### 4. 开始游戏

1. 打开浏览器访问 http://localhost:3000
2. 点击"创建游戏"按钮
3. 开始狼人杀游戏！

## 📁 项目结构

```
wolfkill/
├── src/                    # Python游戏核心逻辑
│   ├── models/            # 数据模型
│   ├── engine/            # 游戏引擎
│   ├── ai/                # AI策略
│   └── ui/                # 用户界面
├── backend/               # Flask API服务器
│   ├── api/               # API路由
│   └── requirements.txt   # Python依赖
├── frontend/              # React前端应用
│   ├── src/               # React源码
│   ├── public/            # 静态资源
│   └── package.json       # Node.js依赖
├── tests/                 # 测试文件
└── docs/                  # 文档
```

## 🔧 开发指南

### 后端开发

```bash
# 进入后端目录
cd backend

# 安装开发依赖
pip install -r requirements.txt

# 运行API服务器
python api/app.py

# 运行测试
cd ..
python -m pytest tests/
```

### 前端开发

```bash
# 进入前端目录
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm start

# 构建生产版本
npm run build

# 运行测试
npm test
```

## 🌐 API文档

### REST API端点

- `GET /api/health` - 健康检查
- `POST /api/games` - 创建新游戏
- `GET /api/games/{game_id}` - 获取游戏状态
- `POST /api/games/{game_id}/join` - 加入游戏
- `POST /api/games/{game_id}/start` - 开始游戏
- `POST /api/games/{game_id}/vote` - 提交投票
- `POST /api/games/{game_id}/action` - 执行行动
- `POST /api/games/{game_id}/chat` - 发送聊天消息

### WebSocket事件

- `connect` - 客户端连接
- `join_game` - 加入游戏房间
- `game_state_update` - 游戏状态更新
- `chat_message` - 聊天消息
- `vote_update` - 投票更新
- `action_update` - 行动更新
- `phase_change` - 阶段变化

## 🎯 游戏功能

### 已实现功能

- ✅ 完整的狼人杀游戏逻辑
- ✅ 多种角色支持（村民、狼人、预言家、女巫、守卫、猎人）
- ✅ AI玩家策略
- ✅ 实时Web界面
- ✅ WebSocket实时通信
- ✅ 投票和技能系统
- ✅ 聊天功能
- ✅ 动画效果

### 计划功能

- ⏳ 更多AI策略
- ⏳ 游戏回放系统
- ⏳ 用户账户系统
- ⏳ 排行榜
- ⏳ 自定义房间
- ⏳ 音效系统

## 🐛 故障排除

### 常见问题

1. **后端启动失败**
   ```bash
   # 检查Python版本
   python --version

   # 重新安装依赖
   pip install -r backend/requirements.txt
   ```

2. **前端启动失败**
   ```bash
   # 检查Node.js版本
   node --version

   # 清除缓存并重新安装
   cd frontend
   rm -rf node_modules package-lock.json
   npm install
   ```

3. **WebSocket连接失败**
   - 确保后端服务器正在运行
   - 检查防火墙设置
   - 验证端口8000是否被占用

4. **API请求失败**
   - 检查CORS设置
   - 验证API地址配置
   - 查看浏览器控制台错误

### 日志查看

```bash
# 后端日志
python backend/api/app.py

# 前端日志
# 查看浏览器开发者工具控制台
```

## 📝 贡献指南

1. Fork项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

- 感谢所有贡献者
- 狼人杀游戏规则参考
- 开源社区的支持

---

**享受游戏！🐺**