#!/usr/bin/env python3
"""
狼人杀AI游戏 - Flask API服务器
提供RESTful API接口和WebSocket实时通信
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
from flask_socketio import SocketIO, emit, join_room, leave_room
import sys
import os
import uuid
from datetime import datetime
from typing import Dict, List, Optional

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))

from src.models.game_state import GameConfig
from src.models.enums import Role, GamePhase, VoteType
from src.engine.game_engine import GameEngine

# 创建Flask应用
app = Flask(__name__)
app.config['SECRET_KEY'] = 'wolfkill-secret-key'

# 启用CORS
CORS(app, origins=["http://localhost:3000"])

# 创建SocketIO实例
socketio = SocketIO(app, cors_allowed_origins="http://localhost:3000")

# 全局游戏状态存储
games: Dict[str, GameEngine] = {}
game_rooms: Dict[str, List[str]] = {}  # game_id -> [session_ids]

class APIResponse:
    """API响应格式化"""

    @staticmethod
    def success(data=None, message="操作成功"):
        return {
            "success": True,
            "data": data,
            "message": message,
            "timestamp": datetime.now().isoformat()
        }

    @staticmethod
    def error(message="操作失败", code=400):
        return {
            "success": False,
            "error": message,
            "code": code,
            "timestamp": datetime.now().isoformat()
        }, code

# ==================== REST API 路由 ====================

@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查"""
    return jsonify(APIResponse.success({
        "status": "healthy",
        "version": "1.0.0",
        "active_games": len(games)
    }))

@app.route('/api/games', methods=['POST'])
def create_game():
    """创建新游戏"""
    try:
        data = request.get_json()

        # 验证请求数据
        if not data:
            return jsonify(APIResponse.error("缺少请求数据"))

        total_players = data.get('total_players', 6)
        role_distribution = data.get('role_distribution', {
            'VILLAGER': 2,
            'WEREWOLF': 2,
            'SEER': 1,
            'WITCH': 1
        })

        # 转换角色分布
        role_dist = {}
        for role_name, count in role_distribution.items():
            try:
                role = Role[role_name.upper()]
                role_dist[role] = count
            except KeyError:
                return jsonify(APIResponse.error(f"无效的角色类型: {role_name}"))

        # 创建游戏配置
        config = GameConfig(
            total_players=total_players,
            role_distribution=role_dist
        )

        # 创建游戏引擎
        game_engine = GameEngine(config)

        # 生成玩家名称
        player_names = [f"AI玩家{i+1}" for i in range(total_players)]

        # 创建游戏
        game_id = f"game_{uuid.uuid4().hex[:8]}"
        game_state = game_engine.create_game(game_id, player_names)

        # 存储游戏
        games[game_id] = game_engine
        game_rooms[game_id] = []

        return jsonify(APIResponse.success(
            game_state.to_dict(),
            "游戏创建成功"
        ))

    except Exception as e:
        return jsonify(APIResponse.error(f"创建游戏失败: {str(e)}"))

@app.route('/api/games/<game_id>', methods=['GET'])
def get_game_state(game_id):
    """获取游戏状态"""
    try:
        if game_id not in games:
            return jsonify(APIResponse.error("游戏不存在", 404))

        game_engine = games[game_id]
        game_state = game_engine.get_current_state()

        return jsonify(APIResponse.success(game_state.to_dict()))

    except Exception as e:
        return jsonify(APIResponse.error(f"获取游戏状态失败: {str(e)}"))

@app.route('/api/games/<game_id>/join', methods=['POST'])
def join_game(game_id):
    """加入游戏"""
    try:
        if game_id not in games:
            return jsonify(APIResponse.error("游戏不存在", 404))

        game_engine = games[game_id]
        game_state = game_engine.get_current_state()

        # 检查游戏是否可以加入
        if game_state.current_phase != GamePhase.SETUP:
            return jsonify(APIResponse.error("游戏已开始，无法加入"))

        return jsonify(APIResponse.success(
            game_state.to_dict(),
            "成功加入游戏"
        ))

    except Exception as e:
        return jsonify(APIResponse.error(f"加入游戏失败: {str(e)}"))

@app.route('/api/games/<game_id>/start', methods=['POST'])
def start_game(game_id):
    """开始游戏"""
    try:
        if game_id not in games:
            return jsonify(APIResponse.error("游戏不存在", 404))

        game_engine = games[game_id]

        # 开始游戏
        game_state = game_engine.start_game()

        # 通知所有客户端
        socketio.emit('game_state_update', game_state.to_dict(), room=game_id)
        socketio.emit('phase_change', {
            'phase': game_state.current_phase.value,
            'round': game_state.current_round
        }, room=game_id)

        return jsonify(APIResponse.success(
            game_state.to_dict(),
            "游戏已开始"
        ))

    except Exception as e:
        return jsonify(APIResponse.error(f"开始游戏失败: {str(e)}"))

@app.route('/api/games/<game_id>/vote', methods=['POST'])
def submit_vote(game_id):
    """提交投票"""
    try:
        if game_id not in games:
            return jsonify(APIResponse.error("游戏不存在", 404))

        data = request.get_json()
        if not data:
            return jsonify(APIResponse.error("缺少投票数据"))

        voter_id = data.get('voter_id')
        target_id = data.get('target_id')
        vote_type = data.get('vote_type', 'ELIMINATION')
        reason = data.get('reason', '')

        game_engine = games[game_id]

        # 这里应该调用游戏引擎的投票方法
        # 暂时返回成功响应

        # 通知所有客户端
        socketio.emit('vote_update', {
            'voter_id': voter_id,
            'target_id': target_id,
            'vote_type': vote_type,
            'reason': reason
        }, room=game_id)

        return jsonify(APIResponse.success(None, "投票提交成功"))

    except Exception as e:
        return jsonify(APIResponse.error(f"投票失败: {str(e)}"))

@app.route('/api/games/<game_id>/action', methods=['POST'])
def submit_action(game_id):
    """提交行动"""
    try:
        if game_id not in games:
            return jsonify(APIResponse.error("游戏不存在", 404))

        data = request.get_json()
        if not data:
            return jsonify(APIResponse.error("缺少行动数据"))

        player_id = data.get('player_id')
        action_type = data.get('action_type')
        target_id = data.get('target_id')

        game_engine = games[game_id]

        # 这里应该调用游戏引擎的行动方法
        # 暂时返回成功响应

        # 通知所有客户端
        socketio.emit('action_update', {
            'player_id': player_id,
            'action_type': action_type,
            'target_id': target_id,
            'timestamp': datetime.now().isoformat()
        }, room=game_id)

        return jsonify(APIResponse.success(None, "行动执行成功"))

    except Exception as e:
        return jsonify(APIResponse.error(f"行动执行失败: {str(e)}"))

@app.route('/api/games/<game_id>/chat', methods=['POST'])
def send_chat_message(game_id):
    """发送聊天消息"""
    try:
        if game_id not in games:
            return jsonify(APIResponse.error("游戏不存在", 404))

        data = request.get_json()
        if not data:
            return jsonify(APIResponse.error("缺少消息数据"))

        message = data.get('message', '').strip()
        if not message:
            return jsonify(APIResponse.error("消息内容不能为空"))

        sender = data.get('sender', '匿名玩家')

        # 广播消息给房间内所有客户端
        socketio.emit('chat_message', {
            'sender': sender,
            'message': message,
            'timestamp': datetime.now().isoformat(),
            'type': 'player'
        }, room=game_id)

        return jsonify(APIResponse.success(None, "消息发送成功"))

    except Exception as e:
        return jsonify(APIResponse.error(f"发送消息失败: {str(e)}"))

@app.route('/api/games/<game_id>/history', methods=['GET'])
def get_game_history(game_id):
    """获取游戏历史"""
    try:
        if game_id not in games:
            return jsonify(APIResponse.error("游戏不存在", 404))

        # 这里应该返回游戏历史记录
        # 暂时返回空历史
        history = {
            'game_id': game_id,
            'events': [],
            'votes': [],
            'actions': []
        }

        return jsonify(APIResponse.success(history))

    except Exception as e:
        return jsonify(APIResponse.error(f"获取游戏历史失败: {str(e)}"))

# ==================== WebSocket 事件处理 ====================

@socketio.on('connect')
def handle_connect():
    """客户端连接"""
    print(f"客户端连接: {request.sid}")
    emit('connected', {'message': '连接成功'})

@socketio.on('disconnect')
def handle_disconnect():
    """客户端断开连接"""
    print(f"客户端断开连接: {request.sid}")

    # 从所有游戏房间中移除
    for game_id, session_ids in game_rooms.items():
        if request.sid in session_ids:
            session_ids.remove(request.sid)
            leave_room(game_id)

@socketio.on('join_game')
def handle_join_game(data):
    """加入游戏房间"""
    game_id = data.get('game_id')
    if not game_id:
        emit('error', {'message': '缺少游戏ID'})
        return

    if game_id not in games:
        emit('error', {'message': '游戏不存在'})
        return

    join_room(game_id)
    if game_id not in game_rooms:
        game_rooms[game_id] = []

    if request.sid not in game_rooms[game_id]:
        game_rooms[game_id].append(request.sid)

    emit('joined_game', {'game_id': game_id})
    print(f"客户端 {request.sid} 加入游戏 {game_id}")

@socketio.on('leave_game')
def handle_leave_game(data):
    """离开游戏房间"""
    game_id = data.get('game_id')
    if game_id and game_id in game_rooms:
        if request.sid in game_rooms[game_id]:
            game_rooms[game_id].remove(request.sid)
        leave_room(game_id)
        emit('left_game', {'game_id': game_id})

@socketio.on('message')
def handle_message(data):
    """处理通用消息"""
    print(f"收到消息: {data}")
    emit('message_received', data)

if __name__ == '__main__':
    print("启动狼人杀AI游戏API服务器...")
    print("API地址: http://localhost:8000")
    print("WebSocket地址: ws://localhost:8000")

    # 启动服务器
    socketio.run(
        app,
        host='0.0.0.0',
        port=8000,
        debug=True,
        allow_unsafe_werkzeug=True
    )