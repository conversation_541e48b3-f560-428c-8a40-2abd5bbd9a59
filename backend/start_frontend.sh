#!/bin/bash

# 狼人杀AI游戏前端启动脚本

echo "🐺 狼人杀AI游戏 - 前端启动"
echo "=================================================="

# 检查Node.js
if ! command -v node &> /dev/null; then
    echo "❌ 错误: 未找到Node.js，请先安装Node.js 16+"
    exit 1
fi

echo "✅ Node.js版本: $(node --version)"

# 检查npm
if ! command -v npm &> /dev/null; then
    echo "❌ 错误: 未找到npm"
    exit 1
fi

echo "✅ npm版本: $(npm --version)"

# 检查前端目录
if [ ! -d "frontend/src" ]; then
    echo "❌ 错误: 请在项目根目录运行此脚本"
    exit 1
fi

# 进入前端目录
cd frontend

# 检查是否已安装依赖
if [ ! -d "node_modules" ]; then
    echo "📦 安装前端依赖..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ 依赖安装失败"
        exit 1
    fi
fi

echo "✅ 依赖检查通过"

# 设置环境变量
export REACT_APP_API_URL=http://localhost:8000/api
export REACT_APP_WEBSOCKET_URL=http://localhost:8000

echo ""
echo "🚀 启动前端开发服务器..."
echo "🌐 前端地址: http://localhost:3000"
echo "📡 API地址: http://localhost:8000"
echo ""
echo "请确保后端服务器已启动 (运行 python start_backend.py)"
echo "按 Ctrl+C 停止服务器"
echo "--------------------------------------------------"

# 启动前端服务器
npm start