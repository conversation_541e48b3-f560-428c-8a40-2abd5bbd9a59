#!/usr/bin/env python3
"""
Qwen3-30B配置检查脚本
验证Qwen3-30B服务配置是否正确
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.config.llm_config import get_service_status, llm_config_manager


def check_service_connection():
    """检查服务连接"""
    print("检查Qwen3-30B服务连接...")

    config = llm_config_manager.get_config("qwen3_30b")
    base_url = config.base_url

    print(f"配置的服务地址: {base_url}")
    print(f"配置的模型名称: {config.model_name}")

    # 使用内置的服务状态检查
    status = get_service_status()

    if status['qwen3_30b']['available']:
        print(f"✅ Qwen3-30B服务可用")
        return True
    else:
        print(f"❌ Qwen3-30B服务不可用")
        if status['qwen3_30b']['error']:
            print(f"错误: {status['qwen3_30b']['error']}")
        print("请确保Qwen3-30B服务正在运行在指定地址")
        return False


def test_text_generation():
    """测试文本生成"""
    print("\n测试文本生成...")

    try:
        from src.ai.llm_integration import create_llm_manager

        # 创建LLM管理器
        llm_manager = create_llm_manager("qwen3_30b")

        # 测试简单文本生成
        test_prompt = "请说'测试成功'"
        print(f"测试提示: {test_prompt}")

        response = llm_manager.provider.generate_text_sync(test_prompt)

        if response and len(response) > 0:
            print(f"✅ 文本生成成功: {response}")
            return True
        else:
            print(f"❌ 文本生成失败: 响应为空")
            return False

    except Exception as e:
        print(f"❌ 文本生成测试失败: {e}")
        return False


def check_configuration():
    """检查配置"""
    print("检查Qwen3-30B配置...")
    
    config = llm_config_manager.get_config("qwen3_30b")
    
    print(f"服务地址: {config.base_url}")
    print(f"模型名称: {config.model_name}")
    print(f"API密钥: {config.api_key}")
    print(f"最大令牌数: {config.max_tokens}")
    print(f"温度参数: {config.temperature}")
    
    # 验证配置
    validation = llm_config_manager.validate_config("qwen3_30b")
    
    if validation['valid']:
        print("✅ 配置验证通过")
        if validation['warnings']:
            for warning in validation['warnings']:
                print(f"⚠️ {warning}")
    else:
        print(f"❌ 配置验证失败: {validation['error']}")
    
    return validation['valid']


def main():
    """主函数"""
    print("Qwen3-30B配置检查工具")
    print("=" * 50)
    
    # 检查配置
    config_ok = check_configuration()
    
    if not config_ok:
        print("\n配置检查失败，请修正配置后重试")
        return
    
    print("\n" + "-" * 50)
    
    # 检查服务连接
    connection_ok = check_service_connection()
    
    if not connection_ok:
        print("\n服务连接失败，请检查:")
        print("1. Qwen3-30B服务是否正在运行")
        print("2. 服务地址是否正确 (http://localhost:8005/v1)")
        print("3. 网络连接是否正常")
        return
    
    print("\n" + "-" * 50)
    
    # 测试文本生成
    generation_ok = test_text_generation()
    
    print("\n" + "=" * 50)
    
    if config_ok and connection_ok and generation_ok:
        print("🎉 所有检查通过!")
        print("Qwen3-30B已正确配置并可以使用")
        print("\n可以运行以下命令开始使用:")
        print("  python test_qwen3_integration.py")
        print("  python demo_qwen3_game.py")
    else:
        print("❌ 检查未完全通过")
        print("请根据上述错误信息修正配置")
    
    print("=" * 50)


if __name__ == "__main__":
    main()
