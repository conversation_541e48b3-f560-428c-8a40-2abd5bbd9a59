#!/usr/bin/env python3
"""
Phase 2 功能演示脚本
展示完善的游戏逻辑、历史记录和配置系统
"""

import sys
import os
import time

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.models.enums import Role, GamePhase, GameResult, VoteType
from src.models.game_state import GameConfig
from src.engine.game_engine import GameEngine
from src.players.ai_player import AIPlayer
from src.config.game_modes import GameModeManager


def print_separator(char='-', length=60):
    """打印分隔线"""
    print(char * length)


def print_header(title):
    """打印标题"""
    print_separator('=')
    print(f" {title} ".center(60, '='))
    print_separator('=')


def demo_game_modes():
    """演示游戏模式系统"""
    print_header("Phase 2 演示：游戏模式系统")
    
    mode_manager = GameModeManager()
    
    print("可用游戏模式:")
    for mode_name in mode_manager.get_available_modes():
        mode_info = mode_manager.get_mode_info(mode_name)
        print(f"\n{mode_info['name']} ({mode_name})")
        print(f"  描述: {mode_info['description']}")
        print(f"  玩家数: {mode_info['player_range']} (推荐: {mode_info['recommended_players']})")
        print(f"  难度: {mode_info['difficulty']}")
        print(f"  角色分配: {mode_info['role_distribution']}")
    
    # 演示为特定玩家数创建配置
    print(f"\n为8人局推荐的模式:")
    suitable_modes = mode_manager.get_modes_for_player_count(8)
    for mode_name in suitable_modes:
        mode = mode_manager.get_mode(mode_name)
        config = mode.create_config_for_players(8)
        print(f"  {mode.name}: {dict((role.name, count) for role, count in config.role_distribution.items())}")
    
    return mode_manager.get_mode("classic").create_config_for_players(8)


def demo_enhanced_game_engine(config):
    """演示增强的游戏引擎"""
    print_header("Phase 2 演示：增强游戏引擎")
    
    # 创建游戏
    engine = GameEngine(config)
    player_names = ["Alice", "Bob", "Charlie", "David", "Eve", "Frank", "Grace", "Henry"]
    game_state = engine.create_game("phase2_demo", player_names)
    
    print("游戏创建成功!")
    print(f"游戏ID: {game_state.game_id}")
    
    # 显示角色分配
    print("\n角色分配:")
    for player_id, player in game_state.players.items():
        print(f"  {player.name}: {player.role.name} ({player.faction.name})")
    
    # 创建AI玩家
    ai_players = {}
    for player_id, player_info in game_state.players.items():
        ai_player = AIPlayer(player_id, player_info.name)
        ai_player.set_role(player_info.role)
        ai_players[player_id] = ai_player
    
    return engine, game_state, ai_players


def demo_detailed_night_phase(engine, game_state, ai_players):
    """演示详细的夜晚阶段"""
    print_header("Phase 2 演示：详细夜晚阶段")
    
    # 开始夜晚阶段
    engine.start_game()
    night_result = engine.start_night_phase()
    print(f"夜晚阶段开始: {night_result}")
    
    night_manager = engine.get_night_phase_manager()
    
    # 狼人行动
    werewolves = game_state.get_werewolves()
    if werewolves:
        print(f"\n狼人行动 ({len(werewolves)}个狼人):")
        werewolf_votes = {}
        for werewolf in werewolves:
            ai_werewolf = ai_players[werewolf.player_id]
            target = ai_werewolf.make_vote_decision(game_state, VoteType.WEREWOLF_KILL)
            if target:
                werewolf_votes[werewolf.player_id] = target
                print(f"  {werewolf.name} 投票杀死 {game_state.players[target].name}")
        
        werewolf_result = engine.submit_werewolf_action(werewolf_votes)
        print(f"  狼人行动结果: {werewolf_result}")
    
    # 预言家行动
    seers = game_state.get_players_by_role(Role.SEER)
    if seers:
        seer = seers[0]
        ai_seer = ai_players[seer.player_id]
        target = ai_seer.make_special_action_decision(game_state, "seer_check")
        if target:
            seer_result = engine.submit_seer_action(seer.player_id, target)
            print(f"\n预言家行动:")
            print(f"  {seer.name} 查验 {game_state.players[target].name}: {seer_result}")
    
    # 女巫行动
    witches = game_state.get_players_by_role(Role.WITCH)
    if witches:
        witch = witches[0]
        witch_info = night_manager.get_role_action_info(Role.WITCH)
        print(f"\n女巫行动选项:")
        print(f"  解药可用: {witch_info.get('antidote_available', False)}")
        print(f"  毒药可用: {witch_info.get('poison_available', False)}")
        print(f"  狼人目标: {witch_info.get('werewolf_target_name', 'None')}")
        
        # 简单AI决策：如果有人被杀且有解药，就救人
        save_target = None
        poison_target = None
        
        if witch_info.get('antidote_available') and witch_info.get('werewolf_target'):
            save_target = witch_info['werewolf_target']
        
        if witch_info.get('poison_available'):
            ai_witch = ai_players[witch.player_id]
            poison_target = ai_witch.make_special_action_decision(game_state, "witch_poison")
        
        witch_result = engine.submit_witch_action(witch.player_id, save_target, poison_target)
        print(f"  女巫行动结果: {witch_result}")
    
    # 守卫行动
    guards = game_state.get_players_by_role(Role.GUARD)
    if guards:
        guard = guards[0]
        ai_guard = ai_players[guard.player_id]
        target = ai_guard.make_special_action_decision(game_state, "guard_protect")
        if target:
            guard_result = engine.submit_guard_action(guard.player_id, target)
            print(f"\n守卫行动:")
            print(f"  {guard.name} 保护 {game_state.players[target].name}: {guard_result}")
    
    # 解决夜晚行动
    print(f"\n夜晚状态: {night_manager.get_night_status()}")
    
    if night_manager.is_night_complete():
        night_resolution = engine.resolve_night_actions()
        print(f"\n夜晚结果: {night_resolution}")
        
        if night_resolution.get('deaths'):
            for death in night_resolution['deaths']:
                print(f"  {death['player_name']} ({death['role'].name}) 死于 {death['cause']}")
    
    return night_resolution


def demo_detailed_day_phase(engine, game_state, ai_players):
    """演示详细的白天阶段"""
    print_header("Phase 2 演示：详细白天阶段")
    
    # 开始讨论阶段
    discussion_result = engine.start_day_discussion()
    print(f"讨论阶段开始: {discussion_result}")
    
    # AI玩家发言
    print(f"\n玩家发言:")
    alive_players = game_state.get_alive_players()
    for player in alive_players[:4]:  # 只显示前4个玩家的发言
        ai_player = ai_players[player.player_id]
        speech = ai_player.generate_speech(game_state, GamePhase.DAY_DISCUSSION)
        engine.add_player_speech(player.player_id, speech)
        print(f"  {player.name}: {speech}")
    
    day_manager = engine.get_day_phase_manager()
    discussion_summary = day_manager.get_discussion_summary()
    print(f"\n讨论总结: {discussion_summary['total_speeches']}条发言")
    
    # 开始投票阶段
    voting_result = engine.start_day_voting()
    print(f"\n投票阶段开始: {voting_result}")
    
    # AI玩家投票
    print(f"\n玩家投票:")
    for player in alive_players:
        ai_player = ai_players[player.player_id]
        target = ai_player.make_vote_decision(game_state, VoteType.ELIMINATION)
        if target:
            vote_result = engine.cast_elimination_vote(player.player_id, target, "AI决策")
            if vote_result.get('success'):
                print(f"  {player.name} 投票给 {game_state.players[target].name}")
    
    voting_summary = day_manager.get_voting_summary()
    print(f"\n投票统计:")
    for player_id, votes in voting_summary['vote_counts'].items():
        player_name = game_state.players[player_id].name
        print(f"  {player_name}: {votes}票")
    
    # 解决投票
    voting_resolution = engine.resolve_day_voting()
    print(f"\n投票结果: {voting_resolution}")
    
    if voting_resolution.get('eliminated_player'):
        eliminated = voting_resolution['eliminated_player']
        print(f"  {eliminated['name']} ({eliminated['role'].name}) 被淘汰")
    
    return voting_resolution


def demo_victory_conditions(engine):
    """演示胜负条件检查"""
    print_header("Phase 2 演示：胜负条件检查")
    
    victory_result = engine.check_victory_conditions_detailed()
    print(f"胜负检查结果: {victory_result}")
    
    victory_prob = engine.get_victory_probability()
    print(f"胜利概率: 村民 {victory_prob['villagers']:.2%}, 狼人 {victory_prob['werewolves']:.2%}")
    
    balance_info = engine.get_game_balance_info()
    print(f"游戏平衡信息:")
    print(f"  阵营人数: {balance_info.get('faction_counts', {})}")
    print(f"  平衡分数: {balance_info.get('balance_score', 0):.2f}")
    print(f"  是否平衡: {balance_info.get('is_balanced', False)}")
    
    return victory_result


def demo_game_history(engine):
    """演示游戏历史记录"""
    print_header("Phase 2 演示：游戏历史记录")
    
    history_manager = engine.get_history_manager()
    if not history_manager:
        print("历史管理器未初始化")
        return
    
    # 获取游戏总结
    summary = engine.get_game_summary()
    print(f"游戏总结:")
    print(f"  游戏ID: {summary['game_info']['game_id']}")
    print(f"  总回合数: {summary['game_info']['total_rounds']}")
    print(f"  总事件数: {summary['game_info']['total_events']}")
    print(f"  游戏结果: {summary['game_info']['result']}")
    
    print(f"\n事件统计:")
    event_summary = summary['event_summary']
    print(f"  死亡事件: {event_summary['deaths']}")
    print(f"  投票事件: {event_summary['votes']}")
    print(f"  行动事件: {event_summary['actions']}")
    
    print(f"\n玩家统计:")
    for player_id, stats in summary['player_stats'].items():
        print(f"  {stats['name']} ({stats['role']}): {stats['total_events']}个事件, 存活: {stats['survived']}")
    
    # 导出历史记录
    filename = engine.export_game_history()
    print(f"\n游戏历史已导出到: {filename}")


def main():
    """主演示函数"""
    print("狼人杀AI游戏 - Phase 2 功能演示")
    print("=" * 60)
    
    try:
        # 演示游戏模式
        config = demo_game_modes()
        input("\n按回车键继续...")
        
        # 演示增强游戏引擎
        engine, game_state, ai_players = demo_enhanced_game_engine(config)
        input("\n按回车键继续...")
        
        # 演示夜晚阶段
        night_result = demo_detailed_night_phase(engine, game_state, ai_players)
        input("\n按回车键继续...")
        
        # 演示白天阶段
        day_result = demo_detailed_day_phase(engine, game_state, ai_players)
        input("\n按回车键继续...")
        
        # 演示胜负条件
        victory_result = demo_victory_conditions(engine)
        input("\n按回车键继续...")
        
        # 演示游戏历史
        demo_game_history(engine)
        
        print("\n" + "=" * 60)
        print("Phase 2 演示完成!")
        print("新增功能:")
        print("✅ 详细的白天和夜晚阶段逻辑")
        print("✅ 完善的胜负条件判定")
        print("✅ 游戏历史记录和事件日志")
        print("✅ 高级游戏配置系统")
        print("✅ 多种游戏模式支持")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n演示出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
