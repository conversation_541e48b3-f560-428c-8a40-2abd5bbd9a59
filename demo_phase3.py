#!/usr/bin/env python3
"""
Phase 3 功能演示脚本
展示高级AI策略系统的所有功能
"""

import sys
import os
import time

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.models.enums import Role, GamePhase, GameResult, VoteType
from src.models.game_state import GameConfig
from src.engine.game_engine import GameEngine
from src.ai.ai_strategy_manager import AIStrategyManager
from src.ai.ai_personality import DifficultyLevel, PersonalityType
from src.config.game_modes import GameModeManager


def print_separator(char='-', length=60):
    """打印分隔线"""
    print(char * length)


def print_header(title):
    """打印标题"""
    print_separator('=')
    print(f" {title} ".center(60, '='))
    print_separator('=')


def demo_ai_personality_system():
    """演示AI个性系统"""
    print_header("Phase 3 演示：AI个性系统")
    
    ai_manager = AIStrategyManager()
    
    print("创建不同个性的AI玩家:")
    
    # 创建不同个性类型的AI
    personalities = [
        (PersonalityType.AGGRESSIVE, "攻击型Alice"),
        (PersonalityType.DEFENSIVE, "防守型Bob"),
        (PersonalityType.ANALYTICAL, "分析型Charlie"),
        (PersonalityType.SOCIAL, "社交型Diana"),
        (PersonalityType.DECEPTIVE, "欺骗型Eve")
    ]
    
    ai_players = []
    for i, (personality_type, name) in enumerate(personalities):
        ai_player = ai_manager.create_ai_player(
            player_id=i + 1,
            name=name,
            difficulty=DifficultyLevel.NORMAL,
            personality_type=personality_type,
            use_llm=True,  # 使用Qwen3-30B模型
            llm_config="qwen3_30b"
        )
        ai_players.append(ai_player)
        
        # 显示AI状态
        status = ai_player.get_ai_status()
        print(f"\n{name}:")
        print(f"  个性类型: {status['personality_type']}")
        print(f"  难度等级: {status['difficulty']}")
        print(f"  关键特征:")
        traits = status['traits']
        print(f"    攻击性: {traits['aggressiveness']:.2f}")
        print(f"    谨慎性: {traits['cautiousness']:.2f}")
        print(f"    分析能力: {traits['analytical_thinking']:.2f}")
        print(f"    社交技能: {traits['social_skills']:.2f}")
        print(f"    自信心: {traits['confidence']:.2f}")
    
    return ai_manager, ai_players


def demo_difficulty_levels():
    """演示难度等级"""
    print_header("Phase 3 演示：AI难度等级")
    
    ai_manager = AIStrategyManager()
    
    print("创建不同难度的AI玩家:")
    
    difficulties = [
        (DifficultyLevel.BEGINNER, "新手Frank"),
        (DifficultyLevel.EASY, "简单Grace"),
        (DifficultyLevel.NORMAL, "普通Henry"),
        (DifficultyLevel.HARD, "困难Ivy"),
        (DifficultyLevel.EXPERT, "专家Jack")
    ]
    
    for i, (difficulty, name) in enumerate(difficulties):
        ai_player = ai_manager.create_ai_player(
            player_id=i + 10,
            name=name,
            difficulty=difficulty,
            personality_type=PersonalityType.ANALYTICAL
        )
        
        status = ai_player.get_ai_status()
        print(f"\n{name}:")
        print(f"  难度等级: {status['difficulty']}")
        print(f"  投票信心: {status['behavior_params']['vote_confidence']:.2f}")
        print(f"  发言频率: {status['behavior_params']['speech_frequency']:.2f}")
        print(f"  信息分享: {status['behavior_params']['information_sharing']:.2f}")


def demo_role_specific_ai(ai_manager, ai_players):
    """演示角色专用AI"""
    print_header("Phase 3 演示：角色专用AI")
    
    # 创建游戏来分配角色
    from src.models.game_state import GameConfig
    config = GameConfig(
        total_players=5,
        role_distribution={Role.VILLAGER: 2, Role.WEREWOLF: 2, Role.SEER: 1}
    )
    
    engine = GameEngine(config)
    player_names = [ai.name for ai in ai_players[:5]]
    game_state = engine.create_game("phase3_demo", player_names)
    
    # 为AI玩家分配角色
    for player_id, player_info in game_state.players.items():
        if player_id <= len(ai_players):
            ai_player = ai_players[player_id - 1]
            ai_player.set_role(player_info.role)
            
            print(f"\n{ai_player.name} 被分配为 {player_info.role.name}")
            
            # 显示角色专用信息
            status = ai_player.get_ai_status()
            if 'role_specific_info' in status:
                print(f"  角色专用信息: {status['role_specific_info']}")
    
    return engine, game_state


def demo_ai_decision_making(engine, game_state, ai_players):
    """演示AI决策制定"""
    print_header("Phase 3 演示：AI决策制定")
    
    engine.start_game()
    
    print("演示不同角色的AI决策:")
    
    # 夜晚阶段决策
    engine.start_night_phase()
    print(f"\n=== 夜晚阶段决策 ===")
    
    for player_id, player_info in game_state.players.items():
        if not player_info.is_alive() or player_id > len(ai_players):
            continue
            
        ai_player = ai_players[player_id - 1]
        
        if player_info.role == Role.WEREWOLF:
            target = ai_player.make_vote_decision(game_state, VoteType.WEREWOLF_KILL)
            if target:
                target_name = game_state.players[target].name
                print(f"  狼人 {ai_player.name} 选择杀死 {target_name}")
        
        elif player_info.role == Role.SEER:
            target = ai_player.make_special_action_decision(game_state, "seer_check")
            if target:
                target_name = game_state.players[target].name
                print(f"  预言家 {ai_player.name} 选择查验 {target_name}")
        
        elif player_info.role == Role.GUARD:
            target = ai_player.make_special_action_decision(game_state, "guard_protect")
            if target:
                target_name = game_state.players[target].name
                print(f"  守卫 {ai_player.name} 选择保护 {target_name}")
    
    # 白天阶段决策
    engine.start_day_discussion()
    print(f"\n=== 白天讨论阶段 ===")
    
    for player_id, player_info in game_state.players.items():
        if not player_info.is_alive() or player_id > len(ai_players):
            continue
            
        ai_player = ai_players[player_id - 1]
        speech = ai_player.generate_speech(game_state, GamePhase.DAY_DISCUSSION)
        
        if speech:  # 只显示非空发言
            print(f"  {ai_player.name}: {speech}")
    
    # 投票阶段决策
    engine.start_day_voting()
    print(f"\n=== 投票阶段决策 ===")
    
    for player_id, player_info in game_state.players.items():
        if not player_info.is_alive() or player_id > len(ai_players):
            continue
            
        ai_player = ai_players[player_id - 1]
        target = ai_player.make_vote_decision(game_state, VoteType.ELIMINATION)
        
        if target:
            target_name = game_state.players[target].name
            print(f"  {ai_player.name} 投票给 {target_name}")
        else:
            print(f"  {ai_player.name} 弃权")


def demo_llm_integration():
    """演示大模型集成"""
    print_header("Phase 3 演示：大模型集成")
    
    from src.ai.llm_integration import create_llm_manager
    
    print("测试大模型集成功能:")
    
    # 创建Qwen3-30B LLM管理器
    llm_manager = create_llm_manager("qwen3_30b")
    
    # 创建测试游戏状态
    config = GameConfig(
        total_players=4,
        role_distribution={Role.VILLAGER: 2, Role.WEREWOLF: 1, Role.SEER: 1}
    )
    
    engine = GameEngine(config)
    game_state = engine.create_game("llm_test", ["Alice", "Bob", "Charlie", "David"])
    
    print(f"\n测试不同角色的LLM生成发言:")
    
    roles_to_test = [Role.VILLAGER, Role.WEREWOLF, Role.SEER]
    
    for role in roles_to_test:
        context = {"player_id": 1}
        speech = llm_manager.generate_speech(role, game_state, context)
        print(f"  {role.name}: {speech}")
    
    print(f"\n测试策略分析:")
    possible_actions = ["vote_player_2", "vote_player_3", "abstain"]
    analysis = llm_manager.generate_strategy_analysis(Role.VILLAGER, game_state, possible_actions)
    print(f"  推荐行动: {analysis.get('recommended_action', 'unknown')}")
    print(f"  信心度: {analysis.get('confidence', 0):.2f}")
    print(f"  推理: {analysis.get('reasoning', 'no reasoning')}")


def demo_team_analysis(ai_manager):
    """演示团队分析"""
    print_header("Phase 3 演示：团队分析")
    
    # 创建平衡团队
    player_names = ["Alpha", "Beta", "Gamma", "Delta", "Epsilon", "Zeta"]
    balanced_team = ai_manager.create_balanced_team(
        player_names, 
        difficulty=DifficultyLevel.NORMAL
    )
    
    print("创建平衡团队:")
    for ai_player in balanced_team:
        status = ai_player.get_ai_status()
        print(f"  {ai_player.name}: {status['personality_type']} ({status['difficulty']})")
    
    # 分析团队组成
    analysis = ai_manager.get_team_analysis()
    
    print(f"\n团队分析结果:")
    print(f"  个性分布: {analysis['personality_distribution']}")
    print(f"  难度分布: {analysis['difficulty_distribution']}")
    print(f"  团队平衡分数: {analysis['team_balance_score']:.2f}")
    
    print(f"\n平均特征:")
    avg_traits = analysis['average_traits']
    key_traits = ['aggressiveness', 'cautiousness', 'analytical_thinking', 'social_skills']
    for trait in key_traits:
        if trait in avg_traits:
            print(f"  {trait}: {avg_traits[trait]:.2f}")


def demo_adaptive_learning():
    """演示适应性学习"""
    print_header("Phase 3 演示：适应性学习")
    
    ai_manager = AIStrategyManager()
    
    # 创建启用学习的AI
    ai_player = ai_manager.create_ai_player(
        player_id=1,
        name="学习型AI",
        difficulty=DifficultyLevel.NORMAL,
        personality_type=PersonalityType.ANALYTICAL
    )
    
    # 启用学习
    ai_player.ai_config.learning_enabled = True
    ai_player.ai_config.adaptation_rate = 0.2
    
    print("初始AI状态:")
    initial_status = ai_player.get_ai_status()
    initial_traits = initial_status['traits']
    print(f"  谨慎性: {initial_traits['cautiousness']:.2f}")
    print(f"  自信心: {initial_traits['confidence']:.2f}")
    print(f"  分析能力: {initial_traits['analytical_thinking']:.2f}")
    
    # 模拟游戏事件
    print(f"\n模拟游戏事件和学习过程:")
    
    # 模拟被淘汰事件
    ai_player.process_game_event("player_eliminated", {"player_id": 1})
    print("  处理被淘汰事件 -> 增加谨慎性")
    
    # 模拟胜利事件
    from src.models.enums import GameResult
    ai_player.set_role(Role.VILLAGER)  # 设置为村民
    ai_player.process_game_event("game_ended", {"result": GameResult.VILLAGERS_WIN})
    print("  处理胜利事件 -> 增加自信心")
    
    # 模拟失败事件
    ai_player.process_game_event("game_ended", {"result": GameResult.WEREWOLVES_WIN})
    print("  处理失败事件 -> 增加分析能力")
    
    print(f"\n学习后AI状态:")
    final_status = ai_player.get_ai_status()
    final_traits = final_status['traits']
    print(f"  谨慎性: {final_traits['cautiousness']:.2f} (变化: {final_traits['cautiousness'] - initial_traits['cautiousness']:+.2f})")
    print(f"  自信心: {final_traits['confidence']:.2f} (变化: {final_traits['confidence'] - initial_traits['confidence']:+.2f})")
    print(f"  分析能力: {final_traits['analytical_thinking']:.2f} (变化: {final_traits['analytical_thinking'] - initial_traits['analytical_thinking']:+.2f})")


def main():
    """主演示函数"""
    print("狼人杀AI游戏 - Phase 3 AI策略系统演示")
    print("=" * 60)
    
    try:
        # 演示AI个性系统
        ai_manager, ai_players = demo_ai_personality_system()
        input("\n按回车键继续...")
        
        # 演示难度等级
        demo_difficulty_levels()
        input("\n按回车键继续...")
        
        # 演示角色专用AI
        engine, game_state = demo_role_specific_ai(ai_manager, ai_players)
        input("\n按回车键继续...")
        
        # 演示AI决策制定
        demo_ai_decision_making(engine, game_state, ai_players)
        input("\n按回车键继续...")
        
        # 演示大模型集成
        demo_llm_integration()
        input("\n按回车键继续...")
        
        # 演示团队分析
        demo_team_analysis(ai_manager)
        input("\n按回车键继续...")
        
        # 演示适应性学习
        demo_adaptive_learning()
        
        print("\n" + "=" * 60)
        print("Phase 3 演示完成!")
        print("新增功能:")
        print("✅ 大模型集成组件")
        print("✅ 基础村民AI策略")
        print("✅ 狼人AI伪装和配合")
        print("✅ 预言家AI查验和领导")
        print("✅ 特殊角色AI策略")
        print("✅ AI难度等级系统")
        print("✅ 个性化参数系统")
        print("✅ 适应性学习机制")
        print("✅ 统一AI策略管理")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n演示出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
