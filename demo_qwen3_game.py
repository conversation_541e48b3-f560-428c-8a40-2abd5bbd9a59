#!/usr/bin/env python3
"""
使用Qwen3-30B的完整狼人杀游戏演示
展示AI玩家使用大模型进行智能决策和发言
"""

import sys
import os
import time

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.models.enums import Role, GamePhase, VoteType
from src.models.game_state import GameConfig
from src.engine.game_engine import GameEngine
from src.ai.ai_strategy_manager import AIStrategyManager
from src.ai.ai_personality import DifficultyLevel, PersonalityType
from src.config.llm_config import get_service_status


def print_separator(char='-', length=80):
    """打印分隔线"""
    print(char * length)


def print_header(title):
    """打印标题"""
    print_separator('=')
    print(f" {title} ".center(80, '='))
    print_separator('=')


def check_qwen3_service():
    """检查Qwen3-30B服务状态"""
    print("检查Qwen3-30B服务状态...")
    status = get_service_status()
    
    if status['qwen3_30b']['available']:
        print("✅ Qwen3-30B服务可用")
        return True
    else:
        print("❌ Qwen3-30B服务不可用")
        print(f"错误: {status['qwen3_30b']['error']}")
        print("\n请确保Qwen3-30B服务正在运行在 http://localhost:8005/v1")
        return False


def create_ai_players():
    """创建AI玩家团队"""
    print_header("创建AI玩家团队")
    
    ai_manager = AIStrategyManager()
    
    # 创建不同个性和难度的AI玩家
    player_configs = [
        ("Alice", PersonalityType.ANALYTICAL, DifficultyLevel.EXPERT),
        ("Bob", PersonalityType.AGGRESSIVE, DifficultyLevel.HARD),
        ("Charlie", PersonalityType.SOCIAL, DifficultyLevel.NORMAL),
        ("Diana", PersonalityType.DEFENSIVE, DifficultyLevel.HARD),
        ("Eve", PersonalityType.DECEPTIVE, DifficultyLevel.EXPERT),
        ("Frank", PersonalityType.LOYAL, DifficultyLevel.NORMAL)
    ]
    
    ai_players = []
    for name, personality, difficulty in player_configs:
        ai_player = ai_manager.create_ai_player(
            player_id=len(ai_players) + 1,
            name=name,
            difficulty=difficulty,
            personality_type=personality,
            use_llm=True,
            llm_config="qwen3_30b"
        )
        ai_players.append(ai_player)
        
        print(f"创建AI玩家: {name} ({personality.value}, {difficulty.value})")
    
    return ai_manager, ai_players


def setup_game(ai_players):
    """设置游戏"""
    print_header("设置游戏")
    
    # 创建游戏配置
    config = GameConfig(
        total_players=6,
        role_distribution={
            Role.VILLAGER: 2,
            Role.WEREWOLF: 2,
            Role.SEER: 1,
            Role.WITCH: 1
        }
    )
    
    # 创建游戏引擎
    engine = GameEngine(config)
    player_names = [ai.name for ai in ai_players]
    game_state = engine.create_game("qwen3_demo", player_names)
    
    # 为AI玩家分配角色
    print("\n角色分配:")
    for player_id, player_info in game_state.players.items():
        if player_id <= len(ai_players):
            ai_player = ai_players[player_id - 1]
            ai_player.set_role(player_info.role)
            print(f"  {ai_player.name}: {player_info.role.name}")
    
    return engine, game_state


def simulate_night_phase(engine, game_state, ai_players):
    """模拟夜晚阶段"""
    print_header("夜晚阶段")
    
    # 开始夜晚阶段
    engine.start_night_phase()
    print("夜幕降临，特殊角色开始行动...")
    
    # 狼人行动
    werewolves = game_state.get_werewolves()
    if werewolves:
        print(f"\n🐺 狼人行动 ({len(werewolves)}个狼人):")
        werewolf_votes = {}
        
        for werewolf in werewolves:
            ai_werewolf = next(ai for ai in ai_players if ai.player_id == werewolf.player_id)
            target = ai_werewolf.make_vote_decision(game_state, VoteType.WEREWOLF_KILL)
            
            if target:
                werewolf_votes[werewolf.player_id] = target
                target_name = game_state.players[target].name
                print(f"  {werewolf.name} 选择杀死 {target_name}")
        
        if werewolf_votes:
            result = engine.submit_werewolf_action(werewolf_votes)
            if result.get('success'):
                target_name = game_state.players[result['target_id']].name
                print(f"  狼人决定杀死 {target_name}")
    
    # 预言家行动
    seers = game_state.get_players_by_role(Role.SEER)
    if seers:
        seer = seers[0]
        ai_seer = next(ai for ai in ai_players if ai.player_id == seer.player_id)
        target = ai_seer.make_special_action_decision(game_state, "seer_check")
        
        if target:
            result = engine.submit_seer_action(seer.player_id, target)
            target_name = game_state.players[target].name
            is_werewolf = result.get('is_werewolf', False)
            print(f"\n🔮 预言家 {seer.name} 查验 {target_name}: {'狼人' if is_werewolf else '好人'}")
    
    # 女巫行动
    witches = game_state.get_players_by_role(Role.WITCH)
    if witches:
        witch = witches[0]
        ai_witch = next(ai for ai in ai_players if ai.player_id == witch.player_id)
        
        # 简化女巫决策
        save_target = None
        poison_target = None
        
        result = engine.submit_witch_action(witch.player_id, save_target, poison_target)
        if result.get('success'):
            actions = result.get('actions', [])
            if actions:
                print(f"\n🧙 女巫 {witch.name}: {', '.join(actions)}")
            else:
                print(f"\n🧙 女巫 {witch.name} 选择不使用药剂")
    
    # 解决夜晚行动
    night_manager = engine.get_night_phase_manager()
    if night_manager.is_night_complete():
        resolution = engine.resolve_night_actions()
        
        if resolution.get('success'):
            deaths = resolution.get('deaths', [])
            if deaths:
                print(f"\n💀 夜晚死亡:")
                for death in deaths:
                    print(f"  {death['player_name']} ({death['role'].name}) - {death['cause']}")
            else:
                print(f"\n🌅 夜晚平安无事")


def simulate_day_phase(engine, game_state, ai_players):
    """模拟白天阶段"""
    print_header("白天阶段")
    
    # 开始讨论阶段
    engine.start_day_discussion()
    print("天亮了，玩家开始讨论...")
    
    # AI玩家发言
    print(f"\n💬 玩家发言:")
    alive_players = game_state.get_alive_players()
    
    for player in alive_players:
        ai_player = next((ai for ai in ai_players if ai.player_id == player.player_id), None)
        if ai_player:
            print(f"\n{ai_player.name} 思考中...")
            time.sleep(1)  # 模拟思考时间
            
            speech = ai_player.generate_speech(game_state, GamePhase.DAY_DISCUSSION)
            if speech:
                print(f"  {ai_player.name}: {speech}")
                engine.add_player_speech(ai_player.player_id, speech)
    
    # 开始投票阶段
    print(f"\n🗳️ 投票阶段:")
    engine.start_day_voting()
    
    vote_results = {}
    for player in alive_players:
        ai_player = next((ai for ai in ai_players if ai.player_id == player.player_id), None)
        if ai_player:
            target = ai_player.make_vote_decision(game_state, VoteType.ELIMINATION)
            
            if target:
                target_name = game_state.players[target].name
                vote_results[ai_player.player_id] = target
                print(f"  {ai_player.name} 投票给 {target_name}")
                engine.cast_elimination_vote(ai_player.player_id, target, "AI决策")
            else:
                print(f"  {ai_player.name} 弃权")
    
    # 解决投票
    voting_result = engine.resolve_day_voting()
    if voting_result.get('success'):
        if voting_result.get('eliminated_player'):
            eliminated = voting_result['eliminated_player']
            print(f"\n⚖️ 投票结果: {eliminated['name']} ({eliminated['role'].name}) 被淘汰")
        else:
            print(f"\n⚖️ 投票结果: 平票，无人被淘汰")


def check_game_end(engine):
    """检查游戏是否结束"""
    victory_result = engine.check_victory_conditions_detailed()
    
    if victory_result['result'].name != 'ONGOING':
        print_header("游戏结束")
        print(f"🏆 {victory_result['message']}")
        print(f"原因: {victory_result['reason']}")
        
        # 显示最终状态
        game_state = engine.game_state
        print(f"\n最终存活玩家:")
        for player in game_state.get_alive_players():
            print(f"  {player.name} ({player.role.name})")
        
        return True
    
    return False


def main():
    """主演示函数"""
    print("狼人杀AI游戏 - Qwen3-30B智能AI演示")
    print("=" * 80)
    
    try:
        # 检查Qwen3-30B服务
        if not check_qwen3_service():
            return
        
        # 创建AI玩家
        ai_manager, ai_players = create_ai_players()
        input("\n按回车键继续...")
        
        # 设置游戏
        engine, game_state = setup_game(ai_players)
        engine.start_game()
        input("\n按回车键开始游戏...")
        
        # 游戏主循环
        round_count = 0
        max_rounds = 5  # 最多5轮
        
        while round_count < max_rounds:
            round_count += 1
            print(f"\n{'='*20} 第 {round_count} 轮 {'='*20}")
            
            # 夜晚阶段
            simulate_night_phase(engine, game_state, ai_players)
            
            # 检查游戏是否结束
            if check_game_end(engine):
                break
            
            input("\n按回车键进入白天...")
            
            # 白天阶段
            simulate_day_phase(engine, game_state, ai_players)
            
            # 检查游戏是否结束
            if check_game_end(engine):
                break
            
            input("\n按回车键进入下一轮...")
        
        if round_count >= max_rounds:
            print(f"\n游戏达到最大轮数 ({max_rounds})，演示结束")
        
        # 显示游戏统计
        print_header("游戏统计")
        summary = engine.get_game_summary()
        print(f"总轮数: {summary['game_info']['total_rounds']}")
        print(f"总事件数: {summary['game_info']['total_events']}")
        
        print("\n" + "=" * 80)
        print("Qwen3-30B智能AI演示完成!")
        print("AI玩家展现了:")
        print("✅ 智能的角色扮演")
        print("✅ 自然的语言表达")
        print("✅ 策略性的决策制定")
        print("✅ 个性化的行为模式")
        print("=" * 80)
        
    except Exception as e:
        print(f"\n演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
