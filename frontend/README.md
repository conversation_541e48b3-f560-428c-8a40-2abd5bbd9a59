# 狼人杀AI游戏 - React前端

这是狼人杀AI游戏的React前端界面，提供现代化的用户体验。

## 🚀 快速开始

### 环境要求

- Node.js 16.0 或更高版本
- npm 或 yarn

### 安装依赖

```bash
cd frontend/wolfkill-frontend
npm install
```

### 启动开发服务器

```bash
npm start
```

应用将在 http://localhost:3000 启动

### 构建生产版本

```bash
npm run build
```

## 🏗️ 项目结构

```
src/
├── components/          # React组件
│   ├── Header.tsx      # 头部组件
│   ├── GameBoard.tsx   # 游戏面板
│   ├── PlayerList.tsx  # 玩家列表
│   └── ChatPanel.tsx   # 聊天面板
├── hooks/              # 自定义Hooks
│   ├── useGameState.ts # 游戏状态管理
│   └── useWebSocket.ts # WebSocket连接
├── services/           # API服务
│   └── api.ts         # API接口
├── styles/            # 样式和主题
│   └── theme.ts       # 主题配置
├── types/             # TypeScript类型定义
│   └── index.ts       # 类型定义
├── App.tsx            # 主应用组件
└── index.tsx          # 应用入口
```

## 🎨 功能特性

### 已实现功能

- ✅ 现代化React界面
- ✅ TypeScript类型安全
- ✅ Styled-components样式系统
- ✅ 响应式设计
- ✅ 游戏状态管理
- ✅ WebSocket实时通信
- ✅ 玩家列表显示
- ✅ 聊天系统
- ✅ 游戏面板

### 待实现功能

- ⏳ 投票界面
- ⏳ 技能使用界面
- ⏳ 音效系统
- ⏳ 动画效果
- ⏳ 游戏教程
- ⏳ 设置面板

## 🔧 技术栈

- **React 18** - 用户界面框架
- **TypeScript** - 类型安全
- **Styled-components** - CSS-in-JS样式
- **Axios** - HTTP客户端
- **Socket.io-client** - WebSocket通信

## 🌐 API集成

前端通过以下方式与后端通信：

1. **HTTP API** - 游戏创建、状态查询等
2. **WebSocket** - 实时游戏状态同步

### 环境变量

创建 `.env` 文件配置API地址：

```env
REACT_APP_API_URL=http://localhost:8000/api
REACT_APP_WEBSOCKET_URL=http://localhost:8000
```

## 🎮 使用说明

1. **启动应用** - 访问 http://localhost:3000
2. **创建游戏** - 点击"创建游戏"按钮
3. **查看玩家** - 左侧面板显示所有玩家信息
4. **游戏操作** - 中央面板进行投票、技能等操作
5. **聊天交流** - 右侧面板发送消息

## 🔍 开发说明

### 组件开发

所有组件使用TypeScript和Styled-components开发：

```tsx
import React from 'react';
import styled from 'styled-components';

const StyledComponent = styled.div`
  color: ${props => props.theme.colors.primary};
`;

const MyComponent: React.FC = () => {
  return <StyledComponent>Hello World</StyledComponent>;
};
```

### 状态管理

使用自定义Hooks管理状态：

```tsx
const { gameState, loading, error } = useGameState();
const { connected, sendMessage } = useWebSocket();
```

### 主题系统

使用统一的主题系统：

```tsx
const theme = {
  colors: {
    primary: '#2C3E50',
    secondary: '#E67E22',
    // ...
  }
};
```

## 🐛 调试

### 开发工具

- React Developer Tools
- Redux DevTools (如果使用Redux)
- 浏览器开发者工具

### 常见问题

1. **WebSocket连接失败** - 检查后端服务是否启动
2. **API请求失败** - 检查API地址配置
3. **样式问题** - 检查主题配置

## 📝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 📄 许可证

MIT License