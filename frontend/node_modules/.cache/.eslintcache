[{"/private/peiwy/work/ai_projects/wolfkill/frontend/src/index.tsx": "1", "/private/peiwy/work/ai_projects/wolfkill/frontend/src/App.tsx": "2", "/private/peiwy/work/ai_projects/wolfkill/frontend/src/styles/theme.ts": "3", "/private/peiwy/work/ai_projects/wolfkill/frontend/src/components/PlayerList.tsx": "4", "/private/peiwy/work/ai_projects/wolfkill/frontend/src/components/GameBoard.tsx": "5", "/private/peiwy/work/ai_projects/wolfkill/frontend/src/components/ChatPanel.tsx": "6", "/private/peiwy/work/ai_projects/wolfkill/frontend/src/components/Header.tsx": "7", "/private/peiwy/work/ai_projects/wolfkill/frontend/src/contexts/GameContext.tsx": "8", "/private/peiwy/work/ai_projects/wolfkill/frontend/src/types/index.ts": "9", "/private/peiwy/work/ai_projects/wolfkill/frontend/src/components/animations/AnimatedComponents.tsx": "10", "/private/peiwy/work/ai_projects/wolfkill/frontend/src/components/dialogs/VoteDialog.tsx": "11", "/private/peiwy/work/ai_projects/wolfkill/frontend/src/components/dialogs/ActionDialog.tsx": "12", "/private/peiwy/work/ai_projects/wolfkill/frontend/src/components/animations/GameAnimations.tsx": "13", "/private/peiwy/work/ai_projects/wolfkill/frontend/src/services/api.ts": "14", "/private/peiwy/work/ai_projects/wolfkill/frontend/src/hooks/useWebSocket.ts": "15", "/private/peiwy/work/ai_projects/wolfkill/frontend/src/styles/animations.ts": "16"}, {"size": 251, "mtime": 1752749675583, "results": "17", "hashOfConfig": "18"}, {"size": 9111, "mtime": 1752800811088, "results": "19", "hashOfConfig": "18"}, {"size": 3653, "mtime": 1752749245274, "results": "20", "hashOfConfig": "18"}, {"size": 8368, "mtime": 1752750662072, "results": "21", "hashOfConfig": "18"}, {"size": 14075, "mtime": 1752800471227, "results": "22", "hashOfConfig": "18"}, {"size": 8039, "mtime": 1752750694638, "results": "23", "hashOfConfig": "18"}, {"size": 4755, "mtime": 1752749466465, "results": "24", "hashOfConfig": "18"}, {"size": 10983, "mtime": 1752800491075, "results": "25", "hashOfConfig": "18"}, {"size": 1942, "mtime": 1752749204932, "results": "26", "hashOfConfig": "18"}, {"size": 6385, "mtime": 1752750565296, "results": "27", "hashOfConfig": "18"}, {"size": 10354, "mtime": 1752750205325, "results": "28", "hashOfConfig": "18"}, {"size": 15092, "mtime": 1752750298893, "results": "29", "hashOfConfig": "18"}, {"size": 7191, "mtime": 1752750619556, "results": "30", "hashOfConfig": "18"}, {"size": 6033, "mtime": 1752749396025, "results": "31", "hashOfConfig": "18"}, {"size": 4285, "mtime": 1752749354339, "results": "32", "hashOfConfig": "18"}, {"size": 4681, "mtime": 1752800900787, "results": "33", "hashOfConfig": "18"}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "93m9yp", {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/private/peiwy/work/ai_projects/wolfkill/frontend/src/index.tsx", [], [], "/private/peiwy/work/ai_projects/wolfkill/frontend/src/App.tsx", ["82", "83", "84"], [], "/private/peiwy/work/ai_projects/wolfkill/frontend/src/styles/theme.ts", [], [], "/private/peiwy/work/ai_projects/wolfkill/frontend/src/components/PlayerList.tsx", ["85"], [], "/private/peiwy/work/ai_projects/wolfkill/frontend/src/components/GameBoard.tsx", ["86", "87", "88"], [], "/private/peiwy/work/ai_projects/wolfkill/frontend/src/components/ChatPanel.tsx", [], [], "/private/peiwy/work/ai_projects/wolfkill/frontend/src/components/Header.tsx", [], [], "/private/peiwy/work/ai_projects/wolfkill/frontend/src/contexts/GameContext.tsx", ["89"], [], "/private/peiwy/work/ai_projects/wolfkill/frontend/src/types/index.ts", [], [], "/private/peiwy/work/ai_projects/wolfkill/frontend/src/components/animations/AnimatedComponents.tsx", [], [], "/private/peiwy/work/ai_projects/wolfkill/frontend/src/components/dialogs/VoteDialog.tsx", ["90"], [], "/private/peiwy/work/ai_projects/wolfkill/frontend/src/components/dialogs/ActionDialog.tsx", [], [], "/private/peiwy/work/ai_projects/wolfkill/frontend/src/components/animations/GameAnimations.tsx", ["91"], [], "/private/peiwy/work/ai_projects/wolfkill/frontend/src/services/api.ts", [], [], "/private/peiwy/work/ai_projects/wolfkill/frontend/src/hooks/useWebSocket.ts", [], [], "/private/peiwy/work/ai_projects/wolfkill/frontend/src/styles/animations.ts", [], [], {"ruleId": "92", "severity": 1, "message": "93", "line": 9, "column": 10, "nodeType": "94", "messageId": "95", "endLine": 9, "endColumn": 22}, {"ruleId": "92", "severity": 1, "message": "96", "line": 145, "column": 7, "nodeType": "94", "messageId": "95", "endLine": 145, "endColumn": 27}, {"ruleId": "92", "severity": 1, "message": "97", "line": 152, "column": 5, "nodeType": "94", "messageId": "95", "endLine": 152, "endColumn": 21}, {"ruleId": "92", "severity": 1, "message": "98", "line": 5, "column": 25, "nodeType": "94", "messageId": "95", "endLine": 5, "endColumn": 31}, {"ruleId": "92", "severity": 1, "message": "99", "line": 231, "column": 10, "nodeType": "94", "messageId": "95", "endLine": 231, "endColumn": 24}, {"ruleId": "92", "severity": 1, "message": "100", "line": 231, "column": 26, "nodeType": "94", "messageId": "95", "endLine": 231, "endColumn": 43}, {"ruleId": "101", "severity": 1, "message": "102", "line": 251, "column": 6, "nodeType": "103", "endLine": 251, "endColumn": 47, "suggestions": "104"}, {"ruleId": "92", "severity": 1, "message": "105", "line": 2, "column": 46, "nodeType": "94", "messageId": "95", "endLine": 2, "endColumn": 62}, {"ruleId": "92", "severity": 1, "message": "106", "line": 4, "column": 10, "nodeType": "94", "messageId": "95", "endLine": 4, "endColumn": 22}, {"ruleId": "92", "severity": 1, "message": "107", "line": 3, "column": 10, "nodeType": "94", "messageId": "95", "endLine": 3, "endColumn": 20}, "@typescript-eslint/no-unused-vars", "'GameProvider' is defined but never used.", "Identifier", "unusedVar", "'AppContent' is assigned a value but never used.", "'selectedPlayerId' is assigned a value but never used.", "'FadeIn' is defined but never used.", "'selectedAction' is assigned a value but never used.", "'setSelectedAction' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'gameState'. Either include it or remove the dependency array.", "ArrayExpression", ["108"], "'WebSocketMessage' is defined but never used.", "'getRoleColor' is defined but never used.", "'animations' is defined but never used.", {"desc": "109", "fix": "110"}, "Update the dependencies array to be: [gameState, gameState.current_phase, previousPhase]", {"range": "111", "text": "112"}, [8496, 8537], "[gameState, gameState.current_phase, previousPhase]"]