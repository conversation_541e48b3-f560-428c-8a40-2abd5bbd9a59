{"ast": null, "code": "import { keyframes, css } from 'styled-components';\n\n// 基础动画\nexport const fadeIn = keyframes`\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n`;\nexport const fadeOut = keyframes`\n  from {\n    opacity: 1;\n  }\n  to {\n    opacity: 0;\n  }\n`;\nexport const slideInUp = keyframes`\n  from {\n    transform: translateY(100%);\n    opacity: 0;\n  }\n  to {\n    transform: translateY(0);\n    opacity: 1;\n  }\n`;\nexport const slideInDown = keyframes`\n  from {\n    transform: translateY(-100%);\n    opacity: 0;\n  }\n  to {\n    transform: translateY(0);\n    opacity: 1;\n  }\n`;\nexport const slideInLeft = keyframes`\n  from {\n    transform: translateX(-100%);\n    opacity: 0;\n  }\n  to {\n    transform: translateX(0);\n    opacity: 1;\n  }\n`;\nexport const slideInRight = keyframes`\n  from {\n    transform: translateX(100%);\n    opacity: 0;\n  }\n  to {\n    transform: translateX(0);\n    opacity: 1;\n  }\n`;\nexport const scaleIn = keyframes`\n  from {\n    transform: scale(0.8);\n    opacity: 0;\n  }\n  to {\n    transform: scale(1);\n    opacity: 1;\n  }\n`;\nexport const scaleOut = keyframes`\n  from {\n    transform: scale(1);\n    opacity: 1;\n  }\n  to {\n    transform: scale(0.8);\n    opacity: 0;\n  }\n`;\n\n// 旋转动画\nexport const spin = keyframes`\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n`;\nexport const pulse = keyframes`\n  0%, 100% {\n    transform: scale(1);\n    opacity: 1;\n  }\n  50% {\n    transform: scale(1.05);\n    opacity: 0.8;\n  }\n`;\nexport const bounce = keyframes`\n  0%, 20%, 53%, 80%, 100% {\n    transform: translate3d(0, 0, 0);\n  }\n  40%, 43% {\n    transform: translate3d(0, -8px, 0);\n  }\n  70% {\n    transform: translate3d(0, -4px, 0);\n  }\n  90% {\n    transform: translate3d(0, -2px, 0);\n  }\n`;\nexport const shake = keyframes`\n  0%, 100% {\n    transform: translateX(0);\n  }\n  10%, 30%, 50%, 70%, 90% {\n    transform: translateX(-2px);\n  }\n  20%, 40%, 60%, 80% {\n    transform: translateX(2px);\n  }\n`;\n\n// 游戏特定动画\nexport const cardFlip = keyframes`\n  0% {\n    transform: rotateY(0);\n  }\n  50% {\n    transform: rotateY(90deg);\n  }\n  100% {\n    transform: rotateY(0);\n  }\n`;\nexport const glow = keyframes`\n  0%, 100% {\n    box-shadow: 0 0 5px rgba(52, 152, 219, 0.5);\n  }\n  50% {\n    box-shadow: 0 0 20px rgba(52, 152, 219, 0.8);\n  }\n`;\nexport const heartbeat = keyframes`\n  0%, 100% {\n    transform: scale(1);\n  }\n  14% {\n    transform: scale(1.1);\n  }\n  28% {\n    transform: scale(1);\n  }\n  42% {\n    transform: scale(1.1);\n  }\n  70% {\n    transform: scale(1);\n  }\n`;\nexport const typewriter = keyframes`\n  from {\n    width: 0;\n  }\n  to {\n    width: 100%;\n  }\n`;\nexport const blink = keyframes`\n  0%, 50% {\n    opacity: 1;\n  }\n  51%, 100% {\n    opacity: 0;\n  }\n`;\n\n// 粒子效果动画\nexport const float = keyframes`\n  0%, 100% {\n    transform: translateY(0px);\n  }\n  50% {\n    transform: translateY(-10px);\n  }\n`;\nexport const sparkle = keyframes`\n  0%, 100% {\n    opacity: 0;\n    transform: scale(0);\n  }\n  50% {\n    opacity: 1;\n    transform: scale(1);\n  }\n`;\n\n// 动画组合工具\nexport const animations = {\n  // 入场动画\n  entrance: {\n    fadeIn: css`${fadeIn} 0.3s ease-out`,\n    slideInUp: css`${slideInUp} 0.4s ease-out`,\n    slideInDown: css`${slideInDown} 0.4s ease-out`,\n    slideInLeft: css`${slideInLeft} 0.4s ease-out`,\n    slideInRight: css`${slideInRight} 0.4s ease-out`,\n    scaleIn: css`${scaleIn} 0.3s ease-out`\n  },\n  // 退场动画\n  exit: {\n    fadeOut: css`${fadeOut} 0.3s ease-in`,\n    scaleOut: css`${scaleOut} 0.3s ease-in`\n  },\n  // 循环动画\n  loop: {\n    spin: css`${spin} 1s linear infinite`,\n    pulse: css`${pulse} 2s ease-in-out infinite`,\n    bounce: css`${bounce} 1s ease infinite`,\n    float: css`${float} 3s ease-in-out infinite`,\n    glow: css`${glow} 2s ease-in-out infinite`,\n    heartbeat: css`${heartbeat} 1.5s ease-in-out infinite`\n  },\n  // 交互动画\n  interaction: {\n    shake: css`${shake} 0.5s ease-in-out`,\n    cardFlip: css`${cardFlip} 0.6s ease-in-out`\n  },\n  // 文字动画\n  text: {\n    typewriter: css`${typewriter} 2s steps(40, end)`,\n    blink: css`${blink} 1s step-end infinite`\n  }\n};\n\n// 动画延迟工具\nexport const getStaggerDelay = (index, baseDelay = 0.1) => {\n  return `${baseDelay * index}s`;\n};\n\n// 缓动函数\nexport const easings = {\n  easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',\n  easeOut: 'cubic-bezier(0, 0, 0.2, 1)',\n  easeIn: 'cubic-bezier(0.4, 0, 1, 1)',\n  bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',\n  elastic: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)'\n};\n\n// 动画持续时间\nexport const durations = {\n  fast: '150ms',\n  normal: '300ms',\n  slow: '500ms',\n  slower: '800ms'\n};", "map": {"version": 3, "names": ["keyframes", "css", "fadeIn", "fadeOut", "slideInUp", "slideInDown", "slideInLeft", "slideInRight", "scaleIn", "scaleOut", "spin", "pulse", "bounce", "shake", "cardFlip", "glow", "heartbeat", "typewriter", "blink", "float", "sparkle", "animations", "entrance", "exit", "loop", "interaction", "text", "getStaggerDelay", "index", "baseDelay", "easings", "easeInOut", "easeOut", "easeIn", "elastic", "durations", "fast", "normal", "slow", "slower"], "sources": ["/private/peiwy/work/ai_projects/wolfkill/frontend/src/styles/animations.ts"], "sourcesContent": ["import { keyframes, css } from 'styled-components';\n\n// 基础动画\nexport const fadeIn = keyframes`\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n`;\n\nexport const fadeOut = keyframes`\n  from {\n    opacity: 1;\n  }\n  to {\n    opacity: 0;\n  }\n`;\n\nexport const slideInUp = keyframes`\n  from {\n    transform: translateY(100%);\n    opacity: 0;\n  }\n  to {\n    transform: translateY(0);\n    opacity: 1;\n  }\n`;\n\nexport const slideInDown = keyframes`\n  from {\n    transform: translateY(-100%);\n    opacity: 0;\n  }\n  to {\n    transform: translateY(0);\n    opacity: 1;\n  }\n`;\n\nexport const slideInLeft = keyframes`\n  from {\n    transform: translateX(-100%);\n    opacity: 0;\n  }\n  to {\n    transform: translateX(0);\n    opacity: 1;\n  }\n`;\n\nexport const slideInRight = keyframes`\n  from {\n    transform: translateX(100%);\n    opacity: 0;\n  }\n  to {\n    transform: translateX(0);\n    opacity: 1;\n  }\n`;\n\nexport const scaleIn = keyframes`\n  from {\n    transform: scale(0.8);\n    opacity: 0;\n  }\n  to {\n    transform: scale(1);\n    opacity: 1;\n  }\n`;\n\nexport const scaleOut = keyframes`\n  from {\n    transform: scale(1);\n    opacity: 1;\n  }\n  to {\n    transform: scale(0.8);\n    opacity: 0;\n  }\n`;\n\n// 旋转动画\nexport const spin = keyframes`\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n`;\n\nexport const pulse = keyframes`\n  0%, 100% {\n    transform: scale(1);\n    opacity: 1;\n  }\n  50% {\n    transform: scale(1.05);\n    opacity: 0.8;\n  }\n`;\n\nexport const bounce = keyframes`\n  0%, 20%, 53%, 80%, 100% {\n    transform: translate3d(0, 0, 0);\n  }\n  40%, 43% {\n    transform: translate3d(0, -8px, 0);\n  }\n  70% {\n    transform: translate3d(0, -4px, 0);\n  }\n  90% {\n    transform: translate3d(0, -2px, 0);\n  }\n`;\n\nexport const shake = keyframes`\n  0%, 100% {\n    transform: translateX(0);\n  }\n  10%, 30%, 50%, 70%, 90% {\n    transform: translateX(-2px);\n  }\n  20%, 40%, 60%, 80% {\n    transform: translateX(2px);\n  }\n`;\n\n// 游戏特定动画\nexport const cardFlip = keyframes`\n  0% {\n    transform: rotateY(0);\n  }\n  50% {\n    transform: rotateY(90deg);\n  }\n  100% {\n    transform: rotateY(0);\n  }\n`;\n\nexport const glow = keyframes`\n  0%, 100% {\n    box-shadow: 0 0 5px rgba(52, 152, 219, 0.5);\n  }\n  50% {\n    box-shadow: 0 0 20px rgba(52, 152, 219, 0.8);\n  }\n`;\n\nexport const heartbeat = keyframes`\n  0%, 100% {\n    transform: scale(1);\n  }\n  14% {\n    transform: scale(1.1);\n  }\n  28% {\n    transform: scale(1);\n  }\n  42% {\n    transform: scale(1.1);\n  }\n  70% {\n    transform: scale(1);\n  }\n`;\n\nexport const typewriter = keyframes`\n  from {\n    width: 0;\n  }\n  to {\n    width: 100%;\n  }\n`;\n\nexport const blink = keyframes`\n  0%, 50% {\n    opacity: 1;\n  }\n  51%, 100% {\n    opacity: 0;\n  }\n`;\n\n// 粒子效果动画\nexport const float = keyframes`\n  0%, 100% {\n    transform: translateY(0px);\n  }\n  50% {\n    transform: translateY(-10px);\n  }\n`;\n\nexport const sparkle = keyframes`\n  0%, 100% {\n    opacity: 0;\n    transform: scale(0);\n  }\n  50% {\n    opacity: 1;\n    transform: scale(1);\n  }\n`;\n\n// 动画组合工具\nexport const animations = {\n  // 入场动画\n  entrance: {\n    fadeIn: css`${fadeIn} 0.3s ease-out`,\n    slideInUp: css`${slideInUp} 0.4s ease-out`,\n    slideInDown: css`${slideInDown} 0.4s ease-out`,\n    slideInLeft: css`${slideInLeft} 0.4s ease-out`,\n    slideInRight: css`${slideInRight} 0.4s ease-out`,\n    scaleIn: css`${scaleIn} 0.3s ease-out`,\n  },\n\n  // 退场动画\n  exit: {\n    fadeOut: css`${fadeOut} 0.3s ease-in`,\n    scaleOut: css`${scaleOut} 0.3s ease-in`,\n  },\n\n  // 循环动画\n  loop: {\n    spin: css`${spin} 1s linear infinite`,\n    pulse: css`${pulse} 2s ease-in-out infinite`,\n    bounce: css`${bounce} 1s ease infinite`,\n    float: css`${float} 3s ease-in-out infinite`,\n    glow: css`${glow} 2s ease-in-out infinite`,\n    heartbeat: css`${heartbeat} 1.5s ease-in-out infinite`,\n  },\n\n  // 交互动画\n  interaction: {\n    shake: css`${shake} 0.5s ease-in-out`,\n    cardFlip: css`${cardFlip} 0.6s ease-in-out`,\n  },\n\n  // 文字动画\n  text: {\n    typewriter: css`${typewriter} 2s steps(40, end)`,\n    blink: css`${blink} 1s step-end infinite`,\n  }\n};\n\n// 动画延迟工具\nexport const getStaggerDelay = (index: number, baseDelay: number = 0.1): string => {\n  return `${baseDelay * index}s`;\n};\n\n// 缓动函数\nexport const easings = {\n  easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',\n  easeOut: 'cubic-bezier(0, 0, 0.2, 1)',\n  easeIn: 'cubic-bezier(0.4, 0, 1, 1)',\n  bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',\n  elastic: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)',\n};\n\n// 动画持续时间\nexport const durations = {\n  fast: '150ms',\n  normal: '300ms',\n  slow: '500ms',\n  slower: '800ms',\n};"], "mappings": "AAAA,SAASA,SAAS,EAAEC,GAAG,QAAQ,mBAAmB;;AAElD;AACA,OAAO,MAAMC,MAAM,GAAGF,SAAS;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,OAAO,MAAMG,OAAO,GAAGH,SAAS;AAChC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,OAAO,MAAMI,SAAS,GAAGJ,SAAS;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,OAAO,MAAMK,WAAW,GAAGL,SAAS;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,OAAO,MAAMM,WAAW,GAAGN,SAAS;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,OAAO,MAAMO,YAAY,GAAGP,SAAS;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,OAAO,MAAMQ,OAAO,GAAGR,SAAS;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,OAAO,MAAMS,QAAQ,GAAGT,SAAS;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,OAAO,MAAMU,IAAI,GAAGV,SAAS;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,OAAO,MAAMW,KAAK,GAAGX,SAAS;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,OAAO,MAAMY,MAAM,GAAGZ,SAAS;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,OAAO,MAAMa,KAAK,GAAGb,SAAS;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,OAAO,MAAMc,QAAQ,GAAGd,SAAS;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,OAAO,MAAMe,IAAI,GAAGf,SAAS;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,OAAO,MAAMgB,SAAS,GAAGhB,SAAS;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,OAAO,MAAMiB,UAAU,GAAGjB,SAAS;AACnC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,OAAO,MAAMkB,KAAK,GAAGlB,SAAS;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,OAAO,MAAMmB,KAAK,GAAGnB,SAAS;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,OAAO,MAAMoB,OAAO,GAAGpB,SAAS;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,OAAO,MAAMqB,UAAU,GAAG;EACxB;EACAC,QAAQ,EAAE;IACRpB,MAAM,EAAED,GAAG,GAAGC,MAAM,gBAAgB;IACpCE,SAAS,EAAEH,GAAG,GAAGG,SAAS,gBAAgB;IAC1CC,WAAW,EAAEJ,GAAG,GAAGI,WAAW,gBAAgB;IAC9CC,WAAW,EAAEL,GAAG,GAAGK,WAAW,gBAAgB;IAC9CC,YAAY,EAAEN,GAAG,GAAGM,YAAY,gBAAgB;IAChDC,OAAO,EAAEP,GAAG,GAAGO,OAAO;EACxB,CAAC;EAED;EACAe,IAAI,EAAE;IACJpB,OAAO,EAAEF,GAAG,GAAGE,OAAO,eAAe;IACrCM,QAAQ,EAAER,GAAG,GAAGQ,QAAQ;EAC1B,CAAC;EAED;EACAe,IAAI,EAAE;IACJd,IAAI,EAAET,GAAG,GAAGS,IAAI,qBAAqB;IACrCC,KAAK,EAAEV,GAAG,GAAGU,KAAK,0BAA0B;IAC5CC,MAAM,EAAEX,GAAG,GAAGW,MAAM,mBAAmB;IACvCO,KAAK,EAAElB,GAAG,GAAGkB,KAAK,0BAA0B;IAC5CJ,IAAI,EAAEd,GAAG,GAAGc,IAAI,0BAA0B;IAC1CC,SAAS,EAAEf,GAAG,GAAGe,SAAS;EAC5B,CAAC;EAED;EACAS,WAAW,EAAE;IACXZ,KAAK,EAAEZ,GAAG,GAAGY,KAAK,mBAAmB;IACrCC,QAAQ,EAAEb,GAAG,GAAGa,QAAQ;EAC1B,CAAC;EAED;EACAY,IAAI,EAAE;IACJT,UAAU,EAAEhB,GAAG,GAAGgB,UAAU,oBAAoB;IAChDC,KAAK,EAAEjB,GAAG,GAAGiB,KAAK;EACpB;AACF,CAAC;;AAED;AACA,OAAO,MAAMS,eAAe,GAAGA,CAACC,KAAa,EAAEC,SAAiB,GAAG,GAAG,KAAa;EACjF,OAAO,GAAGA,SAAS,GAAGD,KAAK,GAAG;AAChC,CAAC;;AAED;AACA,OAAO,MAAME,OAAO,GAAG;EACrBC,SAAS,EAAE,8BAA8B;EACzCC,OAAO,EAAE,4BAA4B;EACrCC,MAAM,EAAE,4BAA4B;EACpCrB,MAAM,EAAE,wCAAwC;EAChDsB,OAAO,EAAE;AACX,CAAC;;AAED;AACA,OAAO,MAAMC,SAAS,GAAG;EACvBC,IAAI,EAAE,OAAO;EACbC,MAAM,EAAE,OAAO;EACfC,IAAI,EAAE,OAAO;EACbC,MAAM,EAAE;AACV,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}