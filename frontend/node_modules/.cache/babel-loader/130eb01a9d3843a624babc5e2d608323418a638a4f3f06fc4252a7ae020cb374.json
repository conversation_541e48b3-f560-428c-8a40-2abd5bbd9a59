{"ast": null, "code": "var _jsxFileName = \"/private/peiwy/work/ai_projects/wolfkill/frontend/src/components/dialogs/VoteDialog.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { VoteType } from '../../types';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Overlay = styled.div`\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  display: ${props => props.isOpen ? 'flex' : 'none'};\n  justify-content: center;\n  align-items: center;\n  z-index: ${props => props.theme.zIndex.modal};\n`;\n_c = Overlay;\nconst DialogContainer = styled.div`\n  background: ${props => props.theme.colors.backgroundLight};\n  border-radius: ${props => props.theme.borderRadius.xl};\n  box-shadow: ${props => props.theme.shadows.xl};\n  width: 90%;\n  max-width: 500px;\n  max-height: 80vh;\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n`;\n_c2 = DialogContainer;\nconst DialogHeader = styled.div`\n  padding: ${props => props.theme.spacing.lg};\n  border-bottom: 1px solid ${props => props.theme.colors.border};\n  background: ${props => props.theme.colors.background};\n`;\n_c3 = DialogHeader;\nconst DialogTitle = styled.h2`\n  font-size: ${props => props.theme.fontSizes.xl};\n  font-weight: ${props => props.theme.fontWeights.semibold};\n  color: ${props => props.theme.colors.text};\n  margin: 0 0 ${props => props.theme.spacing.sm} 0;\n`;\n_c4 = DialogTitle;\nconst DialogDescription = styled.p`\n  font-size: ${props => props.theme.fontSizes.md};\n  color: ${props => props.theme.colors.textLight};\n  margin: 0;\n  line-height: ${props => props.theme.lineHeights.relaxed};\n`;\n_c5 = DialogDescription;\nconst DialogBody = styled.div`\n  padding: ${props => props.theme.spacing.lg};\n  flex: 1;\n  overflow-y: auto;\n`;\n_c6 = DialogBody;\nconst PlayerGrid = styled.div`\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: ${props => props.theme.spacing.sm};\n  margin-bottom: ${props => props.theme.spacing.lg};\n`;\n_c7 = PlayerGrid;\nconst PlayerOption = styled.div`\n  display: flex;\n  align-items: center;\n  padding: ${props => props.theme.spacing.md};\n  border: 2px solid ${props => props.isSelected ? props.theme.colors.primary : props.theme.colors.border};\n  border-radius: ${props => props.theme.borderRadius.lg};\n  background: ${props => props.isSelected ? props.theme.colors.primary + '10' : props.theme.colors.backgroundLight};\n  cursor: ${props => props.isAlive ? 'pointer' : 'not-allowed'};\n  opacity: ${props => props.isAlive ? 1 : 0.5};\n  transition: ${props => props.theme.transitions.fast};\n\n  &:hover {\n    border-color: ${props => props.isAlive ? props.theme.colors.primary : props.theme.colors.border};\n    background: ${props => props.isAlive ? props.theme.colors.primary + '20' : props.theme.colors.backgroundLight};\n  }\n`;\n_c8 = PlayerOption;\nconst PlayerInfo = styled.div`\n  flex: 1;\n`;\n_c9 = PlayerInfo;\nconst PlayerName = styled.div`\n  font-size: ${props => props.theme.fontSizes.md};\n  font-weight: ${props => props.theme.fontWeights.medium};\n  color: ${props => props.theme.colors.text};\n  margin-bottom: ${props => props.theme.spacing.xs};\n`;\n_c0 = PlayerName;\nconst PlayerStatus = styled.div`\n  font-size: ${props => props.theme.fontSizes.sm};\n  color: ${props => props.isAlive ? props.theme.colors.success : props.theme.colors.danger};\n`;\n_c1 = PlayerStatus;\nconst StatusIndicator = styled.div`\n  width: 12px;\n  height: 12px;\n  border-radius: 50%;\n  background: ${props => props.isAlive ? props.theme.colors.success : props.theme.colors.danger};\n  margin-left: ${props => props.theme.spacing.md};\n`;\n_c10 = StatusIndicator;\nconst ReasonSection = styled.div`\n  margin-bottom: ${props => props.theme.spacing.lg};\n`;\n_c11 = ReasonSection;\nconst ReasonLabel = styled.label`\n  display: block;\n  font-size: ${props => props.theme.fontSizes.md};\n  font-weight: ${props => props.theme.fontWeights.medium};\n  color: ${props => props.theme.colors.text};\n  margin-bottom: ${props => props.theme.spacing.sm};\n`;\n_c12 = ReasonLabel;\nconst ReasonInput = styled.textarea`\n  width: 100%;\n  padding: ${props => props.theme.spacing.md};\n  border: 1px solid ${props => props.theme.colors.border};\n  border-radius: ${props => props.theme.borderRadius.md};\n  font-size: ${props => props.theme.fontSizes.md};\n  font-family: ${props => props.theme.fonts.primary};\n  resize: vertical;\n  min-height: 80px;\n  transition: ${props => props.theme.transitions.fast};\n\n  &:focus {\n    outline: none;\n    border-color: ${props => props.theme.colors.primary};\n    box-shadow: 0 0 0 2px ${props => props.theme.colors.primary}20;\n  }\n\n  &::placeholder {\n    color: ${props => props.theme.colors.textLight};\n  }\n`;\n_c13 = ReasonInput;\nconst DialogFooter = styled.div`\n  padding: ${props => props.theme.spacing.lg};\n  border-top: 1px solid ${props => props.theme.colors.border};\n  display: flex;\n  gap: ${props => props.theme.spacing.md};\n  justify-content: flex-end;\n`;\n_c14 = DialogFooter;\nconst Button = styled.button`\n  padding: ${props => props.theme.spacing.md} ${props => props.theme.spacing.lg};\n  border-radius: ${props => props.theme.borderRadius.md};\n  font-size: ${props => props.theme.fontSizes.md};\n  font-weight: ${props => props.theme.fontWeights.medium};\n  border: none;\n  cursor: pointer;\n  transition: ${props => props.theme.transitions.fast};\n  min-width: 100px;\n\n  ${props => {\n  switch (props.variant) {\n    case 'danger':\n      return `\n          background: ${props.theme.colors.danger};\n          color: ${props.theme.colors.textWhite};\n          &:hover:not(:disabled) { background: ${props.theme.colors.dangerLight}; }\n        `;\n    case 'secondary':\n      return `\n          background: ${props.theme.colors.backgroundDark};\n          color: ${props.theme.colors.text};\n          &:hover:not(:disabled) { background: ${props.theme.colors.border}; }\n        `;\n    default:\n      return `\n          background: ${props.theme.colors.primary};\n          color: ${props.theme.colors.textWhite};\n          &:hover:not(:disabled) { background: ${props.theme.colors.primaryLight}; }\n        `;\n  }\n}}\n\n  &:disabled {\n    background: ${props => props.theme.colors.backgroundDark};\n    color: ${props => props.theme.colors.textLight};\n    cursor: not-allowed;\n  }\n`;\n_c15 = Button;\nconst getVoteTitle = voteType => {\n  const titles = {\n    [VoteType.ELIMINATION]: '投票淘汰',\n    [VoteType.WEREWOLF_KILL]: '狼人杀人',\n    [VoteType.SEER_CHECK]: '预言家查验',\n    [VoteType.WITCH_SAVE]: '女巫救人',\n    [VoteType.WITCH_POISON]: '女巫毒人',\n    [VoteType.GUARD_PROTECT]: '守卫保护'\n  };\n  return titles[voteType] || '选择目标';\n};\nconst getVoteDescription = voteType => {\n  const descriptions = {\n    [VoteType.ELIMINATION]: '选择要投票淘汰的玩家，或选择弃权',\n    [VoteType.WEREWOLF_KILL]: '选择要杀害的玩家',\n    [VoteType.SEER_CHECK]: '选择要查验身份的玩家',\n    [VoteType.WITCH_SAVE]: '选择要救治的玩家',\n    [VoteType.WITCH_POISON]: '选择要毒杀的玩家',\n    [VoteType.GUARD_PROTECT]: '选择要保护的玩家'\n  };\n  return descriptions[voteType] || '请选择一个目标';\n};\nconst VoteDialog = ({\n  isOpen,\n  players,\n  voteType,\n  onClose,\n  onConfirm\n}) => {\n  _s();\n  const [selectedPlayerId, setSelectedPlayerId] = useState(null);\n  const [reason, setReason] = useState('');\n  const playerList = Object.values(players);\n  const alivePlayers = playerList.filter(p => p.status === 'ALIVE');\n  const showReasonInput = voteType === VoteType.ELIMINATION;\n  const handlePlayerSelect = playerId => {\n    const player = players[playerId];\n    if (player && player.status === 'ALIVE') {\n      setSelectedPlayerId(playerId);\n    }\n  };\n  const handleConfirm = () => {\n    onConfirm(selectedPlayerId, reason.trim() || undefined);\n    handleClose();\n  };\n  const handleAbstain = () => {\n    onConfirm(null, '选择弃权');\n    handleClose();\n  };\n  const handleClose = () => {\n    setSelectedPlayerId(null);\n    setReason('');\n    onClose();\n  };\n  const handleOverlayClick = e => {\n    if (e.target === e.currentTarget) {\n      handleClose();\n    }\n  };\n  if (!isOpen) return null;\n  return /*#__PURE__*/_jsxDEV(Overlay, {\n    isOpen: isOpen,\n    onClick: handleOverlayClick,\n    children: /*#__PURE__*/_jsxDEV(DialogContainer, {\n      children: [/*#__PURE__*/_jsxDEV(DialogHeader, {\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: getVoteTitle(voteType)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogDescription, {\n          children: getVoteDescription(voteType)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogBody, {\n        children: [/*#__PURE__*/_jsxDEV(PlayerGrid, {\n          children: alivePlayers.map(player => /*#__PURE__*/_jsxDEV(PlayerOption, {\n            isSelected: selectedPlayerId === player.player_id,\n            isAlive: player.status === 'ALIVE',\n            onClick: () => handlePlayerSelect(player.player_id),\n            children: [/*#__PURE__*/_jsxDEV(PlayerInfo, {\n              children: [/*#__PURE__*/_jsxDEV(PlayerName, {\n                children: player.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(PlayerStatus, {\n                isAlive: player.status === 'ALIVE',\n                children: player.status === 'ALIVE' ? '存活' : '死亡'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(StatusIndicator, {\n              isAlive: player.status === 'ALIVE'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 17\n            }, this)]\n          }, player.player_id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 11\n        }, this), showReasonInput && /*#__PURE__*/_jsxDEV(ReasonSection, {\n          children: [/*#__PURE__*/_jsxDEV(ReasonLabel, {\n            htmlFor: \"vote-reason\",\n            children: \"\\u6295\\u7968\\u7406\\u7531\\uFF08\\u53EF\\u9009\\uFF09\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ReasonInput, {\n            id: \"vote-reason\",\n            value: reason,\n            onChange: e => setReason(e.target.value),\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u6295\\u7968\\u7406\\u7531...\",\n            maxLength: 200\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 274,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogFooter, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: handleClose,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 11\n        }, this), voteType === VoteType.ELIMINATION && /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"danger\",\n          onClick: handleAbstain,\n          children: \"\\u5F03\\u6743\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: handleConfirm,\n          disabled: !selectedPlayerId,\n          children: \"\\u786E\\u8BA4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 310,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 266,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 265,\n    columnNumber: 5\n  }, this);\n};\n_s(VoteDialog, \"GgfEPZ2lYJ0/qOn+8uJ4Y9AdPMY=\");\n_c16 = VoteDialog;\nexport default VoteDialog;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16;\n$RefreshReg$(_c, \"Overlay\");\n$RefreshReg$(_c2, \"DialogContainer\");\n$RefreshReg$(_c3, \"DialogHeader\");\n$RefreshReg$(_c4, \"DialogTitle\");\n$RefreshReg$(_c5, \"DialogDescription\");\n$RefreshReg$(_c6, \"DialogBody\");\n$RefreshReg$(_c7, \"PlayerGrid\");\n$RefreshReg$(_c8, \"PlayerOption\");\n$RefreshReg$(_c9, \"PlayerInfo\");\n$RefreshReg$(_c0, \"PlayerName\");\n$RefreshReg$(_c1, \"PlayerStatus\");\n$RefreshReg$(_c10, \"StatusIndicator\");\n$RefreshReg$(_c11, \"ReasonSection\");\n$RefreshReg$(_c12, \"ReasonLabel\");\n$RefreshReg$(_c13, \"ReasonInput\");\n$RefreshReg$(_c14, \"DialogFooter\");\n$RefreshReg$(_c15, \"Button\");\n$RefreshReg$(_c16, \"VoteDialog\");", "map": {"version": 3, "names": ["React", "useState", "styled", "VoteType", "jsxDEV", "_jsxDEV", "Overlay", "div", "props", "isOpen", "theme", "zIndex", "modal", "_c", "DialogContainer", "colors", "backgroundLight", "borderRadius", "xl", "shadows", "_c2", "DialogHeader", "spacing", "lg", "border", "background", "_c3", "DialogTitle", "h2", "fontSizes", "fontWeights", "semibold", "text", "sm", "_c4", "DialogDescription", "p", "md", "textLight", "lineHeights", "relaxed", "_c5", "DialogBody", "_c6", "<PERSON><PERSON><PERSON>", "_c7", "PlayerOption", "isSelected", "primary", "isAlive", "transitions", "fast", "_c8", "PlayerInfo", "_c9", "<PERSON><PERSON><PERSON>", "medium", "xs", "_c0", "PlayerStatus", "success", "danger", "_c1", "StatusIndicator", "_c10", "ReasonSection", "_c11", "ReasonLabel", "label", "_c12", "ReasonInput", "textarea", "fonts", "_c13", "<PERSON><PERSON><PERSON><PERSON>er", "_c14", "<PERSON><PERSON>", "button", "variant", "textWhite", "dangerLight", "backgroundDark", "primaryLight", "_c15", "getVoteTitle", "voteType", "titles", "ELIMINATION", "WEREWOLF_KILL", "SEER_CHECK", "WITCH_SAVE", "WITCH_POISON", "GUARD_PROTECT", "getVoteDescription", "descriptions", "VoteDialog", "players", "onClose", "onConfirm", "_s", "selectedPlayerId", "setSelectedPlayerId", "reason", "setReason", "playerList", "Object", "values", "alivePlayers", "filter", "status", "showReasonInput", "handlePlayerSelect", "playerId", "player", "handleConfirm", "trim", "undefined", "handleClose", "handleAbstain", "handleOverlayClick", "e", "target", "currentTarget", "onClick", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "player_id", "name", "htmlFor", "id", "value", "onChange", "placeholder", "max<PERSON><PERSON><PERSON>", "disabled", "_c16", "$RefreshReg$"], "sources": ["/private/peiwy/work/ai_projects/wolfkill/frontend/src/components/dialogs/VoteDialog.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { Player, VoteType } from '../../types';\nimport { getRoleColor } from '../../styles/theme';\n\ninterface VoteDialogProps {\n  isOpen: boolean;\n  players: { [key: number]: Player };\n  voteType: VoteType;\n  onClose: () => void;\n  onConfirm: (targetId: number | null, reason?: string) => void;\n}\n\nconst Overlay = styled.div<{ isOpen: boolean }>`\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  display: ${props => props.isOpen ? 'flex' : 'none'};\n  justify-content: center;\n  align-items: center;\n  z-index: ${props => props.theme.zIndex.modal};\n`;\n\nconst DialogContainer = styled.div`\n  background: ${props => props.theme.colors.backgroundLight};\n  border-radius: ${props => props.theme.borderRadius.xl};\n  box-shadow: ${props => props.theme.shadows.xl};\n  width: 90%;\n  max-width: 500px;\n  max-height: 80vh;\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n`;\n\nconst DialogHeader = styled.div`\n  padding: ${props => props.theme.spacing.lg};\n  border-bottom: 1px solid ${props => props.theme.colors.border};\n  background: ${props => props.theme.colors.background};\n`;\n\nconst DialogTitle = styled.h2`\n  font-size: ${props => props.theme.fontSizes.xl};\n  font-weight: ${props => props.theme.fontWeights.semibold};\n  color: ${props => props.theme.colors.text};\n  margin: 0 0 ${props => props.theme.spacing.sm} 0;\n`;\n\nconst DialogDescription = styled.p`\n  font-size: ${props => props.theme.fontSizes.md};\n  color: ${props => props.theme.colors.textLight};\n  margin: 0;\n  line-height: ${props => props.theme.lineHeights.relaxed};\n`;\n\nconst DialogBody = styled.div`\n  padding: ${props => props.theme.spacing.lg};\n  flex: 1;\n  overflow-y: auto;\n`;\n\nconst PlayerGrid = styled.div`\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: ${props => props.theme.spacing.sm};\n  margin-bottom: ${props => props.theme.spacing.lg};\n`;\n\nconst PlayerOption = styled.div<{ isSelected: boolean; isAlive: boolean }>`\n  display: flex;\n  align-items: center;\n  padding: ${props => props.theme.spacing.md};\n  border: 2px solid ${props => props.isSelected ? props.theme.colors.primary : props.theme.colors.border};\n  border-radius: ${props => props.theme.borderRadius.lg};\n  background: ${props => props.isSelected ? props.theme.colors.primary + '10' : props.theme.colors.backgroundLight};\n  cursor: ${props => props.isAlive ? 'pointer' : 'not-allowed'};\n  opacity: ${props => props.isAlive ? 1 : 0.5};\n  transition: ${props => props.theme.transitions.fast};\n\n  &:hover {\n    border-color: ${props => props.isAlive ? props.theme.colors.primary : props.theme.colors.border};\n    background: ${props => props.isAlive ? props.theme.colors.primary + '20' : props.theme.colors.backgroundLight};\n  }\n`;\n\nconst PlayerInfo = styled.div`\n  flex: 1;\n`;\n\nconst PlayerName = styled.div`\n  font-size: ${props => props.theme.fontSizes.md};\n  font-weight: ${props => props.theme.fontWeights.medium};\n  color: ${props => props.theme.colors.text};\n  margin-bottom: ${props => props.theme.spacing.xs};\n`;\n\nconst PlayerStatus = styled.div<{ isAlive: boolean }>`\n  font-size: ${props => props.theme.fontSizes.sm};\n  color: ${props => props.isAlive ? props.theme.colors.success : props.theme.colors.danger};\n`;\n\nconst StatusIndicator = styled.div<{ isAlive: boolean }>`\n  width: 12px;\n  height: 12px;\n  border-radius: 50%;\n  background: ${props => props.isAlive ? props.theme.colors.success : props.theme.colors.danger};\n  margin-left: ${props => props.theme.spacing.md};\n`;\n\nconst ReasonSection = styled.div`\n  margin-bottom: ${props => props.theme.spacing.lg};\n`;\n\nconst ReasonLabel = styled.label`\n  display: block;\n  font-size: ${props => props.theme.fontSizes.md};\n  font-weight: ${props => props.theme.fontWeights.medium};\n  color: ${props => props.theme.colors.text};\n  margin-bottom: ${props => props.theme.spacing.sm};\n`;\n\nconst ReasonInput = styled.textarea`\n  width: 100%;\n  padding: ${props => props.theme.spacing.md};\n  border: 1px solid ${props => props.theme.colors.border};\n  border-radius: ${props => props.theme.borderRadius.md};\n  font-size: ${props => props.theme.fontSizes.md};\n  font-family: ${props => props.theme.fonts.primary};\n  resize: vertical;\n  min-height: 80px;\n  transition: ${props => props.theme.transitions.fast};\n\n  &:focus {\n    outline: none;\n    border-color: ${props => props.theme.colors.primary};\n    box-shadow: 0 0 0 2px ${props => props.theme.colors.primary}20;\n  }\n\n  &::placeholder {\n    color: ${props => props.theme.colors.textLight};\n  }\n`;\n\nconst DialogFooter = styled.div`\n  padding: ${props => props.theme.spacing.lg};\n  border-top: 1px solid ${props => props.theme.colors.border};\n  display: flex;\n  gap: ${props => props.theme.spacing.md};\n  justify-content: flex-end;\n`;\n\nconst Button = styled.button<{ variant?: 'primary' | 'secondary' | 'danger' }>`\n  padding: ${props => props.theme.spacing.md} ${props => props.theme.spacing.lg};\n  border-radius: ${props => props.theme.borderRadius.md};\n  font-size: ${props => props.theme.fontSizes.md};\n  font-weight: ${props => props.theme.fontWeights.medium};\n  border: none;\n  cursor: pointer;\n  transition: ${props => props.theme.transitions.fast};\n  min-width: 100px;\n\n  ${props => {\n    switch (props.variant) {\n      case 'danger':\n        return `\n          background: ${props.theme.colors.danger};\n          color: ${props.theme.colors.textWhite};\n          &:hover:not(:disabled) { background: ${props.theme.colors.dangerLight}; }\n        `;\n      case 'secondary':\n        return `\n          background: ${props.theme.colors.backgroundDark};\n          color: ${props.theme.colors.text};\n          &:hover:not(:disabled) { background: ${props.theme.colors.border}; }\n        `;\n      default:\n        return `\n          background: ${props.theme.colors.primary};\n          color: ${props.theme.colors.textWhite};\n          &:hover:not(:disabled) { background: ${props.theme.colors.primaryLight}; }\n        `;\n    }\n  }}\n\n  &:disabled {\n    background: ${props => props.theme.colors.backgroundDark};\n    color: ${props => props.theme.colors.textLight};\n    cursor: not-allowed;\n  }\n`;\n\nconst getVoteTitle = (voteType: VoteType): string => {\n  const titles = {\n    [VoteType.ELIMINATION]: '投票淘汰',\n    [VoteType.WEREWOLF_KILL]: '狼人杀人',\n    [VoteType.SEER_CHECK]: '预言家查验',\n    [VoteType.WITCH_SAVE]: '女巫救人',\n    [VoteType.WITCH_POISON]: '女巫毒人',\n    [VoteType.GUARD_PROTECT]: '守卫保护'\n  };\n  return titles[voteType] || '选择目标';\n};\n\nconst getVoteDescription = (voteType: VoteType): string => {\n  const descriptions = {\n    [VoteType.ELIMINATION]: '选择要投票淘汰的玩家，或选择弃权',\n    [VoteType.WEREWOLF_KILL]: '选择要杀害的玩家',\n    [VoteType.SEER_CHECK]: '选择要查验身份的玩家',\n    [VoteType.WITCH_SAVE]: '选择要救治的玩家',\n    [VoteType.WITCH_POISON]: '选择要毒杀的玩家',\n    [VoteType.GUARD_PROTECT]: '选择要保护的玩家'\n  };\n  return descriptions[voteType] || '请选择一个目标';\n};\n\nconst VoteDialog: React.FC<VoteDialogProps> = ({\n  isOpen,\n  players,\n  voteType,\n  onClose,\n  onConfirm\n}) => {\n  const [selectedPlayerId, setSelectedPlayerId] = useState<number | null>(null);\n  const [reason, setReason] = useState('');\n\n  const playerList = Object.values(players);\n  const alivePlayers = playerList.filter(p => p.status === 'ALIVE');\n  const showReasonInput = voteType === VoteType.ELIMINATION;\n\n  const handlePlayerSelect = (playerId: number) => {\n    const player = players[playerId];\n    if (player && player.status === 'ALIVE') {\n      setSelectedPlayerId(playerId);\n    }\n  };\n\n  const handleConfirm = () => {\n    onConfirm(selectedPlayerId, reason.trim() || undefined);\n    handleClose();\n  };\n\n  const handleAbstain = () => {\n    onConfirm(null, '选择弃权');\n    handleClose();\n  };\n\n  const handleClose = () => {\n    setSelectedPlayerId(null);\n    setReason('');\n    onClose();\n  };\n\n  const handleOverlayClick = (e: React.MouseEvent) => {\n    if (e.target === e.currentTarget) {\n      handleClose();\n    }\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <Overlay isOpen={isOpen} onClick={handleOverlayClick}>\n      <DialogContainer>\n        <DialogHeader>\n          <DialogTitle>{getVoteTitle(voteType)}</DialogTitle>\n          <DialogDescription>\n            {getVoteDescription(voteType)}\n          </DialogDescription>\n        </DialogHeader>\n\n        <DialogBody>\n          <PlayerGrid>\n            {alivePlayers.map(player => (\n              <PlayerOption\n                key={player.player_id}\n                isSelected={selectedPlayerId === player.player_id}\n                isAlive={player.status === 'ALIVE'}\n                onClick={() => handlePlayerSelect(player.player_id)}\n              >\n                <PlayerInfo>\n                  <PlayerName>{player.name}</PlayerName>\n                  <PlayerStatus isAlive={player.status === 'ALIVE'}>\n                    {player.status === 'ALIVE' ? '存活' : '死亡'}\n                  </PlayerStatus>\n                </PlayerInfo>\n                <StatusIndicator isAlive={player.status === 'ALIVE'} />\n              </PlayerOption>\n            ))}\n          </PlayerGrid>\n\n          {showReasonInput && (\n            <ReasonSection>\n              <ReasonLabel htmlFor=\"vote-reason\">\n                投票理由（可选）\n              </ReasonLabel>\n              <ReasonInput\n                id=\"vote-reason\"\n                value={reason}\n                onChange={(e) => setReason(e.target.value)}\n                placeholder=\"请输入投票理由...\"\n                maxLength={200}\n              />\n            </ReasonSection>\n          )}\n        </DialogBody>\n\n        <DialogFooter>\n          <Button variant=\"secondary\" onClick={handleClose}>\n            取消\n          </Button>\n\n          {voteType === VoteType.ELIMINATION && (\n            <Button variant=\"danger\" onClick={handleAbstain}>\n              弃权\n            </Button>\n          )}\n\n          <Button\n            variant=\"primary\"\n            onClick={handleConfirm}\n            disabled={!selectedPlayerId}\n          >\n            确认\n          </Button>\n        </DialogFooter>\n      </DialogContainer>\n    </Overlay>\n  );\n};\n\nexport default VoteDialog;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAAiBC,QAAQ,QAAQ,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAW/C,MAAMC,OAAO,GAAGJ,MAAM,CAACK,GAAwB;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA,aAAaC,KAAK,IAAIA,KAAK,CAACC,MAAM,GAAG,MAAM,GAAG,MAAM;AACpD;AACA;AACA,aAAaD,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACC,MAAM,CAACC,KAAK;AAC9C,CAAC;AAACC,EAAA,GAXIP,OAAO;AAab,MAAMQ,eAAe,GAAGZ,MAAM,CAACK,GAAG;AAClC,gBAAgBC,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACC,eAAe;AAC3D,mBAAmBR,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACO,YAAY,CAACC,EAAE;AACvD,gBAAgBV,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACS,OAAO,CAACD,EAAE;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACE,GAAA,GAVIN,eAAe;AAYrB,MAAMO,YAAY,GAAGnB,MAAM,CAACK,GAAG;AAC/B,aAAaC,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACY,OAAO,CAACC,EAAE;AAC5C,6BAA6Bf,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACS,MAAM;AAC/D,gBAAgBhB,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACU,UAAU;AACtD,CAAC;AAACC,GAAA,GAJIL,YAAY;AAMlB,MAAMM,WAAW,GAAGzB,MAAM,CAAC0B,EAAE;AAC7B,eAAepB,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACmB,SAAS,CAACX,EAAE;AAChD,iBAAiBV,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACoB,WAAW,CAACC,QAAQ;AAC1D,WAAWvB,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACiB,IAAI;AAC3C,gBAAgBxB,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACY,OAAO,CAACW,EAAE;AAC/C,CAAC;AAACC,GAAA,GALIP,WAAW;AAOjB,MAAMQ,iBAAiB,GAAGjC,MAAM,CAACkC,CAAC;AAClC,eAAe5B,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACmB,SAAS,CAACQ,EAAE;AAChD,WAAW7B,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACuB,SAAS;AAChD;AACA,iBAAiB9B,KAAK,IAAIA,KAAK,CAACE,KAAK,CAAC6B,WAAW,CAACC,OAAO;AACzD,CAAC;AAACC,GAAA,GALIN,iBAAiB;AAOvB,MAAMO,UAAU,GAAGxC,MAAM,CAACK,GAAG;AAC7B,aAAaC,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACY,OAAO,CAACC,EAAE;AAC5C;AACA;AACA,CAAC;AAACoB,GAAA,GAJID,UAAU;AAMhB,MAAME,UAAU,GAAG1C,MAAM,CAACK,GAAG;AAC7B;AACA;AACA,SAASC,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACY,OAAO,CAACW,EAAE;AACxC,mBAAmBzB,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACY,OAAO,CAACC,EAAE;AAClD,CAAC;AAACsB,GAAA,GALID,UAAU;AAOhB,MAAME,YAAY,GAAG5C,MAAM,CAACK,GAA8C;AAC1E;AACA;AACA,aAAaC,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACY,OAAO,CAACe,EAAE;AAC5C,sBAAsB7B,KAAK,IAAIA,KAAK,CAACuC,UAAU,GAAGvC,KAAK,CAACE,KAAK,CAACK,MAAM,CAACiC,OAAO,GAAGxC,KAAK,CAACE,KAAK,CAACK,MAAM,CAACS,MAAM;AACxG,mBAAmBhB,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACO,YAAY,CAACM,EAAE;AACvD,gBAAgBf,KAAK,IAAIA,KAAK,CAACuC,UAAU,GAAGvC,KAAK,CAACE,KAAK,CAACK,MAAM,CAACiC,OAAO,GAAG,IAAI,GAAGxC,KAAK,CAACE,KAAK,CAACK,MAAM,CAACC,eAAe;AAClH,YAAYR,KAAK,IAAIA,KAAK,CAACyC,OAAO,GAAG,SAAS,GAAG,aAAa;AAC9D,aAAazC,KAAK,IAAIA,KAAK,CAACyC,OAAO,GAAG,CAAC,GAAG,GAAG;AAC7C,gBAAgBzC,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACwC,WAAW,CAACC,IAAI;AACrD;AACA;AACA,oBAAoB3C,KAAK,IAAIA,KAAK,CAACyC,OAAO,GAAGzC,KAAK,CAACE,KAAK,CAACK,MAAM,CAACiC,OAAO,GAAGxC,KAAK,CAACE,KAAK,CAACK,MAAM,CAACS,MAAM;AACnG,kBAAkBhB,KAAK,IAAIA,KAAK,CAACyC,OAAO,GAAGzC,KAAK,CAACE,KAAK,CAACK,MAAM,CAACiC,OAAO,GAAG,IAAI,GAAGxC,KAAK,CAACE,KAAK,CAACK,MAAM,CAACC,eAAe;AACjH;AACA,CAAC;AAACoC,GAAA,GAfIN,YAAY;AAiBlB,MAAMO,UAAU,GAAGnD,MAAM,CAACK,GAAG;AAC7B;AACA,CAAC;AAAC+C,GAAA,GAFID,UAAU;AAIhB,MAAME,UAAU,GAAGrD,MAAM,CAACK,GAAG;AAC7B,eAAeC,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACmB,SAAS,CAACQ,EAAE;AAChD,iBAAiB7B,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACoB,WAAW,CAAC0B,MAAM;AACxD,WAAWhD,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACiB,IAAI;AAC3C,mBAAmBxB,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACY,OAAO,CAACmC,EAAE;AAClD,CAAC;AAACC,GAAA,GALIH,UAAU;AAOhB,MAAMI,YAAY,GAAGzD,MAAM,CAACK,GAAyB;AACrD,eAAeC,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACmB,SAAS,CAACI,EAAE;AAChD,WAAWzB,KAAK,IAAIA,KAAK,CAACyC,OAAO,GAAGzC,KAAK,CAACE,KAAK,CAACK,MAAM,CAAC6C,OAAO,GAAGpD,KAAK,CAACE,KAAK,CAACK,MAAM,CAAC8C,MAAM;AAC1F,CAAC;AAACC,GAAA,GAHIH,YAAY;AAKlB,MAAMI,eAAe,GAAG7D,MAAM,CAACK,GAAyB;AACxD;AACA;AACA;AACA,gBAAgBC,KAAK,IAAIA,KAAK,CAACyC,OAAO,GAAGzC,KAAK,CAACE,KAAK,CAACK,MAAM,CAAC6C,OAAO,GAAGpD,KAAK,CAACE,KAAK,CAACK,MAAM,CAAC8C,MAAM;AAC/F,iBAAiBrD,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACY,OAAO,CAACe,EAAE;AAChD,CAAC;AAAC2B,IAAA,GANID,eAAe;AAQrB,MAAME,aAAa,GAAG/D,MAAM,CAACK,GAAG;AAChC,mBAAmBC,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACY,OAAO,CAACC,EAAE;AAClD,CAAC;AAAC2C,IAAA,GAFID,aAAa;AAInB,MAAME,WAAW,GAAGjE,MAAM,CAACkE,KAAK;AAChC;AACA,eAAe5D,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACmB,SAAS,CAACQ,EAAE;AAChD,iBAAiB7B,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACoB,WAAW,CAAC0B,MAAM;AACxD,WAAWhD,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACiB,IAAI;AAC3C,mBAAmBxB,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACY,OAAO,CAACW,EAAE;AAClD,CAAC;AAACoC,IAAA,GANIF,WAAW;AAQjB,MAAMG,WAAW,GAAGpE,MAAM,CAACqE,QAAQ;AACnC;AACA,aAAa/D,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACY,OAAO,CAACe,EAAE;AAC5C,sBAAsB7B,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACS,MAAM;AACxD,mBAAmBhB,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACO,YAAY,CAACoB,EAAE;AACvD,eAAe7B,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACmB,SAAS,CAACQ,EAAE;AAChD,iBAAiB7B,KAAK,IAAIA,KAAK,CAACE,KAAK,CAAC8D,KAAK,CAACxB,OAAO;AACnD;AACA;AACA,gBAAgBxC,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACwC,WAAW,CAACC,IAAI;AACrD;AACA;AACA;AACA,oBAAoB3C,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACiC,OAAO;AACvD,4BAA4BxC,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACiC,OAAO;AAC/D;AACA;AACA;AACA,aAAaxC,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACuB,SAAS;AAClD;AACA,CAAC;AAACmC,IAAA,GApBIH,WAAW;AAsBjB,MAAMI,YAAY,GAAGxE,MAAM,CAACK,GAAG;AAC/B,aAAaC,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACY,OAAO,CAACC,EAAE;AAC5C,0BAA0Bf,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACS,MAAM;AAC5D;AACA,SAAShB,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACY,OAAO,CAACe,EAAE;AACxC;AACA,CAAC;AAACsC,IAAA,GANID,YAAY;AAQlB,MAAME,MAAM,GAAG1E,MAAM,CAAC2E,MAAwD;AAC9E,aAAarE,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACY,OAAO,CAACe,EAAE,IAAI7B,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACY,OAAO,CAACC,EAAE;AAC/E,mBAAmBf,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACO,YAAY,CAACoB,EAAE;AACvD,eAAe7B,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACmB,SAAS,CAACQ,EAAE;AAChD,iBAAiB7B,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACoB,WAAW,CAAC0B,MAAM;AACxD;AACA;AACA,gBAAgBhD,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACwC,WAAW,CAACC,IAAI;AACrD;AACA;AACA,IAAI3C,KAAK,IAAI;EACT,QAAQA,KAAK,CAACsE,OAAO;IACnB,KAAK,QAAQ;MACX,OAAO;AACf,wBAAwBtE,KAAK,CAACE,KAAK,CAACK,MAAM,CAAC8C,MAAM;AACjD,mBAAmBrD,KAAK,CAACE,KAAK,CAACK,MAAM,CAACgE,SAAS;AAC/C,iDAAiDvE,KAAK,CAACE,KAAK,CAACK,MAAM,CAACiE,WAAW;AAC/E,SAAS;IACH,KAAK,WAAW;MACd,OAAO;AACf,wBAAwBxE,KAAK,CAACE,KAAK,CAACK,MAAM,CAACkE,cAAc;AACzD,mBAAmBzE,KAAK,CAACE,KAAK,CAACK,MAAM,CAACiB,IAAI;AAC1C,iDAAiDxB,KAAK,CAACE,KAAK,CAACK,MAAM,CAACS,MAAM;AAC1E,SAAS;IACH;MACE,OAAO;AACf,wBAAwBhB,KAAK,CAACE,KAAK,CAACK,MAAM,CAACiC,OAAO;AAClD,mBAAmBxC,KAAK,CAACE,KAAK,CAACK,MAAM,CAACgE,SAAS;AAC/C,iDAAiDvE,KAAK,CAACE,KAAK,CAACK,MAAM,CAACmE,YAAY;AAChF,SAAS;EACL;AACF,CAAC;AACH;AACA;AACA,kBAAkB1E,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACkE,cAAc;AAC5D,aAAazE,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACuB,SAAS;AAClD;AACA;AACA,CAAC;AAAC6C,IAAA,GAtCIP,MAAM;AAwCZ,MAAMQ,YAAY,GAAIC,QAAkB,IAAa;EACnD,MAAMC,MAAM,GAAG;IACb,CAACnF,QAAQ,CAACoF,WAAW,GAAG,MAAM;IAC9B,CAACpF,QAAQ,CAACqF,aAAa,GAAG,MAAM;IAChC,CAACrF,QAAQ,CAACsF,UAAU,GAAG,OAAO;IAC9B,CAACtF,QAAQ,CAACuF,UAAU,GAAG,MAAM;IAC7B,CAACvF,QAAQ,CAACwF,YAAY,GAAG,MAAM;IAC/B,CAACxF,QAAQ,CAACyF,aAAa,GAAG;EAC5B,CAAC;EACD,OAAON,MAAM,CAACD,QAAQ,CAAC,IAAI,MAAM;AACnC,CAAC;AAED,MAAMQ,kBAAkB,GAAIR,QAAkB,IAAa;EACzD,MAAMS,YAAY,GAAG;IACnB,CAAC3F,QAAQ,CAACoF,WAAW,GAAG,kBAAkB;IAC1C,CAACpF,QAAQ,CAACqF,aAAa,GAAG,UAAU;IACpC,CAACrF,QAAQ,CAACsF,UAAU,GAAG,YAAY;IACnC,CAACtF,QAAQ,CAACuF,UAAU,GAAG,UAAU;IACjC,CAACvF,QAAQ,CAACwF,YAAY,GAAG,UAAU;IACnC,CAACxF,QAAQ,CAACyF,aAAa,GAAG;EAC5B,CAAC;EACD,OAAOE,YAAY,CAACT,QAAQ,CAAC,IAAI,SAAS;AAC5C,CAAC;AAED,MAAMU,UAAqC,GAAGA,CAAC;EAC7CtF,MAAM;EACNuF,OAAO;EACPX,QAAQ;EACRY,OAAO;EACPC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpG,QAAQ,CAAgB,IAAI,CAAC;EAC7E,MAAM,CAACqG,MAAM,EAAEC,SAAS,CAAC,GAAGtG,QAAQ,CAAC,EAAE,CAAC;EAExC,MAAMuG,UAAU,GAAGC,MAAM,CAACC,MAAM,CAACV,OAAO,CAAC;EACzC,MAAMW,YAAY,GAAGH,UAAU,CAACI,MAAM,CAACxE,CAAC,IAAIA,CAAC,CAACyE,MAAM,KAAK,OAAO,CAAC;EACjE,MAAMC,eAAe,GAAGzB,QAAQ,KAAKlF,QAAQ,CAACoF,WAAW;EAEzD,MAAMwB,kBAAkB,GAAIC,QAAgB,IAAK;IAC/C,MAAMC,MAAM,GAAGjB,OAAO,CAACgB,QAAQ,CAAC;IAChC,IAAIC,MAAM,IAAIA,MAAM,CAACJ,MAAM,KAAK,OAAO,EAAE;MACvCR,mBAAmB,CAACW,QAAQ,CAAC;IAC/B;EACF,CAAC;EAED,MAAME,aAAa,GAAGA,CAAA,KAAM;IAC1BhB,SAAS,CAACE,gBAAgB,EAAEE,MAAM,CAACa,IAAI,CAAC,CAAC,IAAIC,SAAS,CAAC;IACvDC,WAAW,CAAC,CAAC;EACf,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1BpB,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC;IACvBmB,WAAW,CAAC,CAAC;EACf,CAAC;EAED,MAAMA,WAAW,GAAGA,CAAA,KAAM;IACxBhB,mBAAmB,CAAC,IAAI,CAAC;IACzBE,SAAS,CAAC,EAAE,CAAC;IACbN,OAAO,CAAC,CAAC;EACX,CAAC;EAED,MAAMsB,kBAAkB,GAAIC,CAAmB,IAAK;IAClD,IAAIA,CAAC,CAACC,MAAM,KAAKD,CAAC,CAACE,aAAa,EAAE;MAChCL,WAAW,CAAC,CAAC;IACf;EACF,CAAC;EAED,IAAI,CAAC5G,MAAM,EAAE,OAAO,IAAI;EAExB,oBACEJ,OAAA,CAACC,OAAO;IAACG,MAAM,EAAEA,MAAO;IAACkH,OAAO,EAAEJ,kBAAmB;IAAAK,QAAA,eACnDvH,OAAA,CAACS,eAAe;MAAA8G,QAAA,gBACdvH,OAAA,CAACgB,YAAY;QAAAuG,QAAA,gBACXvH,OAAA,CAACsB,WAAW;UAAAiG,QAAA,EAAExC,YAAY,CAACC,QAAQ;QAAC;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC,eACnD3H,OAAA,CAAC8B,iBAAiB;UAAAyF,QAAA,EACf/B,kBAAkB,CAACR,QAAQ;QAAC;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAEf3H,OAAA,CAACqC,UAAU;QAAAkF,QAAA,gBACTvH,OAAA,CAACuC,UAAU;UAAAgF,QAAA,EACRjB,YAAY,CAACsB,GAAG,CAAChB,MAAM,iBACtB5G,OAAA,CAACyC,YAAY;YAEXC,UAAU,EAAEqD,gBAAgB,KAAKa,MAAM,CAACiB,SAAU;YAClDjF,OAAO,EAAEgE,MAAM,CAACJ,MAAM,KAAK,OAAQ;YACnCc,OAAO,EAAEA,CAAA,KAAMZ,kBAAkB,CAACE,MAAM,CAACiB,SAAS,CAAE;YAAAN,QAAA,gBAEpDvH,OAAA,CAACgD,UAAU;cAAAuE,QAAA,gBACTvH,OAAA,CAACkD,UAAU;gBAAAqE,QAAA,EAAEX,MAAM,CAACkB;cAAI;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACtC3H,OAAA,CAACsD,YAAY;gBAACV,OAAO,EAAEgE,MAAM,CAACJ,MAAM,KAAK,OAAQ;gBAAAe,QAAA,EAC9CX,MAAM,CAACJ,MAAM,KAAK,OAAO,GAAG,IAAI,GAAG;cAAI;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACb3H,OAAA,CAAC0D,eAAe;cAACd,OAAO,EAAEgE,MAAM,CAACJ,MAAM,KAAK;YAAQ;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA,GAXlDf,MAAM,CAACiB,SAAS;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAYT,CACf;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC,EAEZlB,eAAe,iBACdzG,OAAA,CAAC4D,aAAa;UAAA2D,QAAA,gBACZvH,OAAA,CAAC8D,WAAW;YAACiE,OAAO,EAAC,aAAa;YAAAR,QAAA,EAAC;UAEnC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACd3H,OAAA,CAACiE,WAAW;YACV+D,EAAE,EAAC,aAAa;YAChBC,KAAK,EAAEhC,MAAO;YACdiC,QAAQ,EAAGf,CAAC,IAAKjB,SAAS,CAACiB,CAAC,CAACC,MAAM,CAACa,KAAK,CAAE;YAC3CE,WAAW,EAAC,+CAAY;YACxBC,SAAS,EAAE;UAAI;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACW,CAChB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,eAEb3H,OAAA,CAACqE,YAAY;QAAAkD,QAAA,gBACXvH,OAAA,CAACuE,MAAM;UAACE,OAAO,EAAC,WAAW;UAAC6C,OAAO,EAAEN,WAAY;UAAAO,QAAA,EAAC;QAElD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EAER3C,QAAQ,KAAKlF,QAAQ,CAACoF,WAAW,iBAChClF,OAAA,CAACuE,MAAM;UAACE,OAAO,EAAC,QAAQ;UAAC6C,OAAO,EAAEL,aAAc;UAAAM,QAAA,EAAC;QAEjD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT,eAED3H,OAAA,CAACuE,MAAM;UACLE,OAAO,EAAC,SAAS;UACjB6C,OAAO,EAAET,aAAc;UACvBwB,QAAQ,EAAE,CAACtC,gBAAiB;UAAAwB,QAAA,EAC7B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACX,CAAC;AAEd,CAAC;AAAC7B,EAAA,CAjHIJ,UAAqC;AAAA4C,IAAA,GAArC5C,UAAqC;AAmH3C,eAAeA,UAAU;AAAC,IAAAlF,EAAA,EAAAO,GAAA,EAAAM,GAAA,EAAAQ,GAAA,EAAAO,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAO,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAI,GAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAI,IAAA,EAAAE,IAAA,EAAAQ,IAAA,EAAAwD,IAAA;AAAAC,YAAA,CAAA/H,EAAA;AAAA+H,YAAA,CAAAxH,GAAA;AAAAwH,YAAA,CAAAlH,GAAA;AAAAkH,YAAA,CAAA1G,GAAA;AAAA0G,YAAA,CAAAnG,GAAA;AAAAmG,YAAA,CAAAjG,GAAA;AAAAiG,YAAA,CAAA/F,GAAA;AAAA+F,YAAA,CAAAxF,GAAA;AAAAwF,YAAA,CAAAtF,GAAA;AAAAsF,YAAA,CAAAlF,GAAA;AAAAkF,YAAA,CAAA9E,GAAA;AAAA8E,YAAA,CAAA5E,IAAA;AAAA4E,YAAA,CAAA1E,IAAA;AAAA0E,YAAA,CAAAvE,IAAA;AAAAuE,YAAA,CAAAnE,IAAA;AAAAmE,YAAA,CAAAjE,IAAA;AAAAiE,YAAA,CAAAzD,IAAA;AAAAyD,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}