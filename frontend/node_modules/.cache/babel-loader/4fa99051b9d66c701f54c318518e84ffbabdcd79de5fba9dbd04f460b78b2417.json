{"ast": null, "code": "// 主题配置\nexport const theme = {\n  colors: {\n    // 主色调\n    primary: '#2C3E50',\n    primaryLight: '#34495E',\n    primaryDark: '#1A252F',\n    // 辅助色\n    secondary: '#E67E22',\n    secondaryLight: '#F39C12',\n    secondaryDark: '#D35400',\n    // 背景色\n    background: '#ECF0F1',\n    backgroundDark: '#BDC3C7',\n    backgroundLight: '#FFFFFF',\n    // 文字色\n    text: '#2C3E50',\n    textLight: '#7F8C8D',\n    textWhite: '#FFFFFF',\n    // 状态色\n    success: '#27AE60',\n    successLight: '#2ECC71',\n    successDark: '#1E8449',\n    warning: '#F39C12',\n    warningLight: '#F1C40F',\n    warningDark: '#E67E22',\n    danger: '#E74C3C',\n    dangerLight: '#EC7063',\n    dangerDark: '#C0392B',\n    info: '#3498DB',\n    infoLight: '#5DADE2',\n    infoDark: '#2980B9',\n    // 角色颜色\n    villager: '#27AE60',\n    werewolf: '#E74C3C',\n    seer: '#3498DB',\n    witch: '#9B59B6',\n    guard: '#F39C12',\n    hunter: '#8B4513',\n    // 边框色\n    border: '#BDC3C7',\n    borderLight: '#D5DBDB',\n    borderDark: '#85929E',\n    // 阴影色\n    shadow: 'rgba(52, 73, 94, 0.1)',\n    shadowDark: 'rgba(52, 73, 94, 0.2)'\n  },\n  fonts: {\n    primary: '\"Microsoft YaHei\", \"PingFang SC\", \"Helvetica Neue\", Arial, sans-serif',\n    mono: '\"Fira Code\", \"Monaco\", \"Consolas\", monospace'\n  },\n  fontSizes: {\n    xs: '0.75rem',\n    // 12px\n    sm: '0.875rem',\n    // 14px\n    md: '1rem',\n    // 16px\n    lg: '1.125rem',\n    // 18px\n    xl: '1.25rem',\n    // 20px\n    '2xl': '1.5rem',\n    // 24px\n    '3xl': '1.875rem',\n    // 30px\n    '4xl': '2.25rem' // 36px\n  },\n  fontWeights: {\n    normal: 400,\n    medium: 500,\n    semibold: 600,\n    bold: 700\n  },\n  lineHeights: {\n    tight: 1.25,\n    normal: 1.5,\n    relaxed: 1.75\n  },\n  spacing: {\n    xs: '0.25rem',\n    // 4px\n    sm: '0.5rem',\n    // 8px\n    md: '1rem',\n    // 16px\n    lg: '1.5rem',\n    // 24px\n    xl: '2rem',\n    // 32px\n    '2xl': '3rem',\n    // 48px\n    '3xl': '4rem',\n    // 64px\n    '4xl': '6rem' // 96px\n  },\n  borderRadius: {\n    none: '0',\n    sm: '0.125rem',\n    // 2px\n    md: '0.375rem',\n    // 6px\n    lg: '0.5rem',\n    // 8px\n    xl: '0.75rem',\n    // 12px\n    '2xl': '1rem',\n    // 16px\n    full: '9999px'\n  },\n  shadows: {\n    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',\n    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',\n    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',\n    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'\n  },\n  breakpoints: {\n    sm: '640px',\n    md: '768px',\n    lg: '1024px',\n    xl: '1280px',\n    '2xl': '1536px'\n  },\n  transitions: {\n    fast: '150ms ease-in-out',\n    normal: '300ms ease-in-out',\n    slow: '500ms ease-in-out'\n  },\n  zIndex: {\n    dropdown: 1000,\n    sticky: 1020,\n    fixed: 1030,\n    modal: 1040,\n    popover: 1050,\n    tooltip: 1060\n  }\n};\n\n// 角色颜色映射\nexport const getRoleColor = role => {\n  const roleColors = {\n    VILLAGER: theme.colors.villager,\n    WEREWOLF: theme.colors.werewolf,\n    SEER: theme.colors.seer,\n    WITCH: theme.colors.witch,\n    GUARD: theme.colors.guard,\n    HUNTER: theme.colors.hunter\n  };\n  return roleColors[role.toUpperCase()] || theme.colors.text;\n};\n\n// 阶段颜色映射\nexport const getPhaseColor = phase => {\n  const phaseColors = {\n    SETUP: theme.colors.info,\n    NIGHT: theme.colors.primaryDark,\n    DAY_DISCUSSION: theme.colors.warning,\n    DAY_VOTING: theme.colors.danger,\n    GAME_OVER: theme.colors.success\n  };\n  return phaseColors[phase] || theme.colors.text;\n};", "map": {"version": 3, "names": ["theme", "colors", "primary", "primaryLight", "primaryDark", "secondary", "secondaryLight", "secondaryDark", "background", "backgroundDark", "backgroundLight", "text", "textLight", "textWhite", "success", "successLight", "successDark", "warning", "warningLight", "warningDark", "danger", "dangerLight", "dangerDark", "info", "infoLight", "infoDark", "villager", "werewolf", "seer", "witch", "guard", "hunter", "border", "borderLight", "borderDark", "shadow", "shadowDark", "fonts", "mono", "fontSizes", "xs", "sm", "md", "lg", "xl", "fontWeights", "normal", "medium", "semibold", "bold", "lineHeights", "tight", "relaxed", "spacing", "borderRadius", "none", "full", "shadows", "breakpoints", "transitions", "fast", "slow", "zIndex", "dropdown", "sticky", "fixed", "modal", "popover", "tooltip", "getRoleColor", "role", "roleColors", "VILLAGER", "WEREWOLF", "SEER", "WITCH", "GUARD", "HUNTER", "toUpperCase", "getPhaseColor", "phase", "phaseColors", "SETUP", "NIGHT", "DAY_DISCUSSION", "DAY_VOTING", "GAME_OVER"], "sources": ["/private/peiwy/work/ai_projects/wolfkill/frontend/src/styles/theme.ts"], "sourcesContent": ["// 主题配置\nexport const theme = {\n  colors: {\n    // 主色调\n    primary: '#2C3E50',\n    primaryLight: '#34495E',\n    primaryDark: '#1A252F',\n\n    // 辅助色\n    secondary: '#E67E22',\n    secondaryLight: '#F39C12',\n    secondaryDark: '#D35400',\n\n    // 背景色\n    background: '#ECF0F1',\n    backgroundDark: '#BDC3C7',\n    backgroundLight: '#FFFFFF',\n\n    // 文字色\n    text: '#2C3E50',\n    textLight: '#7F8C8D',\n    textWhite: '#FFFFFF',\n\n    // 状态色\n    success: '#27AE60',\n    successLight: '#2ECC71',\n    successDark: '#1E8449',\n\n    warning: '#F39C12',\n    warningLight: '#F1C40F',\n    warningDark: '#E67E22',\n\n    danger: '#E74C3C',\n    dangerLight: '#EC7063',\n    dangerDark: '#C0392B',\n\n    info: '#3498DB',\n    infoLight: '#5DADE2',\n    infoDark: '#2980B9',\n\n    // 角色颜色\n    villager: '#27AE60',\n    werewolf: '#E74C3C',\n    seer: '#3498DB',\n    witch: '#9B59B6',\n    guard: '#F39C12',\n    hunter: '#8B4513',\n\n    // 边框色\n    border: '#BDC3C7',\n    borderLight: '#D5DBDB',\n    borderDark: '#85929E',\n\n    // 阴影色\n    shadow: 'rgba(52, 73, 94, 0.1)',\n    shadowDark: 'rgba(52, 73, 94, 0.2)'\n  },\n\n  fonts: {\n    primary: '\"Microsoft YaHei\", \"PingFang SC\", \"Helvetica Neue\", Arial, sans-serif',\n    mono: '\"Fira Code\", \"Monaco\", \"Consolas\", monospace'\n  },\n\n  fontSizes: {\n    xs: '0.75rem',    // 12px\n    sm: '0.875rem',   // 14px\n    md: '1rem',       // 16px\n    lg: '1.125rem',   // 18px\n    xl: '1.25rem',    // 20px\n    '2xl': '1.5rem',  // 24px\n    '3xl': '1.875rem', // 30px\n    '4xl': '2.25rem'  // 36px\n  },\n\n  fontWeights: {\n    normal: 400,\n    medium: 500,\n    semibold: 600,\n    bold: 700\n  },\n\n  lineHeights: {\n    tight: 1.25,\n    normal: 1.5,\n    relaxed: 1.75\n  },\n\n  spacing: {\n    xs: '0.25rem',   // 4px\n    sm: '0.5rem',    // 8px\n    md: '1rem',      // 16px\n    lg: '1.5rem',    // 24px\n    xl: '2rem',      // 32px\n    '2xl': '3rem',   // 48px\n    '3xl': '4rem',   // 64px\n    '4xl': '6rem'    // 96px\n  },\n\n  borderRadius: {\n    none: '0',\n    sm: '0.125rem',   // 2px\n    md: '0.375rem',   // 6px\n    lg: '0.5rem',     // 8px\n    xl: '0.75rem',    // 12px\n    '2xl': '1rem',    // 16px\n    full: '9999px'\n  },\n\n  shadows: {\n    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',\n    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',\n    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',\n    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'\n  },\n\n  breakpoints: {\n    sm: '640px',\n    md: '768px',\n    lg: '1024px',\n    xl: '1280px',\n    '2xl': '1536px'\n  },\n\n  transitions: {\n    fast: '150ms ease-in-out',\n    normal: '300ms ease-in-out',\n    slow: '500ms ease-in-out'\n  },\n\n  zIndex: {\n    dropdown: 1000,\n    sticky: 1020,\n    fixed: 1030,\n    modal: 1040,\n    popover: 1050,\n    tooltip: 1060\n  }\n};\n\n// 角色颜色映射\nexport const getRoleColor = (role: string): string => {\n  const roleColors: { [key: string]: string } = {\n    VILLAGER: theme.colors.villager,\n    WEREWOLF: theme.colors.werewolf,\n    SEER: theme.colors.seer,\n    WITCH: theme.colors.witch,\n    GUARD: theme.colors.guard,\n    HUNTER: theme.colors.hunter\n  };\n  return roleColors[role.toUpperCase()] || theme.colors.text;\n};\n\n// 阶段颜色映射\nexport const getPhaseColor = (phase: string): string => {\n  const phaseColors: { [key: string]: string } = {\n    SETUP: theme.colors.info,\n    NIGHT: theme.colors.primaryDark,\n    DAY_DISCUSSION: theme.colors.warning,\n    DAY_VOTING: theme.colors.danger,\n    GAME_OVER: theme.colors.success\n  };\n  return phaseColors[phase] || theme.colors.text;\n};\n\nexport type Theme = typeof theme;"], "mappings": "AAAA;AACA,OAAO,MAAMA,KAAK,GAAG;EACnBC,MAAM,EAAE;IACN;IACAC,OAAO,EAAE,SAAS;IAClBC,YAAY,EAAE,SAAS;IACvBC,WAAW,EAAE,SAAS;IAEtB;IACAC,SAAS,EAAE,SAAS;IACpBC,cAAc,EAAE,SAAS;IACzBC,aAAa,EAAE,SAAS;IAExB;IACAC,UAAU,EAAE,SAAS;IACrBC,cAAc,EAAE,SAAS;IACzBC,eAAe,EAAE,SAAS;IAE1B;IACAC,IAAI,EAAE,SAAS;IACfC,SAAS,EAAE,SAAS;IACpBC,SAAS,EAAE,SAAS;IAEpB;IACAC,OAAO,EAAE,SAAS;IAClBC,YAAY,EAAE,SAAS;IACvBC,WAAW,EAAE,SAAS;IAEtBC,OAAO,EAAE,SAAS;IAClBC,YAAY,EAAE,SAAS;IACvBC,WAAW,EAAE,SAAS;IAEtBC,MAAM,EAAE,SAAS;IACjBC,WAAW,EAAE,SAAS;IACtBC,UAAU,EAAE,SAAS;IAErBC,IAAI,EAAE,SAAS;IACfC,SAAS,EAAE,SAAS;IACpBC,QAAQ,EAAE,SAAS;IAEnB;IACAC,QAAQ,EAAE,SAAS;IACnBC,QAAQ,EAAE,SAAS;IACnBC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,SAAS;IAChBC,KAAK,EAAE,SAAS;IAChBC,MAAM,EAAE,SAAS;IAEjB;IACAC,MAAM,EAAE,SAAS;IACjBC,WAAW,EAAE,SAAS;IACtBC,UAAU,EAAE,SAAS;IAErB;IACAC,MAAM,EAAE,uBAAuB;IAC/BC,UAAU,EAAE;EACd,CAAC;EAEDC,KAAK,EAAE;IACLnC,OAAO,EAAE,uEAAuE;IAChFoC,IAAI,EAAE;EACR,CAAC;EAEDC,SAAS,EAAE;IACTC,EAAE,EAAE,SAAS;IAAK;IAClBC,EAAE,EAAE,UAAU;IAAI;IAClBC,EAAE,EAAE,MAAM;IAAQ;IAClBC,EAAE,EAAE,UAAU;IAAI;IAClBC,EAAE,EAAE,SAAS;IAAK;IAClB,KAAK,EAAE,QAAQ;IAAG;IAClB,KAAK,EAAE,UAAU;IAAE;IACnB,KAAK,EAAE,SAAS,CAAE;EACpB,CAAC;EAEDC,WAAW,EAAE;IACXC,MAAM,EAAE,GAAG;IACXC,MAAM,EAAE,GAAG;IACXC,QAAQ,EAAE,GAAG;IACbC,IAAI,EAAE;EACR,CAAC;EAEDC,WAAW,EAAE;IACXC,KAAK,EAAE,IAAI;IACXL,MAAM,EAAE,GAAG;IACXM,OAAO,EAAE;EACX,CAAC;EAEDC,OAAO,EAAE;IACPb,EAAE,EAAE,SAAS;IAAI;IACjBC,EAAE,EAAE,QAAQ;IAAK;IACjBC,EAAE,EAAE,MAAM;IAAO;IACjBC,EAAE,EAAE,QAAQ;IAAK;IACjBC,EAAE,EAAE,MAAM;IAAO;IACjB,KAAK,EAAE,MAAM;IAAI;IACjB,KAAK,EAAE,MAAM;IAAI;IACjB,KAAK,EAAE,MAAM,CAAI;EACnB,CAAC;EAEDU,YAAY,EAAE;IACZC,IAAI,EAAE,GAAG;IACTd,EAAE,EAAE,UAAU;IAAI;IAClBC,EAAE,EAAE,UAAU;IAAI;IAClBC,EAAE,EAAE,QAAQ;IAAM;IAClBC,EAAE,EAAE,SAAS;IAAK;IAClB,KAAK,EAAE,MAAM;IAAK;IAClBY,IAAI,EAAE;EACR,CAAC;EAEDC,OAAO,EAAE;IACPhB,EAAE,EAAE,iCAAiC;IACrCC,EAAE,EAAE,uEAAuE;IAC3EC,EAAE,EAAE,yEAAyE;IAC7EC,EAAE,EAAE;EACN,CAAC;EAEDc,WAAW,EAAE;IACXjB,EAAE,EAAE,OAAO;IACXC,EAAE,EAAE,OAAO;IACXC,EAAE,EAAE,QAAQ;IACZC,EAAE,EAAE,QAAQ;IACZ,KAAK,EAAE;EACT,CAAC;EAEDe,WAAW,EAAE;IACXC,IAAI,EAAE,mBAAmB;IACzBd,MAAM,EAAE,mBAAmB;IAC3Be,IAAI,EAAE;EACR,CAAC;EAEDC,MAAM,EAAE;IACNC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE,IAAI;IACZC,KAAK,EAAE,IAAI;IACXC,KAAK,EAAE,IAAI;IACXC,OAAO,EAAE,IAAI;IACbC,OAAO,EAAE;EACX;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,YAAY,GAAIC,IAAY,IAAa;EACpD,MAAMC,UAAqC,GAAG;IAC5CC,QAAQ,EAAExE,KAAK,CAACC,MAAM,CAACyB,QAAQ;IAC/B+C,QAAQ,EAAEzE,KAAK,CAACC,MAAM,CAAC0B,QAAQ;IAC/B+C,IAAI,EAAE1E,KAAK,CAACC,MAAM,CAAC2B,IAAI;IACvB+C,KAAK,EAAE3E,KAAK,CAACC,MAAM,CAAC4B,KAAK;IACzB+C,KAAK,EAAE5E,KAAK,CAACC,MAAM,CAAC6B,KAAK;IACzB+C,MAAM,EAAE7E,KAAK,CAACC,MAAM,CAAC8B;EACvB,CAAC;EACD,OAAOwC,UAAU,CAACD,IAAI,CAACQ,WAAW,CAAC,CAAC,CAAC,IAAI9E,KAAK,CAACC,MAAM,CAACU,IAAI;AAC5D,CAAC;;AAED;AACA,OAAO,MAAMoE,aAAa,GAAIC,KAAa,IAAa;EACtD,MAAMC,WAAsC,GAAG;IAC7CC,KAAK,EAAElF,KAAK,CAACC,MAAM,CAACsB,IAAI;IACxB4D,KAAK,EAAEnF,KAAK,CAACC,MAAM,CAACG,WAAW;IAC/BgF,cAAc,EAAEpF,KAAK,CAACC,MAAM,CAACgB,OAAO;IACpCoE,UAAU,EAAErF,KAAK,CAACC,MAAM,CAACmB,MAAM;IAC/BkE,SAAS,EAAEtF,KAAK,CAACC,MAAM,CAACa;EAC1B,CAAC;EACD,OAAOmE,WAAW,CAACD,KAAK,CAAC,IAAIhF,KAAK,CAACC,MAAM,CAACU,IAAI;AAChD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}