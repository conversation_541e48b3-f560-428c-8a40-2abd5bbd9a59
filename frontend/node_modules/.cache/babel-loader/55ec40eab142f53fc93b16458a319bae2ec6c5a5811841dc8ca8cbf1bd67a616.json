{"ast": null, "code": "var _jsxFileName = \"/private/peiwy/work/ai_projects/wolfkill/frontend/src/components/dialogs/ActionDialog.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { Role } from '../../types';\nimport { getRoleColor } from '../../styles/theme';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Overlay = styled.div`\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  display: ${props => props.isOpen ? 'flex' : 'none'};\n  justify-content: center;\n  align-items: center;\n  z-index: ${props => props.theme.zIndex.modal};\n`;\n_c = Overlay;\nconst DialogContainer = styled.div`\n  background: ${props => props.theme.colors.backgroundLight};\n  border-radius: ${props => props.theme.borderRadius.xl};\n  box-shadow: ${props => props.theme.shadows.xl};\n  width: 90%;\n  max-width: 600px;\n  max-height: 80vh;\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n`;\n_c2 = DialogContainer;\nconst DialogHeader = styled.div`\n  padding: ${props => props.theme.spacing.lg};\n  border-bottom: 1px solid ${props => props.theme.colors.border};\n  background: linear-gradient(135deg, ${props => props.roleColor}20, ${props => props.theme.colors.background});\n`;\n_c3 = DialogHeader;\nconst RoleIcon = styled.div`\n  font-size: 2rem;\n  margin-bottom: ${props => props.theme.spacing.sm};\n`;\n_c4 = RoleIcon;\nconst DialogTitle = styled.h2`\n  font-size: ${props => props.theme.fontSizes.xl};\n  font-weight: ${props => props.theme.fontWeights.semibold};\n  color: ${props => props.roleColor};\n  margin: 0 0 ${props => props.theme.spacing.sm} 0;\n`;\n_c5 = DialogTitle;\nconst DialogDescription = styled.p`\n  font-size: ${props => props.theme.fontSizes.md};\n  color: ${props => props.theme.colors.textLight};\n  margin: 0;\n  line-height: ${props => props.theme.lineHeights.relaxed};\n`;\n_c6 = DialogDescription;\nconst DialogBody = styled.div`\n  padding: ${props => props.theme.spacing.lg};\n  flex: 1;\n  overflow-y: auto;\n`;\n_c7 = DialogBody;\nconst ActionSection = styled.div`\n  margin-bottom: ${props => props.theme.spacing.xl};\n`;\n_c8 = ActionSection;\nconst SectionTitle = styled.h3`\n  font-size: ${props => props.theme.fontSizes.lg};\n  font-weight: ${props => props.theme.fontWeights.semibold};\n  color: ${props => props.theme.colors.text};\n  margin: 0 0 ${props => props.theme.spacing.md} 0;\n`;\n_c9 = SectionTitle;\nconst ActionGrid = styled.div`\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: ${props => props.theme.spacing.sm};\n`;\n_c0 = ActionGrid;\nconst ActionOption = styled.div`\n  display: flex;\n  align-items: center;\n  padding: ${props => props.theme.spacing.md};\n  border: 2px solid ${props => props.isSelected ? props.theme.colors.primary : props.theme.colors.border};\n  border-radius: ${props => props.theme.borderRadius.lg};\n  background: ${props => props.isSelected ? props.theme.colors.primary + '10' : props.theme.colors.backgroundLight};\n  cursor: ${props => props.isAvailable ? 'pointer' : 'not-allowed'};\n  opacity: ${props => props.isAvailable ? 1 : 0.5};\n  transition: ${props => props.theme.transitions.fast};\n\n  &:hover {\n    border-color: ${props => props.isAvailable ? props.theme.colors.primary : props.theme.colors.border};\n    background: ${props => props.isAvailable ? props.theme.colors.primary + '20' : props.theme.colors.backgroundLight};\n  }\n`;\n_c1 = ActionOption;\nconst ActionInfo = styled.div`\n  flex: 1;\n`;\n_c10 = ActionInfo;\nconst ActionName = styled.div`\n  font-size: ${props => props.theme.fontSizes.md};\n  font-weight: ${props => props.theme.fontWeights.medium};\n  color: ${props => props.theme.colors.text};\n  margin-bottom: ${props => props.theme.spacing.xs};\n`;\n_c11 = ActionName;\nconst ActionDescription = styled.div`\n  font-size: ${props => props.theme.fontSizes.sm};\n  color: ${props => props.theme.colors.textLight};\n  line-height: ${props => props.theme.lineHeights.relaxed};\n`;\n_c12 = ActionDescription;\nconst TargetSection = styled.div`\n  display: ${props => props.show ? 'block' : 'none'};\n  margin-bottom: ${props => props.theme.spacing.lg};\n`;\n_c13 = TargetSection;\nconst PlayerGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: ${props => props.theme.spacing.sm};\n`;\n_c14 = PlayerGrid;\nconst PlayerOption = styled.div`\n  display: flex;\n  align-items: center;\n  padding: ${props => props.theme.spacing.md};\n  border: 2px solid ${props => props.isSelected ? props.theme.colors.secondary : props.theme.colors.border};\n  border-radius: ${props => props.theme.borderRadius.lg};\n  background: ${props => props.isSelected ? props.theme.colors.secondary + '10' : props.theme.colors.backgroundLight};\n  cursor: ${props => props.isTargetable ? 'pointer' : 'not-allowed'};\n  opacity: ${props => props.isTargetable ? 1 : 0.5};\n  transition: ${props => props.theme.transitions.fast};\n\n  &:hover {\n    border-color: ${props => props.isTargetable ? props.theme.colors.secondary : props.theme.colors.border};\n    background: ${props => props.isTargetable ? props.theme.colors.secondary + '20' : props.theme.colors.backgroundLight};\n  }\n`;\n_c15 = PlayerOption;\nconst PlayerInfo = styled.div`\n  flex: 1;\n`;\n_c16 = PlayerInfo;\nconst PlayerName = styled.div`\n  font-size: ${props => props.theme.fontSizes.md};\n  font-weight: ${props => props.theme.fontWeights.medium};\n  color: ${props => props.theme.colors.text};\n  margin-bottom: ${props => props.theme.spacing.xs};\n`;\n_c17 = PlayerName;\nconst PlayerStatus = styled.div`\n  font-size: ${props => props.theme.fontSizes.sm};\n  color: ${props => props.isAlive ? props.theme.colors.success : props.theme.colors.danger};\n`;\n_c18 = PlayerStatus;\nconst StatusIndicator = styled.div`\n  width: 12px;\n  height: 12px;\n  border-radius: 50%;\n  background: ${props => props.isAlive ? props.theme.colors.success : props.theme.colors.danger};\n  margin-left: ${props => props.theme.spacing.md};\n`;\n_c19 = StatusIndicator;\nconst DialogFooter = styled.div`\n  padding: ${props => props.theme.spacing.lg};\n  border-top: 1px solid ${props => props.theme.colors.border};\n  display: flex;\n  gap: ${props => props.theme.spacing.md};\n  justify-content: flex-end;\n`;\n_c20 = DialogFooter;\nconst Button = styled.button`\n  padding: ${props => props.theme.spacing.md} ${props => props.theme.spacing.lg};\n  border-radius: ${props => props.theme.borderRadius.md};\n  font-size: ${props => props.theme.fontSizes.md};\n  font-weight: ${props => props.theme.fontWeights.medium};\n  border: none;\n  cursor: pointer;\n  transition: ${props => props.theme.transitions.fast};\n  min-width: 100px;\n\n  ${props => {\n  switch (props.variant) {\n    case 'warning':\n      return `\n          background: ${props.theme.colors.warning};\n          color: ${props.theme.colors.textWhite};\n          &:hover:not(:disabled) { background: ${props.theme.colors.warningLight}; }\n        `;\n    case 'secondary':\n      return `\n          background: ${props.theme.colors.backgroundDark};\n          color: ${props.theme.colors.text};\n          &:hover:not(:disabled) { background: ${props.theme.colors.border}; }\n        `;\n    default:\n      return `\n          background: ${props.theme.colors.primary};\n          color: ${props.theme.colors.textWhite};\n          &:hover:not(:disabled) { background: ${props.theme.colors.primaryLight}; }\n        `;\n  }\n}}\n\n  &:disabled {\n    background: ${props => props.theme.colors.backgroundDark};\n    color: ${props => props.theme.colors.textLight};\n    cursor: not-allowed;\n  }\n`;\n_c21 = Button;\nconst getRoleIcon = role => {\n  const roleIcons = {\n    [Role.VILLAGER]: '👨‍🌾',\n    [Role.WEREWOLF]: '🐺',\n    [Role.SEER]: '🔮',\n    [Role.WITCH]: '🧙‍♀️',\n    [Role.GUARD]: '🛡️',\n    [Role.HUNTER]: '🏹'\n  };\n  return roleIcons[role] || '❓';\n};\nconst getRoleName = role => {\n  const roleNames = {\n    [Role.VILLAGER]: '村民',\n    [Role.WEREWOLF]: '狼人',\n    [Role.SEER]: '预言家',\n    [Role.WITCH]: '女巫',\n    [Role.GUARD]: '守卫',\n    [Role.HUNTER]: '猎人'\n  };\n  return roleNames[role] || '未知角色';\n};\nconst getRoleDescription = role => {\n  const descriptions = {\n    [Role.VILLAGER]: '普通村民，白天参与讨论和投票',\n    [Role.WEREWOLF]: '夜晚杀人，白天伪装成村民',\n    [Role.SEER]: '每晚可以查验一个人的身份',\n    [Role.WITCH]: '拥有一瓶解药和一瓶毒药，各用一次',\n    [Role.GUARD]: '每晚可以保护一个人不被狼人杀死',\n    [Role.HUNTER]: '被淘汰时可以开枪带走一个人'\n  };\n  return descriptions[role] || '';\n};\nconst getActionInfo = actionType => {\n  const actionMap = {\n    'seer_check': {\n      name: '查验身份',\n      description: '查看目标玩家的真实身份（村民阵营或狼人阵营）'\n    },\n    'witch_save': {\n      name: '使用解药',\n      description: '救治被狼人杀害的玩家，每局游戏只能使用一次'\n    },\n    'witch_poison': {\n      name: '使用毒药',\n      description: '毒杀一个玩家，每局游戏只能使用一次'\n    },\n    'guard_protect': {\n      name: '保护玩家',\n      description: '保护目标玩家免受狼人攻击，不能连续两晚保护同一人'\n    },\n    'werewolf_kill': {\n      name: '杀害玩家',\n      description: '选择要杀害的目标，需要狼人阵营达成一致'\n    },\n    'hunter_shoot': {\n      name: '猎人开枪',\n      description: '被淘汰时可以开枪带走一个玩家'\n    },\n    'skip': {\n      name: '跳过行动',\n      description: '本回合不执行任何特殊行动'\n    }\n  };\n  return actionMap[actionType] || {\n    name: actionType,\n    description: '未知行动'\n  };\n};\nconst canTargetPlayer = (player, actionType, playerRole) => {\n  switch (actionType) {\n    case 'seer_check':\n    case 'guard_protect':\n      return player.status === 'ALIVE';\n    case 'werewolf_kill':\n      return player.status === 'ALIVE' && player.role !== Role.WEREWOLF;\n    case 'witch_poison':\n    case 'hunter_shoot':\n      return player.status === 'ALIVE';\n    case 'witch_save':\n      // 女巫救人的逻辑可能需要特殊处理\n      return true;\n    default:\n      return player.status === 'ALIVE';\n  }\n};\nconst ActionDialog = ({\n  isOpen,\n  playerRole,\n  players,\n  availableActions,\n  onClose,\n  onConfirm\n}) => {\n  _s();\n  const [selectedAction, setSelectedAction] = useState('skip');\n  const [selectedTargetId, setSelectedTargetId] = useState(null);\n  const roleColor = getRoleColor(playerRole.toString());\n  const playerList = Object.values(players);\n  const needsTarget = selectedAction !== 'skip';\n  const handleActionSelect = actionType => {\n    setSelectedAction(actionType);\n    setSelectedTargetId(null);\n  };\n  const handleTargetSelect = playerId => {\n    const player = players[playerId];\n    if (player && canTargetPlayer(player, selectedAction, playerRole)) {\n      setSelectedTargetId(playerId);\n    }\n  };\n  const handleConfirm = () => {\n    if (selectedAction === 'skip') {\n      onConfirm('skip');\n    } else if (needsTarget && selectedTargetId) {\n      onConfirm(selectedAction, selectedTargetId);\n    } else if (!needsTarget) {\n      onConfirm(selectedAction);\n    }\n    handleClose();\n  };\n  const handleSkip = () => {\n    onConfirm('skip');\n    handleClose();\n  };\n  const handleClose = () => {\n    setSelectedAction('skip');\n    setSelectedTargetId(null);\n    onClose();\n  };\n  const handleOverlayClick = e => {\n    if (e.target === e.currentTarget) {\n      handleClose();\n    }\n  };\n  const canConfirm = selectedAction === 'skip' || !needsTarget || selectedTargetId !== null;\n  if (!isOpen) return null;\n  return /*#__PURE__*/_jsxDEV(Overlay, {\n    isOpen: isOpen,\n    onClick: handleOverlayClick,\n    children: /*#__PURE__*/_jsxDEV(DialogContainer, {\n      children: [/*#__PURE__*/_jsxDEV(DialogHeader, {\n        roleColor: roleColor,\n        children: [/*#__PURE__*/_jsxDEV(RoleIcon, {\n          children: getRoleIcon(playerRole)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 374,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogTitle, {\n          roleColor: roleColor,\n          children: [getRoleName(playerRole), \" - \\u591C\\u665A\\u884C\\u52A8\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogDescription, {\n          children: getRoleDescription(playerRole)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 373,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogBody, {\n        children: [/*#__PURE__*/_jsxDEV(ActionSection, {\n          children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n            children: \"\\u9009\\u62E9\\u884C\\u52A8\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 385,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ActionGrid, {\n            children: [availableActions.map(actionType => {\n              const actionInfo = getActionInfo(actionType);\n              return /*#__PURE__*/_jsxDEV(ActionOption, {\n                isSelected: selectedAction === actionType,\n                isAvailable: true,\n                onClick: () => handleActionSelect(actionType),\n                children: /*#__PURE__*/_jsxDEV(ActionInfo, {\n                  children: [/*#__PURE__*/_jsxDEV(ActionName, {\n                    children: actionInfo.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 397,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(ActionDescription, {\n                    children: actionInfo.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 398,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 396,\n                  columnNumber: 21\n                }, this)\n              }, actionType, false, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 19\n              }, this);\n            }), /*#__PURE__*/_jsxDEV(ActionOption, {\n              isSelected: selectedAction === 'skip',\n              isAvailable: true,\n              onClick: () => handleActionSelect('skip'),\n              children: /*#__PURE__*/_jsxDEV(ActionInfo, {\n                children: [/*#__PURE__*/_jsxDEV(ActionName, {\n                  children: \"\\u8DF3\\u8FC7\\u884C\\u52A8\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 410,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(ActionDescription, {\n                  children: \"\\u672C\\u56DE\\u5408\\u4E0D\\u6267\\u884C\\u4EFB\\u4F55\\u7279\\u6B8A\\u884C\\u52A8\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 411,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 409,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 384,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TargetSection, {\n          show: needsTarget,\n          children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n            children: \"\\u9009\\u62E9\\u76EE\\u6807\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 418,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(PlayerGrid, {\n            children: playerList.filter(player => canTargetPlayer(player, selectedAction, playerRole)).map(player => /*#__PURE__*/_jsxDEV(PlayerOption, {\n              isSelected: selectedTargetId === player.player_id,\n              isTargetable: canTargetPlayer(player, selectedAction, playerRole),\n              onClick: () => handleTargetSelect(player.player_id),\n              children: [/*#__PURE__*/_jsxDEV(PlayerInfo, {\n                children: [/*#__PURE__*/_jsxDEV(PlayerName, {\n                  children: player.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 430,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(PlayerStatus, {\n                  isAlive: player.status === 'ALIVE',\n                  children: player.status === 'ALIVE' ? '存活' : '死亡'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 431,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 429,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(StatusIndicator, {\n                isAlive: player.status === 'ALIVE'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 435,\n                columnNumber: 21\n              }, this)]\n            }, player.player_id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 417,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 383,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogFooter, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: handleClose,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 443,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"warning\",\n          onClick: handleSkip,\n          children: \"\\u8DF3\\u8FC7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 447,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: handleConfirm,\n          disabled: !canConfirm,\n          children: \"\\u786E\\u8BA4\\u884C\\u52A8\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 451,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 442,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 372,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 371,\n    columnNumber: 5\n  }, this);\n};\n_s(ActionDialog, \"MUSI3AA9W9fuYbvfFOUNDdm1AFE=\");\n_c22 = ActionDialog;\nexport default ActionDialog;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22;\n$RefreshReg$(_c, \"Overlay\");\n$RefreshReg$(_c2, \"DialogContainer\");\n$RefreshReg$(_c3, \"DialogHeader\");\n$RefreshReg$(_c4, \"RoleIcon\");\n$RefreshReg$(_c5, \"DialogTitle\");\n$RefreshReg$(_c6, \"DialogDescription\");\n$RefreshReg$(_c7, \"DialogBody\");\n$RefreshReg$(_c8, \"ActionSection\");\n$RefreshReg$(_c9, \"SectionTitle\");\n$RefreshReg$(_c0, \"ActionGrid\");\n$RefreshReg$(_c1, \"ActionOption\");\n$RefreshReg$(_c10, \"ActionInfo\");\n$RefreshReg$(_c11, \"ActionName\");\n$RefreshReg$(_c12, \"ActionDescription\");\n$RefreshReg$(_c13, \"TargetSection\");\n$RefreshReg$(_c14, \"PlayerGrid\");\n$RefreshReg$(_c15, \"PlayerOption\");\n$RefreshReg$(_c16, \"PlayerInfo\");\n$RefreshReg$(_c17, \"PlayerName\");\n$RefreshReg$(_c18, \"PlayerStatus\");\n$RefreshReg$(_c19, \"StatusIndicator\");\n$RefreshReg$(_c20, \"DialogFooter\");\n$RefreshReg$(_c21, \"Button\");\n$RefreshReg$(_c22, \"ActionDialog\");", "map": {"version": 3, "names": ["React", "useState", "styled", "Role", "getRoleColor", "jsxDEV", "_jsxDEV", "Overlay", "div", "props", "isOpen", "theme", "zIndex", "modal", "_c", "DialogContainer", "colors", "backgroundLight", "borderRadius", "xl", "shadows", "_c2", "DialogHeader", "spacing", "lg", "border", "roleColor", "background", "_c3", "RoleIcon", "sm", "_c4", "DialogTitle", "h2", "fontSizes", "fontWeights", "semibold", "_c5", "DialogDescription", "p", "md", "textLight", "lineHeights", "relaxed", "_c6", "DialogBody", "_c7", "ActionSection", "_c8", "SectionTitle", "h3", "text", "_c9", "ActionGrid", "_c0", "ActionOption", "isSelected", "primary", "isAvailable", "transitions", "fast", "_c1", "ActionInfo", "_c10", "ActionName", "medium", "xs", "_c11", "ActionDescription", "_c12", "TargetSection", "show", "_c13", "<PERSON><PERSON><PERSON>", "_c14", "PlayerOption", "secondary", "isTargetable", "_c15", "PlayerInfo", "_c16", "<PERSON><PERSON><PERSON>", "_c17", "PlayerStatus", "isAlive", "success", "danger", "_c18", "StatusIndicator", "_c19", "<PERSON><PERSON><PERSON><PERSON>er", "_c20", "<PERSON><PERSON>", "button", "variant", "warning", "textWhite", "warningLight", "backgroundDark", "primaryLight", "_c21", "getRoleIcon", "role", "roleIcons", "VILLAGER", "WEREWOLF", "SEER", "WITCH", "GUARD", "HUNTER", "getRoleName", "roleNames", "getRoleDescription", "descriptions", "getActionInfo", "actionType", "actionMap", "name", "description", "canTargetPlayer", "player", "player<PERSON><PERSON>", "status", "ActionDialog", "players", "availableActions", "onClose", "onConfirm", "_s", "selectedAction", "setSelectedAction", "selectedTargetId", "setSelectedTargetId", "toString", "playerList", "Object", "values", "<PERSON><PERSON><PERSON><PERSON>", "handleActionSelect", "handleTargetSelect", "playerId", "handleConfirm", "handleClose", "handleSkip", "handleOverlayClick", "e", "target", "currentTarget", "canConfirm", "onClick", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "actionInfo", "filter", "player_id", "disabled", "_c22", "$RefreshReg$"], "sources": ["/private/peiwy/work/ai_projects/wolfkill/frontend/src/components/dialogs/ActionDialog.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { Player, Role } from '../../types';\nimport { getRoleColor } from '../../styles/theme';\n\ninterface ActionDialogProps {\n  isOpen: boolean;\n  playerRole: Role;\n  players: { [key: number]: Player };\n  availableActions: string[];\n  onClose: () => void;\n  onConfirm: (actionType: string, targetId?: number) => void;\n}\n\nconst Overlay = styled.div<{ isOpen: boolean }>`\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  display: ${props => props.isOpen ? 'flex' : 'none'};\n  justify-content: center;\n  align-items: center;\n  z-index: ${props => props.theme.zIndex.modal};\n`;\n\nconst DialogContainer = styled.div`\n  background: ${props => props.theme.colors.backgroundLight};\n  border-radius: ${props => props.theme.borderRadius.xl};\n  box-shadow: ${props => props.theme.shadows.xl};\n  width: 90%;\n  max-width: 600px;\n  max-height: 80vh;\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n`;\n\nconst DialogHeader = styled.div<{ roleColor: string }>`\n  padding: ${props => props.theme.spacing.lg};\n  border-bottom: 1px solid ${props => props.theme.colors.border};\n  background: linear-gradient(135deg, ${props => props.roleColor}20, ${props => props.theme.colors.background});\n`;\n\nconst RoleIcon = styled.div`\n  font-size: 2rem;\n  margin-bottom: ${props => props.theme.spacing.sm};\n`;\n\nconst DialogTitle = styled.h2<{ roleColor: string }>`\n  font-size: ${props => props.theme.fontSizes.xl};\n  font-weight: ${props => props.theme.fontWeights.semibold};\n  color: ${props => props.roleColor};\n  margin: 0 0 ${props => props.theme.spacing.sm} 0;\n`;\n\nconst DialogDescription = styled.p`\n  font-size: ${props => props.theme.fontSizes.md};\n  color: ${props => props.theme.colors.textLight};\n  margin: 0;\n  line-height: ${props => props.theme.lineHeights.relaxed};\n`;\n\nconst DialogBody = styled.div`\n  padding: ${props => props.theme.spacing.lg};\n  flex: 1;\n  overflow-y: auto;\n`;\n\nconst ActionSection = styled.div`\n  margin-bottom: ${props => props.theme.spacing.xl};\n`;\n\nconst SectionTitle = styled.h3`\n  font-size: ${props => props.theme.fontSizes.lg};\n  font-weight: ${props => props.theme.fontWeights.semibold};\n  color: ${props => props.theme.colors.text};\n  margin: 0 0 ${props => props.theme.spacing.md} 0;\n`;\n\nconst ActionGrid = styled.div`\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: ${props => props.theme.spacing.sm};\n`;\n\nconst ActionOption = styled.div<{ isSelected: boolean; isAvailable: boolean }>`\n  display: flex;\n  align-items: center;\n  padding: ${props => props.theme.spacing.md};\n  border: 2px solid ${props => props.isSelected ? props.theme.colors.primary : props.theme.colors.border};\n  border-radius: ${props => props.theme.borderRadius.lg};\n  background: ${props => props.isSelected ? props.theme.colors.primary + '10' : props.theme.colors.backgroundLight};\n  cursor: ${props => props.isAvailable ? 'pointer' : 'not-allowed'};\n  opacity: ${props => props.isAvailable ? 1 : 0.5};\n  transition: ${props => props.theme.transitions.fast};\n\n  &:hover {\n    border-color: ${props => props.isAvailable ? props.theme.colors.primary : props.theme.colors.border};\n    background: ${props => props.isAvailable ? props.theme.colors.primary + '20' : props.theme.colors.backgroundLight};\n  }\n`;\n\nconst ActionInfo = styled.div`\n  flex: 1;\n`;\n\nconst ActionName = styled.div`\n  font-size: ${props => props.theme.fontSizes.md};\n  font-weight: ${props => props.theme.fontWeights.medium};\n  color: ${props => props.theme.colors.text};\n  margin-bottom: ${props => props.theme.spacing.xs};\n`;\n\nconst ActionDescription = styled.div`\n  font-size: ${props => props.theme.fontSizes.sm};\n  color: ${props => props.theme.colors.textLight};\n  line-height: ${props => props.theme.lineHeights.relaxed};\n`;\n\nconst TargetSection = styled.div<{ show: boolean }>`\n  display: ${props => props.show ? 'block' : 'none'};\n  margin-bottom: ${props => props.theme.spacing.lg};\n`;\n\nconst PlayerGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: ${props => props.theme.spacing.sm};\n`;\n\nconst PlayerOption = styled.div<{ isSelected: boolean; isTargetable: boolean }>`\n  display: flex;\n  align-items: center;\n  padding: ${props => props.theme.spacing.md};\n  border: 2px solid ${props => props.isSelected ? props.theme.colors.secondary : props.theme.colors.border};\n  border-radius: ${props => props.theme.borderRadius.lg};\n  background: ${props => props.isSelected ? props.theme.colors.secondary + '10' : props.theme.colors.backgroundLight};\n  cursor: ${props => props.isTargetable ? 'pointer' : 'not-allowed'};\n  opacity: ${props => props.isTargetable ? 1 : 0.5};\n  transition: ${props => props.theme.transitions.fast};\n\n  &:hover {\n    border-color: ${props => props.isTargetable ? props.theme.colors.secondary : props.theme.colors.border};\n    background: ${props => props.isTargetable ? props.theme.colors.secondary + '20' : props.theme.colors.backgroundLight};\n  }\n`;\n\nconst PlayerInfo = styled.div`\n  flex: 1;\n`;\n\nconst PlayerName = styled.div`\n  font-size: ${props => props.theme.fontSizes.md};\n  font-weight: ${props => props.theme.fontWeights.medium};\n  color: ${props => props.theme.colors.text};\n  margin-bottom: ${props => props.theme.spacing.xs};\n`;\n\nconst PlayerStatus = styled.div<{ isAlive: boolean }>`\n  font-size: ${props => props.theme.fontSizes.sm};\n  color: ${props => props.isAlive ? props.theme.colors.success : props.theme.colors.danger};\n`;\n\nconst StatusIndicator = styled.div<{ isAlive: boolean }>`\n  width: 12px;\n  height: 12px;\n  border-radius: 50%;\n  background: ${props => props.isAlive ? props.theme.colors.success : props.theme.colors.danger};\n  margin-left: ${props => props.theme.spacing.md};\n`;\n\nconst DialogFooter = styled.div`\n  padding: ${props => props.theme.spacing.lg};\n  border-top: 1px solid ${props => props.theme.colors.border};\n  display: flex;\n  gap: ${props => props.theme.spacing.md};\n  justify-content: flex-end;\n`;\n\nconst Button = styled.button<{ variant?: 'primary' | 'secondary' | 'warning' }>`\n  padding: ${props => props.theme.spacing.md} ${props => props.theme.spacing.lg};\n  border-radius: ${props => props.theme.borderRadius.md};\n  font-size: ${props => props.theme.fontSizes.md};\n  font-weight: ${props => props.theme.fontWeights.medium};\n  border: none;\n  cursor: pointer;\n  transition: ${props => props.theme.transitions.fast};\n  min-width: 100px;\n\n  ${props => {\n    switch (props.variant) {\n      case 'warning':\n        return `\n          background: ${props.theme.colors.warning};\n          color: ${props.theme.colors.textWhite};\n          &:hover:not(:disabled) { background: ${props.theme.colors.warningLight}; }\n        `;\n      case 'secondary':\n        return `\n          background: ${props.theme.colors.backgroundDark};\n          color: ${props.theme.colors.text};\n          &:hover:not(:disabled) { background: ${props.theme.colors.border}; }\n        `;\n      default:\n        return `\n          background: ${props.theme.colors.primary};\n          color: ${props.theme.colors.textWhite};\n          &:hover:not(:disabled) { background: ${props.theme.colors.primaryLight}; }\n        `;\n    }\n  }}\n\n  &:disabled {\n    background: ${props => props.theme.colors.backgroundDark};\n    color: ${props => props.theme.colors.textLight};\n    cursor: not-allowed;\n  }\n`;\n\nconst getRoleIcon = (role: Role): string => {\n  const roleIcons = {\n    [Role.VILLAGER]: '👨‍🌾',\n    [Role.WEREWOLF]: '🐺',\n    [Role.SEER]: '🔮',\n    [Role.WITCH]: '🧙‍♀️',\n    [Role.GUARD]: '🛡️',\n    [Role.HUNTER]: '🏹'\n  };\n  return roleIcons[role] || '❓';\n};\n\nconst getRoleName = (role: Role): string => {\n  const roleNames = {\n    [Role.VILLAGER]: '村民',\n    [Role.WEREWOLF]: '狼人',\n    [Role.SEER]: '预言家',\n    [Role.WITCH]: '女巫',\n    [Role.GUARD]: '守卫',\n    [Role.HUNTER]: '猎人'\n  };\n  return roleNames[role] || '未知角色';\n};\n\nconst getRoleDescription = (role: Role): string => {\n  const descriptions = {\n    [Role.VILLAGER]: '普通村民，白天参与讨论和投票',\n    [Role.WEREWOLF]: '夜晚杀人，白天伪装成村民',\n    [Role.SEER]: '每晚可以查验一个人的身份',\n    [Role.WITCH]: '拥有一瓶解药和一瓶毒药，各用一次',\n    [Role.GUARD]: '每晚可以保护一个人不被狼人杀死',\n    [Role.HUNTER]: '被淘汰时可以开枪带走一个人'\n  };\n  return descriptions[role] || '';\n};\n\nconst getActionInfo = (actionType: string) => {\n  const actionMap: { [key: string]: { name: string; description: string } } = {\n    'seer_check': {\n      name: '查验身份',\n      description: '查看目标玩家的真实身份（村民阵营或狼人阵营）'\n    },\n    'witch_save': {\n      name: '使用解药',\n      description: '救治被狼人杀害的玩家，每局游戏只能使用一次'\n    },\n    'witch_poison': {\n      name: '使用毒药',\n      description: '毒杀一个玩家，每局游戏只能使用一次'\n    },\n    'guard_protect': {\n      name: '保护玩家',\n      description: '保护目标玩家免受狼人攻击，不能连续两晚保护同一人'\n    },\n    'werewolf_kill': {\n      name: '杀害玩家',\n      description: '选择要杀害的目标，需要狼人阵营达成一致'\n    },\n    'hunter_shoot': {\n      name: '猎人开枪',\n      description: '被淘汰时可以开枪带走一个玩家'\n    },\n    'skip': {\n      name: '跳过行动',\n      description: '本回合不执行任何特殊行动'\n    }\n  };\n\n  return actionMap[actionType] || { name: actionType, description: '未知行动' };\n};\n\nconst canTargetPlayer = (player: Player, actionType: string, playerRole: Role): boolean => {\n  switch (actionType) {\n    case 'seer_check':\n    case 'guard_protect':\n      return player.status === 'ALIVE';\n    case 'werewolf_kill':\n      return player.status === 'ALIVE' && player.role !== Role.WEREWOLF;\n    case 'witch_poison':\n    case 'hunter_shoot':\n      return player.status === 'ALIVE';\n    case 'witch_save':\n      // 女巫救人的逻辑可能需要特殊处理\n      return true;\n    default:\n      return player.status === 'ALIVE';\n  }\n};\n\nconst ActionDialog: React.FC<ActionDialogProps> = ({\n  isOpen,\n  playerRole,\n  players,\n  availableActions,\n  onClose,\n  onConfirm\n}) => {\n  const [selectedAction, setSelectedAction] = useState<string>('skip');\n  const [selectedTargetId, setSelectedTargetId] = useState<number | null>(null);\n\n  const roleColor = getRoleColor(playerRole.toString());\n  const playerList = Object.values(players);\n  const needsTarget = selectedAction !== 'skip';\n\n  const handleActionSelect = (actionType: string) => {\n    setSelectedAction(actionType);\n    setSelectedTargetId(null);\n  };\n\n  const handleTargetSelect = (playerId: number) => {\n    const player = players[playerId];\n    if (player && canTargetPlayer(player, selectedAction, playerRole)) {\n      setSelectedTargetId(playerId);\n    }\n  };\n\n  const handleConfirm = () => {\n    if (selectedAction === 'skip') {\n      onConfirm('skip');\n    } else if (needsTarget && selectedTargetId) {\n      onConfirm(selectedAction, selectedTargetId);\n    } else if (!needsTarget) {\n      onConfirm(selectedAction);\n    }\n    handleClose();\n  };\n\n  const handleSkip = () => {\n    onConfirm('skip');\n    handleClose();\n  };\n\n  const handleClose = () => {\n    setSelectedAction('skip');\n    setSelectedTargetId(null);\n    onClose();\n  };\n\n  const handleOverlayClick = (e: React.MouseEvent) => {\n    if (e.target === e.currentTarget) {\n      handleClose();\n    }\n  };\n\n  const canConfirm = selectedAction === 'skip' || !needsTarget || selectedTargetId !== null;\n\n  if (!isOpen) return null;\n\n  return (\n    <Overlay isOpen={isOpen} onClick={handleOverlayClick}>\n      <DialogContainer>\n        <DialogHeader roleColor={roleColor}>\n          <RoleIcon>{getRoleIcon(playerRole)}</RoleIcon>\n          <DialogTitle roleColor={roleColor}>\n            {getRoleName(playerRole)} - 夜晚行动\n          </DialogTitle>\n          <DialogDescription>\n            {getRoleDescription(playerRole)}\n          </DialogDescription>\n        </DialogHeader>\n\n        <DialogBody>\n          <ActionSection>\n            <SectionTitle>选择行动</SectionTitle>\n            <ActionGrid>\n              {availableActions.map(actionType => {\n                const actionInfo = getActionInfo(actionType);\n                return (\n                  <ActionOption\n                    key={actionType}\n                    isSelected={selectedAction === actionType}\n                    isAvailable={true}\n                    onClick={() => handleActionSelect(actionType)}\n                  >\n                    <ActionInfo>\n                      <ActionName>{actionInfo.name}</ActionName>\n                      <ActionDescription>{actionInfo.description}</ActionDescription>\n                    </ActionInfo>\n                  </ActionOption>\n                );\n              })}\n\n              <ActionOption\n                isSelected={selectedAction === 'skip'}\n                isAvailable={true}\n                onClick={() => handleActionSelect('skip')}\n              >\n                <ActionInfo>\n                  <ActionName>跳过行动</ActionName>\n                  <ActionDescription>本回合不执行任何特殊行动</ActionDescription>\n                </ActionInfo>\n              </ActionOption>\n            </ActionGrid>\n          </ActionSection>\n\n          <TargetSection show={needsTarget}>\n            <SectionTitle>选择目标</SectionTitle>\n            <PlayerGrid>\n              {playerList\n                .filter(player => canTargetPlayer(player, selectedAction, playerRole))\n                .map(player => (\n                  <PlayerOption\n                    key={player.player_id}\n                    isSelected={selectedTargetId === player.player_id}\n                    isTargetable={canTargetPlayer(player, selectedAction, playerRole)}\n                    onClick={() => handleTargetSelect(player.player_id)}\n                  >\n                    <PlayerInfo>\n                      <PlayerName>{player.name}</PlayerName>\n                      <PlayerStatus isAlive={player.status === 'ALIVE'}>\n                        {player.status === 'ALIVE' ? '存活' : '死亡'}\n                      </PlayerStatus>\n                    </PlayerInfo>\n                    <StatusIndicator isAlive={player.status === 'ALIVE'} />\n                  </PlayerOption>\n                ))}\n            </PlayerGrid>\n          </TargetSection>\n        </DialogBody>\n\n        <DialogFooter>\n          <Button variant=\"secondary\" onClick={handleClose}>\n            取消\n          </Button>\n\n          <Button variant=\"warning\" onClick={handleSkip}>\n            跳过\n          </Button>\n\n          <Button\n            variant=\"primary\"\n            onClick={handleConfirm}\n            disabled={!canConfirm}\n          >\n            确认行动\n          </Button>\n        </DialogFooter>\n      </DialogContainer>\n    </Overlay>\n  );\n};\n\nexport default ActionDialog;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAAiBC,IAAI,QAAQ,aAAa;AAC1C,SAASC,YAAY,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAWlD,MAAMC,OAAO,GAAGL,MAAM,CAACM,GAAwB;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA,aAAaC,KAAK,IAAIA,KAAK,CAACC,MAAM,GAAG,MAAM,GAAG,MAAM;AACpD;AACA;AACA,aAAaD,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACC,MAAM,CAACC,KAAK;AAC9C,CAAC;AAACC,EAAA,GAXIP,OAAO;AAab,MAAMQ,eAAe,GAAGb,MAAM,CAACM,GAAG;AAClC,gBAAgBC,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACC,eAAe;AAC3D,mBAAmBR,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACO,YAAY,CAACC,EAAE;AACvD,gBAAgBV,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACS,OAAO,CAACD,EAAE;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACE,GAAA,GAVIN,eAAe;AAYrB,MAAMO,YAAY,GAAGpB,MAAM,CAACM,GAA0B;AACtD,aAAaC,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACY,OAAO,CAACC,EAAE;AAC5C,6BAA6Bf,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACS,MAAM;AAC/D,wCAAwChB,KAAK,IAAIA,KAAK,CAACiB,SAAS,OAAOjB,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACW,UAAU;AAC7G,CAAC;AAACC,GAAA,GAJIN,YAAY;AAMlB,MAAMO,QAAQ,GAAG3B,MAAM,CAACM,GAAG;AAC3B;AACA,mBAAmBC,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACY,OAAO,CAACO,EAAE;AAClD,CAAC;AAACC,GAAA,GAHIF,QAAQ;AAKd,MAAMG,WAAW,GAAG9B,MAAM,CAAC+B,EAAyB;AACpD,eAAexB,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACuB,SAAS,CAACf,EAAE;AAChD,iBAAiBV,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACwB,WAAW,CAACC,QAAQ;AAC1D,WAAW3B,KAAK,IAAIA,KAAK,CAACiB,SAAS;AACnC,gBAAgBjB,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACY,OAAO,CAACO,EAAE;AAC/C,CAAC;AAACO,GAAA,GALIL,WAAW;AAOjB,MAAMM,iBAAiB,GAAGpC,MAAM,CAACqC,CAAC;AAClC,eAAe9B,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACuB,SAAS,CAACM,EAAE;AAChD,WAAW/B,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACyB,SAAS;AAChD;AACA,iBAAiBhC,KAAK,IAAIA,KAAK,CAACE,KAAK,CAAC+B,WAAW,CAACC,OAAO;AACzD,CAAC;AAACC,GAAA,GALIN,iBAAiB;AAOvB,MAAMO,UAAU,GAAG3C,MAAM,CAACM,GAAG;AAC7B,aAAaC,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACY,OAAO,CAACC,EAAE;AAC5C;AACA;AACA,CAAC;AAACsB,GAAA,GAJID,UAAU;AAMhB,MAAME,aAAa,GAAG7C,MAAM,CAACM,GAAG;AAChC,mBAAmBC,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACY,OAAO,CAACJ,EAAE;AAClD,CAAC;AAAC6B,GAAA,GAFID,aAAa;AAInB,MAAME,YAAY,GAAG/C,MAAM,CAACgD,EAAE;AAC9B,eAAezC,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACuB,SAAS,CAACV,EAAE;AAChD,iBAAiBf,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACwB,WAAW,CAACC,QAAQ;AAC1D,WAAW3B,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACmC,IAAI;AAC3C,gBAAgB1C,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACY,OAAO,CAACiB,EAAE;AAC/C,CAAC;AAACY,GAAA,GALIH,YAAY;AAOlB,MAAMI,UAAU,GAAGnD,MAAM,CAACM,GAAG;AAC7B;AACA;AACA,SAASC,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACY,OAAO,CAACO,EAAE;AACxC,CAAC;AAACwB,GAAA,GAJID,UAAU;AAMhB,MAAME,YAAY,GAAGrD,MAAM,CAACM,GAAkD;AAC9E;AACA;AACA,aAAaC,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACY,OAAO,CAACiB,EAAE;AAC5C,sBAAsB/B,KAAK,IAAIA,KAAK,CAAC+C,UAAU,GAAG/C,KAAK,CAACE,KAAK,CAACK,MAAM,CAACyC,OAAO,GAAGhD,KAAK,CAACE,KAAK,CAACK,MAAM,CAACS,MAAM;AACxG,mBAAmBhB,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACO,YAAY,CAACM,EAAE;AACvD,gBAAgBf,KAAK,IAAIA,KAAK,CAAC+C,UAAU,GAAG/C,KAAK,CAACE,KAAK,CAACK,MAAM,CAACyC,OAAO,GAAG,IAAI,GAAGhD,KAAK,CAACE,KAAK,CAACK,MAAM,CAACC,eAAe;AAClH,YAAYR,KAAK,IAAIA,KAAK,CAACiD,WAAW,GAAG,SAAS,GAAG,aAAa;AAClE,aAAajD,KAAK,IAAIA,KAAK,CAACiD,WAAW,GAAG,CAAC,GAAG,GAAG;AACjD,gBAAgBjD,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACgD,WAAW,CAACC,IAAI;AACrD;AACA;AACA,oBAAoBnD,KAAK,IAAIA,KAAK,CAACiD,WAAW,GAAGjD,KAAK,CAACE,KAAK,CAACK,MAAM,CAACyC,OAAO,GAAGhD,KAAK,CAACE,KAAK,CAACK,MAAM,CAACS,MAAM;AACvG,kBAAkBhB,KAAK,IAAIA,KAAK,CAACiD,WAAW,GAAGjD,KAAK,CAACE,KAAK,CAACK,MAAM,CAACyC,OAAO,GAAG,IAAI,GAAGhD,KAAK,CAACE,KAAK,CAACK,MAAM,CAACC,eAAe;AACrH;AACA,CAAC;AAAC4C,GAAA,GAfIN,YAAY;AAiBlB,MAAMO,UAAU,GAAG5D,MAAM,CAACM,GAAG;AAC7B;AACA,CAAC;AAACuD,IAAA,GAFID,UAAU;AAIhB,MAAME,UAAU,GAAG9D,MAAM,CAACM,GAAG;AAC7B,eAAeC,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACuB,SAAS,CAACM,EAAE;AAChD,iBAAiB/B,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACwB,WAAW,CAAC8B,MAAM;AACxD,WAAWxD,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACmC,IAAI;AAC3C,mBAAmB1C,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACY,OAAO,CAAC2C,EAAE;AAClD,CAAC;AAACC,IAAA,GALIH,UAAU;AAOhB,MAAMI,iBAAiB,GAAGlE,MAAM,CAACM,GAAG;AACpC,eAAeC,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACuB,SAAS,CAACJ,EAAE;AAChD,WAAWrB,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACyB,SAAS;AAChD,iBAAiBhC,KAAK,IAAIA,KAAK,CAACE,KAAK,CAAC+B,WAAW,CAACC,OAAO;AACzD,CAAC;AAAC0B,IAAA,GAJID,iBAAiB;AAMvB,MAAME,aAAa,GAAGpE,MAAM,CAACM,GAAsB;AACnD,aAAaC,KAAK,IAAIA,KAAK,CAAC8D,IAAI,GAAG,OAAO,GAAG,MAAM;AACnD,mBAAmB9D,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACY,OAAO,CAACC,EAAE;AAClD,CAAC;AAACgD,IAAA,GAHIF,aAAa;AAKnB,MAAMG,UAAU,GAAGvE,MAAM,CAACM,GAAG;AAC7B;AACA;AACA,SAASC,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACY,OAAO,CAACO,EAAE;AACxC,CAAC;AAAC4C,IAAA,GAJID,UAAU;AAMhB,MAAME,YAAY,GAAGzE,MAAM,CAACM,GAAmD;AAC/E;AACA;AACA,aAAaC,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACY,OAAO,CAACiB,EAAE;AAC5C,sBAAsB/B,KAAK,IAAIA,KAAK,CAAC+C,UAAU,GAAG/C,KAAK,CAACE,KAAK,CAACK,MAAM,CAAC4D,SAAS,GAAGnE,KAAK,CAACE,KAAK,CAACK,MAAM,CAACS,MAAM;AAC1G,mBAAmBhB,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACO,YAAY,CAACM,EAAE;AACvD,gBAAgBf,KAAK,IAAIA,KAAK,CAAC+C,UAAU,GAAG/C,KAAK,CAACE,KAAK,CAACK,MAAM,CAAC4D,SAAS,GAAG,IAAI,GAAGnE,KAAK,CAACE,KAAK,CAACK,MAAM,CAACC,eAAe;AACpH,YAAYR,KAAK,IAAIA,KAAK,CAACoE,YAAY,GAAG,SAAS,GAAG,aAAa;AACnE,aAAapE,KAAK,IAAIA,KAAK,CAACoE,YAAY,GAAG,CAAC,GAAG,GAAG;AAClD,gBAAgBpE,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACgD,WAAW,CAACC,IAAI;AACrD;AACA;AACA,oBAAoBnD,KAAK,IAAIA,KAAK,CAACoE,YAAY,GAAGpE,KAAK,CAACE,KAAK,CAACK,MAAM,CAAC4D,SAAS,GAAGnE,KAAK,CAACE,KAAK,CAACK,MAAM,CAACS,MAAM;AAC1G,kBAAkBhB,KAAK,IAAIA,KAAK,CAACoE,YAAY,GAAGpE,KAAK,CAACE,KAAK,CAACK,MAAM,CAAC4D,SAAS,GAAG,IAAI,GAAGnE,KAAK,CAACE,KAAK,CAACK,MAAM,CAACC,eAAe;AACxH;AACA,CAAC;AAAC6D,IAAA,GAfIH,YAAY;AAiBlB,MAAMI,UAAU,GAAG7E,MAAM,CAACM,GAAG;AAC7B;AACA,CAAC;AAACwE,IAAA,GAFID,UAAU;AAIhB,MAAME,UAAU,GAAG/E,MAAM,CAACM,GAAG;AAC7B,eAAeC,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACuB,SAAS,CAACM,EAAE;AAChD,iBAAiB/B,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACwB,WAAW,CAAC8B,MAAM;AACxD,WAAWxD,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACmC,IAAI;AAC3C,mBAAmB1C,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACY,OAAO,CAAC2C,EAAE;AAClD,CAAC;AAACgB,IAAA,GALID,UAAU;AAOhB,MAAME,YAAY,GAAGjF,MAAM,CAACM,GAAyB;AACrD,eAAeC,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACuB,SAAS,CAACJ,EAAE;AAChD,WAAWrB,KAAK,IAAIA,KAAK,CAAC2E,OAAO,GAAG3E,KAAK,CAACE,KAAK,CAACK,MAAM,CAACqE,OAAO,GAAG5E,KAAK,CAACE,KAAK,CAACK,MAAM,CAACsE,MAAM;AAC1F,CAAC;AAACC,IAAA,GAHIJ,YAAY;AAKlB,MAAMK,eAAe,GAAGtF,MAAM,CAACM,GAAyB;AACxD;AACA;AACA;AACA,gBAAgBC,KAAK,IAAIA,KAAK,CAAC2E,OAAO,GAAG3E,KAAK,CAACE,KAAK,CAACK,MAAM,CAACqE,OAAO,GAAG5E,KAAK,CAACE,KAAK,CAACK,MAAM,CAACsE,MAAM;AAC/F,iBAAiB7E,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACY,OAAO,CAACiB,EAAE;AAChD,CAAC;AAACiD,IAAA,GANID,eAAe;AAQrB,MAAME,YAAY,GAAGxF,MAAM,CAACM,GAAG;AAC/B,aAAaC,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACY,OAAO,CAACC,EAAE;AAC5C,0BAA0Bf,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACS,MAAM;AAC5D;AACA,SAAShB,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACY,OAAO,CAACiB,EAAE;AACxC;AACA,CAAC;AAACmD,IAAA,GANID,YAAY;AAQlB,MAAME,MAAM,GAAG1F,MAAM,CAAC2F,MAAyD;AAC/E,aAAapF,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACY,OAAO,CAACiB,EAAE,IAAI/B,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACY,OAAO,CAACC,EAAE;AAC/E,mBAAmBf,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACO,YAAY,CAACsB,EAAE;AACvD,eAAe/B,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACuB,SAAS,CAACM,EAAE;AAChD,iBAAiB/B,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACwB,WAAW,CAAC8B,MAAM;AACxD;AACA;AACA,gBAAgBxD,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACgD,WAAW,CAACC,IAAI;AACrD;AACA;AACA,IAAInD,KAAK,IAAI;EACT,QAAQA,KAAK,CAACqF,OAAO;IACnB,KAAK,SAAS;MACZ,OAAO;AACf,wBAAwBrF,KAAK,CAACE,KAAK,CAACK,MAAM,CAAC+E,OAAO;AAClD,mBAAmBtF,KAAK,CAACE,KAAK,CAACK,MAAM,CAACgF,SAAS;AAC/C,iDAAiDvF,KAAK,CAACE,KAAK,CAACK,MAAM,CAACiF,YAAY;AAChF,SAAS;IACH,KAAK,WAAW;MACd,OAAO;AACf,wBAAwBxF,KAAK,CAACE,KAAK,CAACK,MAAM,CAACkF,cAAc;AACzD,mBAAmBzF,KAAK,CAACE,KAAK,CAACK,MAAM,CAACmC,IAAI;AAC1C,iDAAiD1C,KAAK,CAACE,KAAK,CAACK,MAAM,CAACS,MAAM;AAC1E,SAAS;IACH;MACE,OAAO;AACf,wBAAwBhB,KAAK,CAACE,KAAK,CAACK,MAAM,CAACyC,OAAO;AAClD,mBAAmBhD,KAAK,CAACE,KAAK,CAACK,MAAM,CAACgF,SAAS;AAC/C,iDAAiDvF,KAAK,CAACE,KAAK,CAACK,MAAM,CAACmF,YAAY;AAChF,SAAS;EACL;AACF,CAAC;AACH;AACA;AACA,kBAAkB1F,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACkF,cAAc;AAC5D,aAAazF,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACyB,SAAS;AAClD;AACA;AACA,CAAC;AAAC2D,IAAA,GAtCIR,MAAM;AAwCZ,MAAMS,WAAW,GAAIC,IAAU,IAAa;EAC1C,MAAMC,SAAS,GAAG;IAChB,CAACpG,IAAI,CAACqG,QAAQ,GAAG,OAAO;IACxB,CAACrG,IAAI,CAACsG,QAAQ,GAAG,IAAI;IACrB,CAACtG,IAAI,CAACuG,IAAI,GAAG,IAAI;IACjB,CAACvG,IAAI,CAACwG,KAAK,GAAG,OAAO;IACrB,CAACxG,IAAI,CAACyG,KAAK,GAAG,KAAK;IACnB,CAACzG,IAAI,CAAC0G,MAAM,GAAG;EACjB,CAAC;EACD,OAAON,SAAS,CAACD,IAAI,CAAC,IAAI,GAAG;AAC/B,CAAC;AAED,MAAMQ,WAAW,GAAIR,IAAU,IAAa;EAC1C,MAAMS,SAAS,GAAG;IAChB,CAAC5G,IAAI,CAACqG,QAAQ,GAAG,IAAI;IACrB,CAACrG,IAAI,CAACsG,QAAQ,GAAG,IAAI;IACrB,CAACtG,IAAI,CAACuG,IAAI,GAAG,KAAK;IAClB,CAACvG,IAAI,CAACwG,KAAK,GAAG,IAAI;IAClB,CAACxG,IAAI,CAACyG,KAAK,GAAG,IAAI;IAClB,CAACzG,IAAI,CAAC0G,MAAM,GAAG;EACjB,CAAC;EACD,OAAOE,SAAS,CAACT,IAAI,CAAC,IAAI,MAAM;AAClC,CAAC;AAED,MAAMU,kBAAkB,GAAIV,IAAU,IAAa;EACjD,MAAMW,YAAY,GAAG;IACnB,CAAC9G,IAAI,CAACqG,QAAQ,GAAG,gBAAgB;IACjC,CAACrG,IAAI,CAACsG,QAAQ,GAAG,cAAc;IAC/B,CAACtG,IAAI,CAACuG,IAAI,GAAG,cAAc;IAC3B,CAACvG,IAAI,CAACwG,KAAK,GAAG,kBAAkB;IAChC,CAACxG,IAAI,CAACyG,KAAK,GAAG,iBAAiB;IAC/B,CAACzG,IAAI,CAAC0G,MAAM,GAAG;EACjB,CAAC;EACD,OAAOI,YAAY,CAACX,IAAI,CAAC,IAAI,EAAE;AACjC,CAAC;AAED,MAAMY,aAAa,GAAIC,UAAkB,IAAK;EAC5C,MAAMC,SAAmE,GAAG;IAC1E,YAAY,EAAE;MACZC,IAAI,EAAE,MAAM;MACZC,WAAW,EAAE;IACf,CAAC;IACD,YAAY,EAAE;MACZD,IAAI,EAAE,MAAM;MACZC,WAAW,EAAE;IACf,CAAC;IACD,cAAc,EAAE;MACdD,IAAI,EAAE,MAAM;MACZC,WAAW,EAAE;IACf,CAAC;IACD,eAAe,EAAE;MACfD,IAAI,EAAE,MAAM;MACZC,WAAW,EAAE;IACf,CAAC;IACD,eAAe,EAAE;MACfD,IAAI,EAAE,MAAM;MACZC,WAAW,EAAE;IACf,CAAC;IACD,cAAc,EAAE;MACdD,IAAI,EAAE,MAAM;MACZC,WAAW,EAAE;IACf,CAAC;IACD,MAAM,EAAE;MACND,IAAI,EAAE,MAAM;MACZC,WAAW,EAAE;IACf;EACF,CAAC;EAED,OAAOF,SAAS,CAACD,UAAU,CAAC,IAAI;IAAEE,IAAI,EAAEF,UAAU;IAAEG,WAAW,EAAE;EAAO,CAAC;AAC3E,CAAC;AAED,MAAMC,eAAe,GAAGA,CAACC,MAAc,EAAEL,UAAkB,EAAEM,UAAgB,KAAc;EACzF,QAAQN,UAAU;IAChB,KAAK,YAAY;IACjB,KAAK,eAAe;MAClB,OAAOK,MAAM,CAACE,MAAM,KAAK,OAAO;IAClC,KAAK,eAAe;MAClB,OAAOF,MAAM,CAACE,MAAM,KAAK,OAAO,IAAIF,MAAM,CAAClB,IAAI,KAAKnG,IAAI,CAACsG,QAAQ;IACnE,KAAK,cAAc;IACnB,KAAK,cAAc;MACjB,OAAOe,MAAM,CAACE,MAAM,KAAK,OAAO;IAClC,KAAK,YAAY;MACf;MACA,OAAO,IAAI;IACb;MACE,OAAOF,MAAM,CAACE,MAAM,KAAK,OAAO;EACpC;AACF,CAAC;AAED,MAAMC,YAAyC,GAAGA,CAAC;EACjDjH,MAAM;EACN+G,UAAU;EACVG,OAAO;EACPC,gBAAgB;EAChBC,OAAO;EACPC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGjI,QAAQ,CAAS,MAAM,CAAC;EACpE,MAAM,CAACkI,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnI,QAAQ,CAAgB,IAAI,CAAC;EAE7E,MAAMyB,SAAS,GAAGtB,YAAY,CAACqH,UAAU,CAACY,QAAQ,CAAC,CAAC,CAAC;EACrD,MAAMC,UAAU,GAAGC,MAAM,CAACC,MAAM,CAACZ,OAAO,CAAC;EACzC,MAAMa,WAAW,GAAGR,cAAc,KAAK,MAAM;EAE7C,MAAMS,kBAAkB,GAAIvB,UAAkB,IAAK;IACjDe,iBAAiB,CAACf,UAAU,CAAC;IAC7BiB,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAMO,kBAAkB,GAAIC,QAAgB,IAAK;IAC/C,MAAMpB,MAAM,GAAGI,OAAO,CAACgB,QAAQ,CAAC;IAChC,IAAIpB,MAAM,IAAID,eAAe,CAACC,MAAM,EAAES,cAAc,EAAER,UAAU,CAAC,EAAE;MACjEW,mBAAmB,CAACQ,QAAQ,CAAC;IAC/B;EACF,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIZ,cAAc,KAAK,MAAM,EAAE;MAC7BF,SAAS,CAAC,MAAM,CAAC;IACnB,CAAC,MAAM,IAAIU,WAAW,IAAIN,gBAAgB,EAAE;MAC1CJ,SAAS,CAACE,cAAc,EAAEE,gBAAgB,CAAC;IAC7C,CAAC,MAAM,IAAI,CAACM,WAAW,EAAE;MACvBV,SAAS,CAACE,cAAc,CAAC;IAC3B;IACAa,WAAW,CAAC,CAAC;EACf,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvBhB,SAAS,CAAC,MAAM,CAAC;IACjBe,WAAW,CAAC,CAAC;EACf,CAAC;EAED,MAAMA,WAAW,GAAGA,CAAA,KAAM;IACxBZ,iBAAiB,CAAC,MAAM,CAAC;IACzBE,mBAAmB,CAAC,IAAI,CAAC;IACzBN,OAAO,CAAC,CAAC;EACX,CAAC;EAED,MAAMkB,kBAAkB,GAAIC,CAAmB,IAAK;IAClD,IAAIA,CAAC,CAACC,MAAM,KAAKD,CAAC,CAACE,aAAa,EAAE;MAChCL,WAAW,CAAC,CAAC;IACf;EACF,CAAC;EAED,MAAMM,UAAU,GAAGnB,cAAc,KAAK,MAAM,IAAI,CAACQ,WAAW,IAAIN,gBAAgB,KAAK,IAAI;EAEzF,IAAI,CAACzH,MAAM,EAAE,OAAO,IAAI;EAExB,oBACEJ,OAAA,CAACC,OAAO;IAACG,MAAM,EAAEA,MAAO;IAAC2I,OAAO,EAAEL,kBAAmB;IAAAM,QAAA,eACnDhJ,OAAA,CAACS,eAAe;MAAAuI,QAAA,gBACdhJ,OAAA,CAACgB,YAAY;QAACI,SAAS,EAAEA,SAAU;QAAA4H,QAAA,gBACjChJ,OAAA,CAACuB,QAAQ;UAAAyH,QAAA,EAAEjD,WAAW,CAACoB,UAAU;QAAC;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eAC9CpJ,OAAA,CAAC0B,WAAW;UAACN,SAAS,EAAEA,SAAU;UAAA4H,QAAA,GAC/BxC,WAAW,CAACW,UAAU,CAAC,EAAC,6BAC3B;QAAA;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACdpJ,OAAA,CAACgC,iBAAiB;UAAAgH,QAAA,EACftC,kBAAkB,CAACS,UAAU;QAAC;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAEfpJ,OAAA,CAACuC,UAAU;QAAAyG,QAAA,gBACThJ,OAAA,CAACyC,aAAa;UAAAuG,QAAA,gBACZhJ,OAAA,CAAC2C,YAAY;YAAAqG,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC,eACjCpJ,OAAA,CAAC+C,UAAU;YAAAiG,QAAA,GACRzB,gBAAgB,CAAC8B,GAAG,CAACxC,UAAU,IAAI;cAClC,MAAMyC,UAAU,GAAG1C,aAAa,CAACC,UAAU,CAAC;cAC5C,oBACE7G,OAAA,CAACiD,YAAY;gBAEXC,UAAU,EAAEyE,cAAc,KAAKd,UAAW;gBAC1CzD,WAAW,EAAE,IAAK;gBAClB2F,OAAO,EAAEA,CAAA,KAAMX,kBAAkB,CAACvB,UAAU,CAAE;gBAAAmC,QAAA,eAE9ChJ,OAAA,CAACwD,UAAU;kBAAAwF,QAAA,gBACThJ,OAAA,CAAC0D,UAAU;oBAAAsF,QAAA,EAAEM,UAAU,CAACvC;kBAAI;oBAAAkC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC,eAC1CpJ,OAAA,CAAC8D,iBAAiB;oBAAAkF,QAAA,EAAEM,UAAU,CAACtC;kBAAW;oBAAAiC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAoB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD;cAAC,GARRvC,UAAU;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OASH,CAAC;YAEnB,CAAC,CAAC,eAEFpJ,OAAA,CAACiD,YAAY;cACXC,UAAU,EAAEyE,cAAc,KAAK,MAAO;cACtCvE,WAAW,EAAE,IAAK;cAClB2F,OAAO,EAAEA,CAAA,KAAMX,kBAAkB,CAAC,MAAM,CAAE;cAAAY,QAAA,eAE1ChJ,OAAA,CAACwD,UAAU;gBAAAwF,QAAA,gBACThJ,OAAA,CAAC0D,UAAU;kBAAAsF,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7BpJ,OAAA,CAAC8D,iBAAiB;kBAAAkF,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAmB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEhBpJ,OAAA,CAACgE,aAAa;UAACC,IAAI,EAAEkE,WAAY;UAAAa,QAAA,gBAC/BhJ,OAAA,CAAC2C,YAAY;YAAAqG,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC,eACjCpJ,OAAA,CAACmE,UAAU;YAAA6E,QAAA,EACRhB,UAAU,CACRuB,MAAM,CAACrC,MAAM,IAAID,eAAe,CAACC,MAAM,EAAES,cAAc,EAAER,UAAU,CAAC,CAAC,CACrEkC,GAAG,CAACnC,MAAM,iBACTlH,OAAA,CAACqE,YAAY;cAEXnB,UAAU,EAAE2E,gBAAgB,KAAKX,MAAM,CAACsC,SAAU;cAClDjF,YAAY,EAAE0C,eAAe,CAACC,MAAM,EAAES,cAAc,EAAER,UAAU,CAAE;cAClE4B,OAAO,EAAEA,CAAA,KAAMV,kBAAkB,CAACnB,MAAM,CAACsC,SAAS,CAAE;cAAAR,QAAA,gBAEpDhJ,OAAA,CAACyE,UAAU;gBAAAuE,QAAA,gBACThJ,OAAA,CAAC2E,UAAU;kBAAAqE,QAAA,EAAE9B,MAAM,CAACH;gBAAI;kBAAAkC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eACtCpJ,OAAA,CAAC6E,YAAY;kBAACC,OAAO,EAAEoC,MAAM,CAACE,MAAM,KAAK,OAAQ;kBAAA4B,QAAA,EAC9C9B,MAAM,CAACE,MAAM,KAAK,OAAO,GAAG,IAAI,GAAG;gBAAI;kBAAA6B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACbpJ,OAAA,CAACkF,eAAe;gBAACJ,OAAO,EAAEoC,MAAM,CAACE,MAAM,KAAK;cAAQ;gBAAA6B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA,GAXlDlC,MAAM,CAACsC,SAAS;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAYT,CACf;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEbpJ,OAAA,CAACoF,YAAY;QAAA4D,QAAA,gBACXhJ,OAAA,CAACsF,MAAM;UAACE,OAAO,EAAC,WAAW;UAACuD,OAAO,EAAEP,WAAY;UAAAQ,QAAA,EAAC;QAElD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETpJ,OAAA,CAACsF,MAAM;UAACE,OAAO,EAAC,SAAS;UAACuD,OAAO,EAAEN,UAAW;UAAAO,QAAA,EAAC;QAE/C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETpJ,OAAA,CAACsF,MAAM;UACLE,OAAO,EAAC,SAAS;UACjBuD,OAAO,EAAER,aAAc;UACvBkB,QAAQ,EAAE,CAACX,UAAW;UAAAE,QAAA,EACvB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACX,CAAC;AAEd,CAAC;AAAC1B,EAAA,CAvJIL,YAAyC;AAAAqC,IAAA,GAAzCrC,YAAyC;AAyJ/C,eAAeA,YAAY;AAAC,IAAA7G,EAAA,EAAAO,GAAA,EAAAO,GAAA,EAAAG,GAAA,EAAAM,GAAA,EAAAO,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAO,GAAA,EAAAE,IAAA,EAAAI,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAI,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAK,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAS,IAAA,EAAA4D,IAAA;AAAAC,YAAA,CAAAnJ,EAAA;AAAAmJ,YAAA,CAAA5I,GAAA;AAAA4I,YAAA,CAAArI,GAAA;AAAAqI,YAAA,CAAAlI,GAAA;AAAAkI,YAAA,CAAA5H,GAAA;AAAA4H,YAAA,CAAArH,GAAA;AAAAqH,YAAA,CAAAnH,GAAA;AAAAmH,YAAA,CAAAjH,GAAA;AAAAiH,YAAA,CAAA7G,GAAA;AAAA6G,YAAA,CAAA3G,GAAA;AAAA2G,YAAA,CAAApG,GAAA;AAAAoG,YAAA,CAAAlG,IAAA;AAAAkG,YAAA,CAAA9F,IAAA;AAAA8F,YAAA,CAAA5F,IAAA;AAAA4F,YAAA,CAAAzF,IAAA;AAAAyF,YAAA,CAAAvF,IAAA;AAAAuF,YAAA,CAAAnF,IAAA;AAAAmF,YAAA,CAAAjF,IAAA;AAAAiF,YAAA,CAAA/E,IAAA;AAAA+E,YAAA,CAAA1E,IAAA;AAAA0E,YAAA,CAAAxE,IAAA;AAAAwE,YAAA,CAAAtE,IAAA;AAAAsE,YAAA,CAAA7D,IAAA;AAAA6D,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}