{"ast": null, "code": "// 游戏类型定义\nexport let Role = /*#__PURE__*/function (Role) {\n  Role[\"VILLAGER\"] = \"VILLAGER\";\n  Role[\"WEREWOLF\"] = \"WEREWOLF\";\n  Role[\"SEER\"] = \"SEER\";\n  Role[\"WITCH\"] = \"WITCH\";\n  Role[\"GUARD\"] = \"GUARD\";\n  Role[\"HUNTER\"] = \"HUNTER\";\n  return Role;\n}({});\nexport let GamePhase = /*#__PURE__*/function (GamePhase) {\n  GamePhase[\"SETUP\"] = \"SETUP\";\n  GamePhase[\"NIGHT\"] = \"NIGHT\";\n  GamePhase[\"DAY_DISCUSSION\"] = \"DAY_DISCUSSION\";\n  GamePhase[\"DAY_VOTING\"] = \"DAY_VOTING\";\n  GamePhase[\"GAME_OVER\"] = \"GAME_OVER\";\n  return GamePhase;\n}({});\nexport let GameResult = /*#__PURE__*/function (GameResult) {\n  GameResult[\"ONGOING\"] = \"ONGOING\";\n  GameResult[\"VILLAGERS_WIN\"] = \"VILLAGERS_WIN\";\n  GameResult[\"WEREWOLVES_WIN\"] = \"WEREWOLVES_WIN\";\n  GameResult[\"DRAW\"] = \"DRAW\";\n  return GameResult;\n}({});\nexport let PlayerStatus = /*#__PURE__*/function (PlayerStatus) {\n  PlayerStatus[\"ALIVE\"] = \"ALIVE\";\n  PlayerStatus[\"DEAD\"] = \"DEAD\";\n  return PlayerStatus;\n}({});\nexport let VoteType = /*#__PURE__*/function (VoteType) {\n  VoteType[\"ELIMINATION\"] = \"ELIMINATION\";\n  VoteType[\"WEREWOLF_KILL\"] = \"WEREWOLF_KILL\";\n  VoteType[\"SEER_CHECK\"] = \"SEER_CHECK\";\n  VoteType[\"WITCH_SAVE\"] = \"WITCH_SAVE\";\n  VoteType[\"WITCH_POISON\"] = \"WITCH_POISON\";\n  VoteType[\"GUARD_PROTECT\"] = \"GUARD_PROTECT\";\n  return VoteType;\n}({});\n\n// 玩家接口\n\n// 游戏状态接口\n\n// 投票接口\n\n// 行动接口\n\n// 聊天消息接口\n\n// 游戏配置接口\n\n// API响应接口\n\n// WebSocket消息接口", "map": {"version": 3, "names": ["Role", "GamePhase", "GameResult", "PlayerStatus", "VoteType"], "sources": ["/private/peiwy/work/ai_projects/wolfkill/frontend/src/types/index.ts"], "sourcesContent": ["// 游戏类型定义\nexport enum Role {\n  VILLAGER = 'VILLAGER',\n  WEREWOLF = 'WEREWOLF',\n  SEER = 'SEER',\n  WITCH = 'WITCH',\n  GUARD = 'GUARD',\n  HUNTER = 'HUNTER'\n}\n\nexport enum GamePhase {\n  SETUP = 'SETUP',\n  NIGHT = 'NIGHT',\n  DAY_DISCUSSION = 'DAY_DISCUSSION',\n  DAY_VOTING = 'DAY_VOTING',\n  GAME_OVER = 'GAME_OVER'\n}\n\nexport enum GameResult {\n  ONGOING = 'ONGOING',\n  VILLAGERS_WIN = 'VILLAGERS_WIN',\n  WEREWOLVES_WIN = 'WEREWOLVES_WIN',\n  DRAW = 'DRAW'\n}\n\nexport enum PlayerStatus {\n  ALIVE = 'ALIVE',\n  DEAD = 'DEAD'\n}\n\nexport enum VoteType {\n  ELIMINATION = 'ELIMINATION',\n  WEREWOLF_KILL = 'WEREWOLF_KILL',\n  SEER_CHECK = 'SEER_CHECK',\n  WITCH_SAVE = 'WITCH_SAVE',\n  WITCH_POISON = 'WITCH_POISON',\n  GUARD_PROTECT = 'GUARD_PROTECT'\n}\n\n// 玩家接口\nexport interface Player {\n  player_id: number;\n  name: string;\n  role: Role;\n  status: PlayerStatus;\n  faction: string;\n}\n\n// 游戏状态接口\nexport interface GameState {\n  game_id: string;\n  current_phase: GamePhase;\n  current_round: number;\n  game_result: GameResult;\n  players: { [key: number]: Player };\n  alive_players: number[];\n  dead_players: number[];\n}\n\n// 投票接口\nexport interface Vote {\n  voter_id: number;\n  target_id: number | null;\n  vote_type: VoteType;\n  reason?: string;\n}\n\n// 行动接口\nexport interface Action {\n  player_id: number;\n  action_type: string;\n  target_id?: number;\n  timestamp: string;\n}\n\n// 聊天消息接口\nexport interface ChatMessage {\n  id: string;\n  sender: string;\n  message: string;\n  timestamp: string;\n  type: 'system' | 'player' | 'announcement';\n}\n\n// 游戏配置接口\nexport interface GameConfig {\n  total_players: number;\n  role_distribution: { [key in Role]?: number };\n}\n\n// API响应接口\nexport interface ApiResponse<T = any> {\n  success: boolean;\n  data?: T;\n  error?: string;\n  message?: string;\n}\n\n// WebSocket消息接口\nexport interface WebSocketMessage {\n  type: string;\n  data: any;\n  timestamp: string;\n}"], "mappings": "AAAA;AACA,WAAYA,IAAI,0BAAJA,IAAI;EAAJA,IAAI;EAAJA,IAAI;EAAJA,IAAI;EAAJA,IAAI;EAAJA,IAAI;EAAJA,IAAI;EAAA,OAAJA,IAAI;AAAA;AAShB,WAAYC,SAAS,0BAATA,SAAS;EAATA,SAAS;EAATA,SAAS;EAATA,SAAS;EAATA,SAAS;EAATA,SAAS;EAAA,OAATA,SAAS;AAAA;AAQrB,WAAYC,UAAU,0BAAVA,UAAU;EAAVA,UAAU;EAAVA,UAAU;EAAVA,UAAU;EAAVA,UAAU;EAAA,OAAVA,UAAU;AAAA;AAOtB,WAAYC,YAAY,0BAAZA,YAAY;EAAZA,YAAY;EAAZA,YAAY;EAAA,OAAZA,YAAY;AAAA;AAKxB,WAAYC,QAAQ,0BAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAAA,OAARA,QAAQ;AAAA;;AASpB;;AASA;;AAWA;;AAQA;;AAQA;;AASA;;AAMA;;AAQA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}