{"ast": null, "code": "var _jsxFileName = \"/private/peiwy/work/ai_projects/wolfkill/frontend/src/components/ChatPanel.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport styled from 'styled-components';\nimport { MessageBubble } from './animations/GameAnimations';\nimport { FadeIn } from './animations/AnimatedComponents';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PanelContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n`;\n_c = PanelContainer;\nconst PanelHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: ${props => props.theme.spacing.lg};\n`;\n_c2 = PanelHeader;\nconst PanelTitle = styled.h2`\n  font-size: ${props => props.theme.fontSizes.xl};\n  font-weight: ${props => props.theme.fontWeights.semibold};\n  color: ${props => props.theme.colors.text};\n  margin: 0;\n`;\n_c3 = PanelTitle;\nconst MessageCount = styled.span`\n  font-size: ${props => props.theme.fontSizes.sm};\n  color: ${props => props.theme.colors.textLight};\n  background: ${props => props.theme.colors.background};\n  padding: ${props => props.theme.spacing.xs} ${props => props.theme.spacing.sm};\n  border-radius: ${props => props.theme.borderRadius.md};\n`;\n_c4 = MessageCount;\nconst MessagesContainer = styled.div`\n  flex: 1;\n  background: ${props => props.theme.colors.background};\n  border-radius: ${props => props.theme.borderRadius.lg};\n  border: 1px solid ${props => props.theme.colors.border};\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n`;\n_c5 = MessagesContainer;\nconst MessagesList = styled.div`\n  flex: 1;\n  overflow-y: auto;\n  padding: ${props => props.theme.spacing.md};\n  display: flex;\n  flex-direction: column;\n  gap: ${props => props.theme.spacing.sm};\n`;\n_c6 = MessagesList;\nconst MessageItem = styled.div`\n  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};\n  border-radius: ${props => props.theme.borderRadius.md};\n  font-size: ${props => props.theme.fontSizes.sm};\n  line-height: ${props => props.theme.lineHeights.relaxed};\n\n  ${props => {\n  switch (props.type) {\n    case 'system':\n      return `\n          background: ${props.theme.colors.info}20;\n          border-left: 3px solid ${props.theme.colors.info};\n          color: ${props.theme.colors.infoDark};\n        `;\n    case 'announcement':\n      return `\n          background: ${props.theme.colors.warning}20;\n          border-left: 3px solid ${props.theme.colors.warning};\n          color: ${props.theme.colors.warningDark};\n          font-weight: ${props.theme.fontWeights.medium};\n        `;\n    default:\n      return `\n          background: ${props.theme.colors.backgroundLight};\n          border: 1px solid ${props.theme.colors.borderLight};\n          color: ${props.theme.colors.text};\n        `;\n  }\n}}\n`;\n_c7 = MessageItem;\nconst MessageHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: ${props => props.theme.spacing.xs};\n`;\n_c8 = MessageHeader;\nconst MessageSender = styled.span`\n  font-weight: ${props => props.theme.fontWeights.medium};\n  color: ${props => props.theme.colors.primary};\n`;\n_c9 = MessageSender;\nconst MessageTime = styled.span`\n  font-size: ${props => props.theme.fontSizes.xs};\n  color: ${props => props.theme.colors.textLight};\n`;\n_c0 = MessageTime;\nconst MessageContent = styled.div`\n  word-wrap: break-word;\n`;\n_c1 = MessageContent;\nconst InputContainer = styled.div`\n  margin-top: ${props => props.theme.spacing.lg};\n  display: flex;\n  flex-direction: column;\n  gap: ${props => props.theme.spacing.sm};\n`;\n_c10 = InputContainer;\nconst InputArea = styled.div`\n  display: flex;\n  gap: ${props => props.theme.spacing.sm};\n`;\n_c11 = InputArea;\nconst MessageInput = styled.textarea`\n  flex: 1;\n  padding: ${props => props.theme.spacing.md};\n  border: 1px solid ${props => props.theme.colors.border};\n  border-radius: ${props => props.theme.borderRadius.md};\n  font-size: ${props => props.theme.fontSizes.md};\n  font-family: ${props => props.theme.fonts.primary};\n  resize: none;\n  min-height: 60px;\n  max-height: 120px;\n  transition: ${props => props.theme.transitions.fast};\n\n  &:focus {\n    outline: none;\n    border-color: ${props => props.theme.colors.primary};\n    box-shadow: 0 0 0 2px ${props => props.theme.colors.primary}20;\n  }\n\n  &::placeholder {\n    color: ${props => props.theme.colors.textLight};\n  }\n`;\n_c12 = MessageInput;\nconst SendButton = styled.button`\n  background: ${props => props.theme.colors.primary};\n  color: ${props => props.theme.colors.textWhite};\n  border: none;\n  border-radius: ${props => props.theme.borderRadius.md};\n  padding: ${props => props.theme.spacing.md} ${props => props.theme.spacing.lg};\n  font-size: ${props => props.theme.fontSizes.md};\n  font-weight: ${props => props.theme.fontWeights.medium};\n  cursor: pointer;\n  transition: ${props => props.theme.transitions.fast};\n  align-self: flex-end;\n\n  &:hover:not(:disabled) {\n    background: ${props => props.theme.colors.primaryLight};\n  }\n\n  &:disabled {\n    background: ${props => props.theme.colors.backgroundDark};\n    color: ${props => props.theme.colors.textLight};\n    cursor: not-allowed;\n  }\n`;\n_c13 = SendButton;\nconst EmptyState = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 200px;\n  color: ${props => props.theme.colors.textLight};\n  text-align: center;\n`;\n_c14 = EmptyState;\nconst EmptyIcon = styled.div`\n  font-size: 3rem;\n  margin-bottom: ${props => props.theme.spacing.md};\n`;\n_c15 = EmptyIcon;\nconst EmptyText = styled.p`\n  font-size: ${props => props.theme.fontSizes.md};\n`;\n_c16 = EmptyText;\nconst formatTime = timestamp => {\n  const date = new Date(timestamp);\n  return date.toLocaleTimeString('zh-CN', {\n    hour: '2-digit',\n    minute: '2-digit'\n  });\n};\nconst ChatPanel = ({\n  messages = [],\n  onSendMessage\n}) => {\n  _s();\n  const [inputValue, setInputValue] = useState('');\n  const messagesEndRef = useRef(null);\n\n  // 自动滚动到底部\n  const scrollToBottom = () => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: 'smooth'\n    });\n  };\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n  const handleSendMessage = () => {\n    const trimmedMessage = inputValue.trim();\n    if (!trimmedMessage) return;\n    setInputValue('');\n\n    // 发送消息到服务器\n    onSendMessage(trimmedMessage);\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(PanelContainer, {\n    children: [/*#__PURE__*/_jsxDEV(PanelHeader, {\n      children: [/*#__PURE__*/_jsxDEV(PanelTitle, {\n        children: \"\\u53D1\\u8A00\\u8BB0\\u5F55\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MessageCount, {\n        children: messages.length\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 230,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(MessagesContainer, {\n      children: /*#__PURE__*/_jsxDEV(MessagesList, {\n        children: [messages.length === 0 ? /*#__PURE__*/_jsxDEV(EmptyState, {\n          children: [/*#__PURE__*/_jsxDEV(EmptyIcon, {\n            children: \"\\uD83D\\uDCAC\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(EmptyText, {\n            children: \"\\u6682\\u65E0\\u6D88\\u606F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 13\n        }, this) : messages.map((message, index) => /*#__PURE__*/_jsxDEV(FadeIn, {\n          delay: `${index * 0.05}s`,\n          children: /*#__PURE__*/_jsxDEV(MessageBubble, {\n            isAppearing: true,\n            children: /*#__PURE__*/_jsxDEV(MessageItem, {\n              type: message.type,\n              children: [/*#__PURE__*/_jsxDEV(MessageHeader, {\n                children: [/*#__PURE__*/_jsxDEV(MessageSender, {\n                  children: message.sender\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(MessageTime, {\n                  children: formatTime(message.timestamp)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(MessageContent, {\n                children: message.message\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 17\n          }, this)\n        }, message.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 15\n        }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: messagesEndRef\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 235,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(InputContainer, {\n      children: [/*#__PURE__*/_jsxDEV(InputArea, {\n        children: /*#__PURE__*/_jsxDEV(MessageInput, {\n          value: inputValue,\n          onChange: e => setInputValue(e.target.value),\n          onKeyPress: handleKeyPress,\n          placeholder: \"\\u8F93\\u5165\\u53D1\\u8A00\\u5185\\u5BB9... (Enter\\u53D1\\u9001\\uFF0CShift+Enter\\u6362\\u884C)\",\n          maxLength: 500\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SendButton, {\n        onClick: handleSendMessage,\n        disabled: !inputValue.trim(),\n        children: \"\\u53D1\\u9001\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 261,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 229,\n    columnNumber: 5\n  }, this);\n};\n_s(ChatPanel, \"+MWy2mHkWkfnD1LneYgJtGWXtTA=\");\n_c17 = ChatPanel;\nexport default ChatPanel;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17;\n$RefreshReg$(_c, \"PanelContainer\");\n$RefreshReg$(_c2, \"PanelHeader\");\n$RefreshReg$(_c3, \"PanelTitle\");\n$RefreshReg$(_c4, \"MessageCount\");\n$RefreshReg$(_c5, \"MessagesContainer\");\n$RefreshReg$(_c6, \"MessagesList\");\n$RefreshReg$(_c7, \"MessageItem\");\n$RefreshReg$(_c8, \"MessageHeader\");\n$RefreshReg$(_c9, \"MessageSender\");\n$RefreshReg$(_c0, \"MessageTime\");\n$RefreshReg$(_c1, \"MessageContent\");\n$RefreshReg$(_c10, \"InputContainer\");\n$RefreshReg$(_c11, \"InputArea\");\n$RefreshReg$(_c12, \"MessageInput\");\n$RefreshReg$(_c13, \"SendButton\");\n$RefreshReg$(_c14, \"EmptyState\");\n$RefreshReg$(_c15, \"EmptyIcon\");\n$RefreshReg$(_c16, \"EmptyText\");\n$RefreshReg$(_c17, \"ChatPanel\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "styled", "MessageBubble", "FadeIn", "jsxDEV", "_jsxDEV", "PanelContainer", "div", "_c", "PanelHeader", "props", "theme", "spacing", "lg", "_c2", "PanelTitle", "h2", "fontSizes", "xl", "fontWeights", "semibold", "colors", "text", "_c3", "MessageCount", "span", "sm", "textLight", "background", "xs", "borderRadius", "md", "_c4", "MessagesContainer", "border", "_c5", "MessagesList", "_c6", "MessageItem", "lineHeights", "relaxed", "type", "info", "infoDark", "warning", "warningDark", "medium", "backgroundLight", "borderLight", "_c7", "MessageHeader", "_c8", "MessageSender", "primary", "_c9", "MessageTime", "_c0", "MessageContent", "_c1", "InputContainer", "_c10", "InputArea", "_c11", "MessageInput", "textarea", "fonts", "transitions", "fast", "_c12", "SendButton", "button", "textWhite", "primaryLight", "backgroundDark", "_c13", "EmptyState", "_c14", "EmptyIcon", "_c15", "EmptyText", "p", "_c16", "formatTime", "timestamp", "date", "Date", "toLocaleTimeString", "hour", "minute", "ChatPanel", "messages", "onSendMessage", "_s", "inputValue", "setInputValue", "messagesEndRef", "scrollToBottom", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "handleSendMessage", "trimmedMessage", "trim", "handleKeyPress", "e", "key", "shift<PERSON>ey", "preventDefault", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "map", "message", "index", "delay", "isAppearing", "sender", "id", "ref", "value", "onChange", "target", "onKeyPress", "placeholder", "max<PERSON><PERSON><PERSON>", "onClick", "disabled", "_c17", "$RefreshReg$"], "sources": ["/private/peiwy/work/ai_projects/wolfkill/frontend/src/components/ChatPanel.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport styled from 'styled-components';\nimport { ChatMessage } from '../types';\nimport { MessageBubble } from './animations/GameAnimations';\nimport { FadeIn } from './animations/AnimatedComponents';\n\ninterface ChatPanelProps {\n  messages?: ChatMessage[];\n  onSendMessage: (message: string) => void;\n}\n\nconst PanelContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n`;\n\nconst PanelHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: ${props => props.theme.spacing.lg};\n`;\n\nconst PanelTitle = styled.h2`\n  font-size: ${props => props.theme.fontSizes.xl};\n  font-weight: ${props => props.theme.fontWeights.semibold};\n  color: ${props => props.theme.colors.text};\n  margin: 0;\n`;\n\nconst MessageCount = styled.span`\n  font-size: ${props => props.theme.fontSizes.sm};\n  color: ${props => props.theme.colors.textLight};\n  background: ${props => props.theme.colors.background};\n  padding: ${props => props.theme.spacing.xs} ${props => props.theme.spacing.sm};\n  border-radius: ${props => props.theme.borderRadius.md};\n`;\n\nconst MessagesContainer = styled.div`\n  flex: 1;\n  background: ${props => props.theme.colors.background};\n  border-radius: ${props => props.theme.borderRadius.lg};\n  border: 1px solid ${props => props.theme.colors.border};\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n`;\n\nconst MessagesList = styled.div`\n  flex: 1;\n  overflow-y: auto;\n  padding: ${props => props.theme.spacing.md};\n  display: flex;\n  flex-direction: column;\n  gap: ${props => props.theme.spacing.sm};\n`;\n\nconst MessageItem = styled.div<{ type: 'system' | 'player' | 'announcement' }>`\n  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};\n  border-radius: ${props => props.theme.borderRadius.md};\n  font-size: ${props => props.theme.fontSizes.sm};\n  line-height: ${props => props.theme.lineHeights.relaxed};\n\n  ${props => {\n    switch (props.type) {\n      case 'system':\n        return `\n          background: ${props.theme.colors.info}20;\n          border-left: 3px solid ${props.theme.colors.info};\n          color: ${props.theme.colors.infoDark};\n        `;\n      case 'announcement':\n        return `\n          background: ${props.theme.colors.warning}20;\n          border-left: 3px solid ${props.theme.colors.warning};\n          color: ${props.theme.colors.warningDark};\n          font-weight: ${props.theme.fontWeights.medium};\n        `;\n      default:\n        return `\n          background: ${props.theme.colors.backgroundLight};\n          border: 1px solid ${props.theme.colors.borderLight};\n          color: ${props.theme.colors.text};\n        `;\n    }\n  }}\n`;\n\nconst MessageHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: ${props => props.theme.spacing.xs};\n`;\n\nconst MessageSender = styled.span`\n  font-weight: ${props => props.theme.fontWeights.medium};\n  color: ${props => props.theme.colors.primary};\n`;\n\nconst MessageTime = styled.span`\n  font-size: ${props => props.theme.fontSizes.xs};\n  color: ${props => props.theme.colors.textLight};\n`;\n\nconst MessageContent = styled.div`\n  word-wrap: break-word;\n`;\n\nconst InputContainer = styled.div`\n  margin-top: ${props => props.theme.spacing.lg};\n  display: flex;\n  flex-direction: column;\n  gap: ${props => props.theme.spacing.sm};\n`;\n\nconst InputArea = styled.div`\n  display: flex;\n  gap: ${props => props.theme.spacing.sm};\n`;\n\nconst MessageInput = styled.textarea`\n  flex: 1;\n  padding: ${props => props.theme.spacing.md};\n  border: 1px solid ${props => props.theme.colors.border};\n  border-radius: ${props => props.theme.borderRadius.md};\n  font-size: ${props => props.theme.fontSizes.md};\n  font-family: ${props => props.theme.fonts.primary};\n  resize: none;\n  min-height: 60px;\n  max-height: 120px;\n  transition: ${props => props.theme.transitions.fast};\n\n  &:focus {\n    outline: none;\n    border-color: ${props => props.theme.colors.primary};\n    box-shadow: 0 0 0 2px ${props => props.theme.colors.primary}20;\n  }\n\n  &::placeholder {\n    color: ${props => props.theme.colors.textLight};\n  }\n`;\n\nconst SendButton = styled.button`\n  background: ${props => props.theme.colors.primary};\n  color: ${props => props.theme.colors.textWhite};\n  border: none;\n  border-radius: ${props => props.theme.borderRadius.md};\n  padding: ${props => props.theme.spacing.md} ${props => props.theme.spacing.lg};\n  font-size: ${props => props.theme.fontSizes.md};\n  font-weight: ${props => props.theme.fontWeights.medium};\n  cursor: pointer;\n  transition: ${props => props.theme.transitions.fast};\n  align-self: flex-end;\n\n  &:hover:not(:disabled) {\n    background: ${props => props.theme.colors.primaryLight};\n  }\n\n  &:disabled {\n    background: ${props => props.theme.colors.backgroundDark};\n    color: ${props => props.theme.colors.textLight};\n    cursor: not-allowed;\n  }\n`;\n\nconst EmptyState = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 200px;\n  color: ${props => props.theme.colors.textLight};\n  text-align: center;\n`;\n\nconst EmptyIcon = styled.div`\n  font-size: 3rem;\n  margin-bottom: ${props => props.theme.spacing.md};\n`;\n\nconst EmptyText = styled.p`\n  font-size: ${props => props.theme.fontSizes.md};\n`;\n\nconst formatTime = (timestamp: string): string => {\n  const date = new Date(timestamp);\n  return date.toLocaleTimeString('zh-CN', {\n    hour: '2-digit',\n    minute: '2-digit'\n  });\n};\n\nconst ChatPanel: React.FC<ChatPanelProps> = ({ messages = [], onSendMessage }) => {\n  const [inputValue, setInputValue] = useState('');\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n\n  // 自动滚动到底部\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  const handleSendMessage = () => {\n    const trimmedMessage = inputValue.trim();\n    if (!trimmedMessage) return;\n\n    setInputValue('');\n\n    // 发送消息到服务器\n    onSendMessage(trimmedMessage);\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n\n\n  return (\n    <PanelContainer>\n      <PanelHeader>\n        <PanelTitle>发言记录</PanelTitle>\n        <MessageCount>{messages.length}</MessageCount>\n      </PanelHeader>\n\n      <MessagesContainer>\n        <MessagesList>\n          {messages.length === 0 ? (\n            <EmptyState>\n              <EmptyIcon>💬</EmptyIcon>\n              <EmptyText>暂无消息</EmptyText>\n            </EmptyState>\n          ) : (\n            messages.map((message, index) => (\n              <FadeIn key={message.id} delay={`${index * 0.05}s`}>\n                <MessageBubble isAppearing={true}>\n                  <MessageItem type={message.type}>\n                    <MessageHeader>\n                      <MessageSender>{message.sender}</MessageSender>\n                      <MessageTime>{formatTime(message.timestamp)}</MessageTime>\n                    </MessageHeader>\n                    <MessageContent>{message.message}</MessageContent>\n                  </MessageItem>\n                </MessageBubble>\n              </FadeIn>\n            ))\n          )}\n          <div ref={messagesEndRef} />\n        </MessagesList>\n      </MessagesContainer>\n\n      <InputContainer>\n        <InputArea>\n          <MessageInput\n            value={inputValue}\n            onChange={(e) => setInputValue(e.target.value)}\n            onKeyPress={handleKeyPress}\n            placeholder=\"输入发言内容... (Enter发送，Shift+Enter换行)\"\n            maxLength={500}\n          />\n        </InputArea>\n        <SendButton\n          onClick={handleSendMessage}\n          disabled={!inputValue.trim()}\n        >\n          发送\n        </SendButton>\n      </InputContainer>\n    </PanelContainer>\n  );\n};\n\nexport default ChatPanel;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,OAAOC,MAAM,MAAM,mBAAmB;AAEtC,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,MAAM,QAAQ,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAOzD,MAAMC,cAAc,GAAGL,MAAM,CAACM,GAAG;AACjC;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAJIF,cAAc;AAMpB,MAAMG,WAAW,GAAGR,MAAM,CAACM,GAAG;AAC9B;AACA;AACA;AACA,mBAAmBG,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACC,EAAE;AAClD,CAAC;AAACC,GAAA,GALIL,WAAW;AAOjB,MAAMM,UAAU,GAAGd,MAAM,CAACe,EAAE;AAC5B,eAAeN,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACM,SAAS,CAACC,EAAE;AAChD,iBAAiBR,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACQ,WAAW,CAACC,QAAQ;AAC1D,WAAWV,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACU,MAAM,CAACC,IAAI;AAC3C;AACA,CAAC;AAACC,GAAA,GALIR,UAAU;AAOhB,MAAMS,YAAY,GAAGvB,MAAM,CAACwB,IAAI;AAChC,eAAef,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACM,SAAS,CAACS,EAAE;AAChD,WAAWhB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACU,MAAM,CAACM,SAAS;AAChD,gBAAgBjB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACU,MAAM,CAACO,UAAU;AACtD,aAAalB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACiB,EAAE,IAAInB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACc,EAAE;AAC/E,mBAAmBhB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACmB,YAAY,CAACC,EAAE;AACvD,CAAC;AAACC,GAAA,GANIR,YAAY;AAQlB,MAAMS,iBAAiB,GAAGhC,MAAM,CAACM,GAAG;AACpC;AACA,gBAAgBG,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACU,MAAM,CAACO,UAAU;AACtD,mBAAmBlB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACmB,YAAY,CAACjB,EAAE;AACvD,sBAAsBH,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACU,MAAM,CAACa,MAAM;AACxD;AACA;AACA;AACA,CAAC;AAACC,GAAA,GARIF,iBAAiB;AAUvB,MAAMG,YAAY,GAAGnC,MAAM,CAACM,GAAG;AAC/B;AACA;AACA,aAAaG,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACmB,EAAE;AAC5C;AACA;AACA,SAASrB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACc,EAAE;AACxC,CAAC;AAACW,GAAA,GAPID,YAAY;AASlB,MAAME,WAAW,GAAGrC,MAAM,CAACM,GAAmD;AAC9E,aAAaG,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACc,EAAE,IAAIhB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACmB,EAAE;AAC/E,mBAAmBrB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACmB,YAAY,CAACC,EAAE;AACvD,eAAerB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACM,SAAS,CAACS,EAAE;AAChD,iBAAiBhB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAAC4B,WAAW,CAACC,OAAO;AACzD;AACA,IAAI9B,KAAK,IAAI;EACT,QAAQA,KAAK,CAAC+B,IAAI;IAChB,KAAK,QAAQ;MACX,OAAO;AACf,wBAAwB/B,KAAK,CAACC,KAAK,CAACU,MAAM,CAACqB,IAAI;AAC/C,mCAAmChC,KAAK,CAACC,KAAK,CAACU,MAAM,CAACqB,IAAI;AAC1D,mBAAmBhC,KAAK,CAACC,KAAK,CAACU,MAAM,CAACsB,QAAQ;AAC9C,SAAS;IACH,KAAK,cAAc;MACjB,OAAO;AACf,wBAAwBjC,KAAK,CAACC,KAAK,CAACU,MAAM,CAACuB,OAAO;AAClD,mCAAmClC,KAAK,CAACC,KAAK,CAACU,MAAM,CAACuB,OAAO;AAC7D,mBAAmBlC,KAAK,CAACC,KAAK,CAACU,MAAM,CAACwB,WAAW;AACjD,yBAAyBnC,KAAK,CAACC,KAAK,CAACQ,WAAW,CAAC2B,MAAM;AACvD,SAAS;IACH;MACE,OAAO;AACf,wBAAwBpC,KAAK,CAACC,KAAK,CAACU,MAAM,CAAC0B,eAAe;AAC1D,8BAA8BrC,KAAK,CAACC,KAAK,CAACU,MAAM,CAAC2B,WAAW;AAC5D,mBAAmBtC,KAAK,CAACC,KAAK,CAACU,MAAM,CAACC,IAAI;AAC1C,SAAS;EACL;AACF,CAAC;AACH,CAAC;AAAC2B,GAAA,GA7BIX,WAAW;AA+BjB,MAAMY,aAAa,GAAGjD,MAAM,CAACM,GAAG;AAChC;AACA;AACA;AACA,mBAAmBG,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACiB,EAAE;AAClD,CAAC;AAACsB,GAAA,GALID,aAAa;AAOnB,MAAME,aAAa,GAAGnD,MAAM,CAACwB,IAAI;AACjC,iBAAiBf,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACQ,WAAW,CAAC2B,MAAM;AACxD,WAAWpC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACU,MAAM,CAACgC,OAAO;AAC9C,CAAC;AAACC,GAAA,GAHIF,aAAa;AAKnB,MAAMG,WAAW,GAAGtD,MAAM,CAACwB,IAAI;AAC/B,eAAef,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACM,SAAS,CAACY,EAAE;AAChD,WAAWnB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACU,MAAM,CAACM,SAAS;AAChD,CAAC;AAAC6B,GAAA,GAHID,WAAW;AAKjB,MAAME,cAAc,GAAGxD,MAAM,CAACM,GAAG;AACjC;AACA,CAAC;AAACmD,GAAA,GAFID,cAAc;AAIpB,MAAME,cAAc,GAAG1D,MAAM,CAACM,GAAG;AACjC,gBAAgBG,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACC,EAAE;AAC/C;AACA;AACA,SAASH,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACc,EAAE;AACxC,CAAC;AAACkC,IAAA,GALID,cAAc;AAOpB,MAAME,SAAS,GAAG5D,MAAM,CAACM,GAAG;AAC5B;AACA,SAASG,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACc,EAAE;AACxC,CAAC;AAACoC,IAAA,GAHID,SAAS;AAKf,MAAME,YAAY,GAAG9D,MAAM,CAAC+D,QAAQ;AACpC;AACA,aAAatD,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACmB,EAAE;AAC5C,sBAAsBrB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACU,MAAM,CAACa,MAAM;AACxD,mBAAmBxB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACmB,YAAY,CAACC,EAAE;AACvD,eAAerB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACM,SAAS,CAACc,EAAE;AAChD,iBAAiBrB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACsD,KAAK,CAACZ,OAAO;AACnD;AACA;AACA;AACA,gBAAgB3C,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACuD,WAAW,CAACC,IAAI;AACrD;AACA;AACA;AACA,oBAAoBzD,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACU,MAAM,CAACgC,OAAO;AACvD,4BAA4B3C,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACU,MAAM,CAACgC,OAAO;AAC/D;AACA;AACA;AACA,aAAa3C,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACU,MAAM,CAACM,SAAS;AAClD;AACA,CAAC;AAACyC,IAAA,GArBIL,YAAY;AAuBlB,MAAMM,UAAU,GAAGpE,MAAM,CAACqE,MAAM;AAChC,gBAAgB5D,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACU,MAAM,CAACgC,OAAO;AACnD,WAAW3C,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACU,MAAM,CAACkD,SAAS;AAChD;AACA,mBAAmB7D,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACmB,YAAY,CAACC,EAAE;AACvD,aAAarB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACmB,EAAE,IAAIrB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACC,EAAE;AAC/E,eAAeH,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACM,SAAS,CAACc,EAAE;AAChD,iBAAiBrB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACQ,WAAW,CAAC2B,MAAM;AACxD;AACA,gBAAgBpC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACuD,WAAW,CAACC,IAAI;AACrD;AACA;AACA;AACA,kBAAkBzD,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACU,MAAM,CAACmD,YAAY;AAC1D;AACA;AACA;AACA,kBAAkB9D,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACU,MAAM,CAACoD,cAAc;AAC5D,aAAa/D,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACU,MAAM,CAACM,SAAS;AAClD;AACA;AACA,CAAC;AAAC+C,IAAA,GArBIL,UAAU;AAuBhB,MAAMM,UAAU,GAAG1E,MAAM,CAACM,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA,WAAWG,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACU,MAAM,CAACM,SAAS;AAChD;AACA,CAAC;AAACiD,IAAA,GARID,UAAU;AAUhB,MAAME,SAAS,GAAG5E,MAAM,CAACM,GAAG;AAC5B;AACA,mBAAmBG,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACmB,EAAE;AAClD,CAAC;AAAC+C,IAAA,GAHID,SAAS;AAKf,MAAME,SAAS,GAAG9E,MAAM,CAAC+E,CAAC;AAC1B,eAAetE,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACM,SAAS,CAACc,EAAE;AAChD,CAAC;AAACkD,IAAA,GAFIF,SAAS;AAIf,MAAMG,UAAU,GAAIC,SAAiB,IAAa;EAChD,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,SAAS,CAAC;EAChC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;IACtCC,IAAI,EAAE,SAAS;IACfC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;AAED,MAAMC,SAAmC,GAAGA,CAAC;EAAEC,QAAQ,GAAG,EAAE;EAAEC;AAAc,CAAC,KAAK;EAAAC,EAAA;EAChF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGhG,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAMiG,cAAc,GAAGhG,MAAM,CAAiB,IAAI,CAAC;;EAEnD;EACA,MAAMiG,cAAc,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAC3B,CAAAA,qBAAA,GAAAF,cAAc,CAACG,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC;EAEDpG,SAAS,CAAC,MAAM;IACdgG,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACN,QAAQ,CAAC,CAAC;EAEd,MAAMW,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,cAAc,GAAGT,UAAU,CAACU,IAAI,CAAC,CAAC;IACxC,IAAI,CAACD,cAAc,EAAE;IAErBR,aAAa,CAAC,EAAE,CAAC;;IAEjB;IACAH,aAAa,CAACW,cAAc,CAAC;EAC/B,CAAC;EAED,MAAME,cAAc,GAAIC,CAAsB,IAAK;IACjD,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,CAAC,CAACE,QAAQ,EAAE;MACpCF,CAAC,CAACG,cAAc,CAAC,CAAC;MAClBP,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC;EAID,oBACEhG,OAAA,CAACC,cAAc;IAAAuG,QAAA,gBACbxG,OAAA,CAACI,WAAW;MAAAoG,QAAA,gBACVxG,OAAA,CAACU,UAAU;QAAA8F,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC7B5G,OAAA,CAACmB,YAAY;QAAAqF,QAAA,EAAEnB,QAAQ,CAACwB;MAAM;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAe,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnC,CAAC,eAEd5G,OAAA,CAAC4B,iBAAiB;MAAA4E,QAAA,eAChBxG,OAAA,CAAC+B,YAAY;QAAAyE,QAAA,GACVnB,QAAQ,CAACwB,MAAM,KAAK,CAAC,gBACpB7G,OAAA,CAACsE,UAAU;UAAAkC,QAAA,gBACTxG,OAAA,CAACwE,SAAS;YAAAgC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eACzB5G,OAAA,CAAC0E,SAAS;YAAA8B,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,GAEbvB,QAAQ,CAACyB,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAC1BhH,OAAA,CAACF,MAAM;UAAkBmH,KAAK,EAAE,GAAGD,KAAK,GAAG,IAAI,GAAI;UAAAR,QAAA,eACjDxG,OAAA,CAACH,aAAa;YAACqH,WAAW,EAAE,IAAK;YAAAV,QAAA,eAC/BxG,OAAA,CAACiC,WAAW;cAACG,IAAI,EAAE2E,OAAO,CAAC3E,IAAK;cAAAoE,QAAA,gBAC9BxG,OAAA,CAAC6C,aAAa;gBAAA2D,QAAA,gBACZxG,OAAA,CAAC+C,aAAa;kBAAAyD,QAAA,EAAEO,OAAO,CAACI;gBAAM;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAgB,CAAC,eAC/C5G,OAAA,CAACkD,WAAW;kBAAAsD,QAAA,EAAE3B,UAAU,CAACkC,OAAO,CAACjC,SAAS;gBAAC;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAc,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,eAChB5G,OAAA,CAACoD,cAAc;gBAAAoD,QAAA,EAAEO,OAAO,CAACA;cAAO;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC,GATLG,OAAO,CAACK,EAAE;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUf,CACT,CACF,eACD5G,OAAA;UAAKqH,GAAG,EAAE3B;QAAe;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEpB5G,OAAA,CAACsD,cAAc;MAAAkD,QAAA,gBACbxG,OAAA,CAACwD,SAAS;QAAAgD,QAAA,eACRxG,OAAA,CAAC0D,YAAY;UACX4D,KAAK,EAAE9B,UAAW;UAClB+B,QAAQ,EAAGnB,CAAC,IAAKX,aAAa,CAACW,CAAC,CAACoB,MAAM,CAACF,KAAK,CAAE;UAC/CG,UAAU,EAAEtB,cAAe;UAC3BuB,WAAW,EAAC,0FAAmC;UAC/CC,SAAS,EAAE;QAAI;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eACZ5G,OAAA,CAACgE,UAAU;QACT4D,OAAO,EAAE5B,iBAAkB;QAC3B6B,QAAQ,EAAE,CAACrC,UAAU,CAACU,IAAI,CAAC,CAAE;QAAAM,QAAA,EAC9B;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAErB,CAAC;AAACrB,EAAA,CApFIH,SAAmC;AAAA0C,IAAA,GAAnC1C,SAAmC;AAsFzC,eAAeA,SAAS;AAAC,IAAAjF,EAAA,EAAAM,GAAA,EAAAS,GAAA,EAAAS,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAY,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAM,IAAA,EAAAM,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAkD,IAAA;AAAAC,YAAA,CAAA5H,EAAA;AAAA4H,YAAA,CAAAtH,GAAA;AAAAsH,YAAA,CAAA7G,GAAA;AAAA6G,YAAA,CAAApG,GAAA;AAAAoG,YAAA,CAAAjG,GAAA;AAAAiG,YAAA,CAAA/F,GAAA;AAAA+F,YAAA,CAAAnF,GAAA;AAAAmF,YAAA,CAAAjF,GAAA;AAAAiF,YAAA,CAAA9E,GAAA;AAAA8E,YAAA,CAAA5E,GAAA;AAAA4E,YAAA,CAAA1E,GAAA;AAAA0E,YAAA,CAAAxE,IAAA;AAAAwE,YAAA,CAAAtE,IAAA;AAAAsE,YAAA,CAAAhE,IAAA;AAAAgE,YAAA,CAAA1D,IAAA;AAAA0D,YAAA,CAAAxD,IAAA;AAAAwD,YAAA,CAAAtD,IAAA;AAAAsD,YAAA,CAAAnD,IAAA;AAAAmD,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}