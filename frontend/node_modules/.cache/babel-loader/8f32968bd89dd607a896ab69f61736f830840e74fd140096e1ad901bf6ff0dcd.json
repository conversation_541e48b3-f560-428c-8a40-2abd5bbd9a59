{"ast": null, "code": "var _jsxFileName = \"/private/peiwy/work/ai_projects/wolfkill/frontend/src/components/PlayerList.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { GamePhase, Role } from '../types';\nimport { getRoleColor } from '../styles/theme';\nimport { StaggeredList } from './animations/AnimatedComponents';\nimport { DeathAnimation, PlayerCardFlip } from './animations/GameAnimations';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ListContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n`;\n_c = ListContainer;\nconst ListHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: ${props => props.theme.spacing.lg};\n`;\n_c2 = ListHeader;\nconst ListTitle = styled.h2`\n  font-size: ${props => props.theme.fontSizes.xl};\n  font-weight: ${props => props.theme.fontWeights.semibold};\n  color: ${props => props.theme.colors.text};\n  margin: 0;\n`;\n_c3 = ListTitle;\nconst PlayerCount = styled.span`\n  font-size: ${props => props.theme.fontSizes.sm};\n  color: ${props => props.theme.colors.textLight};\n  background: ${props => props.theme.colors.background};\n  padding: ${props => props.theme.spacing.xs} ${props => props.theme.spacing.sm};\n  border-radius: ${props => props.theme.borderRadius.md};\n`;\n_c4 = PlayerCount;\nconst PlayersGrid = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${props => props.theme.spacing.sm};\n  flex: 1;\n  overflow-y: auto;\n`;\n_c5 = PlayersGrid;\nconst PlayerCard = styled.div`\n  background: ${props => props.isAlive ? props.theme.colors.backgroundLight : props.theme.colors.backgroundDark};\n  border: 2px solid ${props => props.isSelected ? props.theme.colors.primary : props.theme.colors.border};\n  border-radius: ${props => props.theme.borderRadius.lg};\n  padding: ${props => props.theme.spacing.md};\n  cursor: pointer;\n  transition: ${props => props.theme.transitions.fast};\n  opacity: ${props => props.isAlive ? 1 : 0.6};\n\n  &:hover {\n    border-color: ${props => props.isSelected ? props.theme.colors.primary : props.theme.colors.primaryLight};\n    box-shadow: ${props => props.theme.shadows.md};\n  }\n`;\n_c6 = PlayerCard;\nconst PlayerHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: ${props => props.theme.spacing.sm};\n`;\n_c7 = PlayerHeader;\nconst PlayerName = styled.h3`\n  font-size: ${props => props.theme.fontSizes.md};\n  font-weight: ${props => props.theme.fontWeights.medium};\n  color: ${props => props.theme.colors.text};\n  margin: 0;\n`;\n_c8 = PlayerName;\nconst StatusIndicator = styled.div`\n  width: 8px;\n  height: 8px;\n  border-radius: 50%;\n  background-color: ${props => props.isAlive ? props.theme.colors.success : props.theme.colors.danger};\n`;\n_c9 = StatusIndicator;\nconst PlayerInfo = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${props => props.theme.spacing.xs};\n`;\n_c0 = PlayerInfo;\nconst RoleInfo = styled.div`\n  display: ${props => props.showRole ? 'flex' : 'none'};\n  align-items: center;\n  gap: ${props => props.theme.spacing.xs};\n  font-size: ${props => props.theme.fontSizes.sm};\n  color: ${props => getRoleColor(props.role)};\n  font-weight: ${props => props.theme.fontWeights.medium};\n`;\n_c1 = RoleInfo;\nconst RoleIcon = styled.span`\n  font-size: ${props => props.theme.fontSizes.md};\n`;\n_c10 = RoleIcon;\nconst StatusText = styled.span`\n  font-size: ${props => props.theme.fontSizes.sm};\n  color: ${props => props.isAlive ? props.theme.colors.success : props.theme.colors.danger};\n  font-weight: ${props => props.theme.fontWeights.medium};\n`;\n_c11 = StatusText;\nconst EmptyState = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 200px;\n  color: ${props => props.theme.colors.textLight};\n  text-align: center;\n`;\n_c12 = EmptyState;\nconst EmptyIcon = styled.div`\n  font-size: 3rem;\n  margin-bottom: ${props => props.theme.spacing.md};\n`;\n_c13 = EmptyIcon;\nconst EmptyText = styled.p`\n  font-size: ${props => props.theme.fontSizes.md};\n`;\n_c14 = EmptyText;\nconst getRoleIcon = role => {\n  const roleIcons = {\n    [Role.VILLAGER]: '👨‍🌾',\n    [Role.WEREWOLF]: '🐺',\n    [Role.SEER]: '🔮',\n    [Role.WITCH]: '🧙‍♀️',\n    [Role.GUARD]: '🛡️',\n    [Role.HUNTER]: '🏹'\n  };\n  return roleIcons[role] || '❓';\n};\nconst getRoleName = role => {\n  const roleNames = {\n    [Role.VILLAGER]: '村民',\n    [Role.WEREWOLF]: '狼人',\n    [Role.SEER]: '预言家',\n    [Role.WITCH]: '女巫',\n    [Role.GUARD]: '守卫',\n    [Role.HUNTER]: '猎人'\n  };\n  return roleNames[role] || '未知角色';\n};\nconst PlayerList = ({\n  players,\n  currentPhase,\n  onPlayerSelect\n}) => {\n  _s();\n  const [selectedPlayerId, setSelectedPlayerId] = useState(null);\n  const playerList = Object.values(players);\n  const aliveCount = playerList.filter(p => p.status === 'ALIVE').length;\n  const totalCount = playerList.length;\n\n  // 根据游戏阶段决定是否显示角色\n  const showRoles = currentPhase === GamePhase.GAME_OVER;\n  const handlePlayerClick = playerId => {\n    setSelectedPlayerId(playerId);\n    onPlayerSelect === null || onPlayerSelect === void 0 ? void 0 : onPlayerSelect(playerId);\n  };\n  if (playerList.length === 0) {\n    return /*#__PURE__*/_jsxDEV(ListContainer, {\n      children: [/*#__PURE__*/_jsxDEV(ListHeader, {\n        children: [/*#__PURE__*/_jsxDEV(ListTitle, {\n          children: \"\\u73A9\\u5BB6\\u5217\\u8868\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(PlayerCount, {\n          children: \"0/0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(EmptyState, {\n        children: [/*#__PURE__*/_jsxDEV(EmptyIcon, {\n          children: \"\\uD83D\\uDC65\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(EmptyText, {\n          children: \"\\u6682\\u65E0\\u73A9\\u5BB6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 7\n    }, this);\n  }\n\n  // 按状态排序：存活玩家在前\n  const sortedPlayers = [...playerList].sort((a, b) => {\n    if (a.status === 'ALIVE' && b.status !== 'ALIVE') return -1;\n    if (a.status !== 'ALIVE' && b.status === 'ALIVE') return 1;\n    return a.player_id - b.player_id;\n  });\n  return /*#__PURE__*/_jsxDEV(ListContainer, {\n    children: [/*#__PURE__*/_jsxDEV(ListHeader, {\n      children: [/*#__PURE__*/_jsxDEV(ListTitle, {\n        children: \"\\u73A9\\u5BB6\\u5217\\u8868\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(PlayerCount, {\n        children: [aliveCount, \"/\", totalCount]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PlayersGrid, {\n      children: /*#__PURE__*/_jsxDEV(StaggeredList, {\n        staggerDelay: 0.1,\n        children: sortedPlayers.map(player => /*#__PURE__*/_jsxDEV(DeathAnimation, {\n          isDead: player.status !== 'ALIVE',\n          children: /*#__PURE__*/_jsxDEV(PlayerCardFlip, {\n            isRevealed: showRoles,\n            backContent: /*#__PURE__*/_jsxDEV(PlayerCard, {\n              isAlive: player.status === 'ALIVE',\n              isSelected: selectedPlayerId === player.player_id,\n              onClick: () => handlePlayerClick(player.player_id),\n              children: [/*#__PURE__*/_jsxDEV(PlayerHeader, {\n                children: [/*#__PURE__*/_jsxDEV(PlayerName, {\n                  children: player.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(StatusIndicator, {\n                  isAlive: player.status === 'ALIVE'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(PlayerInfo, {\n                children: [/*#__PURE__*/_jsxDEV(RoleInfo, {\n                  role: player.role,\n                  showRole: true,\n                  children: [/*#__PURE__*/_jsxDEV(RoleIcon, {\n                    children: getRoleIcon(player.role)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 223,\n                    columnNumber: 25\n                  }, this), getRoleName(player.role)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(StatusText, {\n                  isAlive: player.status === 'ALIVE',\n                  children: player.status === 'ALIVE' ? '存活' : '死亡'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 19\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(PlayerCard, {\n              isAlive: player.status === 'ALIVE',\n              isSelected: selectedPlayerId === player.player_id,\n              onClick: () => handlePlayerClick(player.player_id),\n              children: [/*#__PURE__*/_jsxDEV(PlayerHeader, {\n                children: [/*#__PURE__*/_jsxDEV(PlayerName, {\n                  children: player.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(StatusIndicator, {\n                  isAlive: player.status === 'ALIVE'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 240,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(PlayerInfo, {\n                children: [/*#__PURE__*/_jsxDEV(RoleInfo, {\n                  role: player.role,\n                  showRole: showRoles && !showRoles,\n                  children: [/*#__PURE__*/_jsxDEV(RoleIcon, {\n                    children: getRoleIcon(player.role)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 245,\n                    columnNumber: 23\n                  }, this), getRoleName(player.role)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(StatusText, {\n                  isAlive: player.status === 'ALIVE',\n                  children: player.status === 'ALIVE' ? '存活' : '死亡'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 15\n          }, this)\n        }, player.player_id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 202,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 196,\n    columnNumber: 5\n  }, this);\n};\n_s(PlayerList, \"uG9x03DTwbc0je6cJd7+7QhLrxU=\");\n_c15 = PlayerList;\nexport default PlayerList;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15;\n$RefreshReg$(_c, \"ListContainer\");\n$RefreshReg$(_c2, \"ListHeader\");\n$RefreshReg$(_c3, \"ListTitle\");\n$RefreshReg$(_c4, \"PlayerCount\");\n$RefreshReg$(_c5, \"PlayersGrid\");\n$RefreshReg$(_c6, \"PlayerCard\");\n$RefreshReg$(_c7, \"PlayerHeader\");\n$RefreshReg$(_c8, \"PlayerName\");\n$RefreshReg$(_c9, \"StatusIndicator\");\n$RefreshReg$(_c0, \"PlayerInfo\");\n$RefreshReg$(_c1, \"RoleInfo\");\n$RefreshReg$(_c10, \"RoleIcon\");\n$RefreshReg$(_c11, \"StatusText\");\n$RefreshReg$(_c12, \"EmptyState\");\n$RefreshReg$(_c13, \"EmptyIcon\");\n$RefreshReg$(_c14, \"EmptyText\");\n$RefreshReg$(_c15, \"PlayerList\");", "map": {"version": 3, "names": ["React", "useState", "styled", "GamePhase", "Role", "getRoleColor", "StaggeredList", "DeathAnimation", "PlayerCardFlip", "jsxDEV", "_jsxDEV", "ListContainer", "div", "_c", "ListHeader", "props", "theme", "spacing", "lg", "_c2", "ListTitle", "h2", "fontSizes", "xl", "fontWeights", "semibold", "colors", "text", "_c3", "PlayerCount", "span", "sm", "textLight", "background", "xs", "borderRadius", "md", "_c4", "<PERSON><PERSON><PERSON>", "_c5", "PlayerCard", "isAlive", "backgroundLight", "backgroundDark", "isSelected", "primary", "border", "transitions", "fast", "primaryLight", "shadows", "_c6", "<PERSON><PERSON><PERSON><PERSON>", "_c7", "<PERSON><PERSON><PERSON>", "h3", "medium", "_c8", "StatusIndicator", "success", "danger", "_c9", "PlayerInfo", "_c0", "RoleInfo", "showRole", "role", "_c1", "RoleIcon", "_c10", "StatusText", "_c11", "EmptyState", "_c12", "EmptyIcon", "_c13", "EmptyText", "p", "_c14", "getRoleIcon", "roleIcons", "VILLAGER", "WEREWOLF", "SEER", "WITCH", "GUARD", "HUNTER", "getRoleName", "roleNames", "PlayerList", "players", "currentPhase", "onPlayerSelect", "_s", "selectedPlayerId", "setSelectedPlayerId", "playerList", "Object", "values", "aliveCount", "filter", "status", "length", "totalCount", "showRoles", "GAME_OVER", "handlePlayerClick", "playerId", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sortedPlayers", "sort", "a", "b", "player_id", "stagger<PERSON><PERSON><PERSON>", "map", "player", "isDead", "isRevealed", "backContent", "onClick", "name", "_c15", "$RefreshReg$"], "sources": ["/private/peiwy/work/ai_projects/wolfkill/frontend/src/components/PlayerList.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { Player, GamePhase, Role } from '../types';\nimport { getRoleColor } from '../styles/theme';\nimport { StaggeredList, FadeIn } from './animations/AnimatedComponents';\nimport { DeathAnimation, PlayerCardFlip } from './animations/GameAnimations';\n\ninterface PlayerListProps {\n  players: { [key: number]: Player };\n  currentPhase: GamePhase;\n  onPlayerSelect?: (playerId: number) => void;\n}\n\nconst ListContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n`;\n\nconst ListHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: ${props => props.theme.spacing.lg};\n`;\n\nconst ListTitle = styled.h2`\n  font-size: ${props => props.theme.fontSizes.xl};\n  font-weight: ${props => props.theme.fontWeights.semibold};\n  color: ${props => props.theme.colors.text};\n  margin: 0;\n`;\n\nconst PlayerCount = styled.span`\n  font-size: ${props => props.theme.fontSizes.sm};\n  color: ${props => props.theme.colors.textLight};\n  background: ${props => props.theme.colors.background};\n  padding: ${props => props.theme.spacing.xs} ${props => props.theme.spacing.sm};\n  border-radius: ${props => props.theme.borderRadius.md};\n`;\n\nconst PlayersGrid = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${props => props.theme.spacing.sm};\n  flex: 1;\n  overflow-y: auto;\n`;\n\nconst PlayerCard = styled.div<{ isAlive: boolean; isSelected: boolean }>`\n  background: ${props => props.isAlive ? props.theme.colors.backgroundLight : props.theme.colors.backgroundDark};\n  border: 2px solid ${props => props.isSelected ? props.theme.colors.primary : props.theme.colors.border};\n  border-radius: ${props => props.theme.borderRadius.lg};\n  padding: ${props => props.theme.spacing.md};\n  cursor: pointer;\n  transition: ${props => props.theme.transitions.fast};\n  opacity: ${props => props.isAlive ? 1 : 0.6};\n\n  &:hover {\n    border-color: ${props => props.isSelected ? props.theme.colors.primary : props.theme.colors.primaryLight};\n    box-shadow: ${props => props.theme.shadows.md};\n  }\n`;\n\nconst PlayerHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: ${props => props.theme.spacing.sm};\n`;\n\nconst PlayerName = styled.h3`\n  font-size: ${props => props.theme.fontSizes.md};\n  font-weight: ${props => props.theme.fontWeights.medium};\n  color: ${props => props.theme.colors.text};\n  margin: 0;\n`;\n\nconst StatusIndicator = styled.div<{ isAlive: boolean }>`\n  width: 8px;\n  height: 8px;\n  border-radius: 50%;\n  background-color: ${props => props.isAlive ? props.theme.colors.success : props.theme.colors.danger};\n`;\n\nconst PlayerInfo = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${props => props.theme.spacing.xs};\n`;\n\nconst RoleInfo = styled.div<{ role: Role; showRole: boolean }>`\n  display: ${props => props.showRole ? 'flex' : 'none'};\n  align-items: center;\n  gap: ${props => props.theme.spacing.xs};\n  font-size: ${props => props.theme.fontSizes.sm};\n  color: ${props => getRoleColor(props.role)};\n  font-weight: ${props => props.theme.fontWeights.medium};\n`;\n\nconst RoleIcon = styled.span`\n  font-size: ${props => props.theme.fontSizes.md};\n`;\n\nconst StatusText = styled.span<{ isAlive: boolean }>`\n  font-size: ${props => props.theme.fontSizes.sm};\n  color: ${props => props.isAlive ? props.theme.colors.success : props.theme.colors.danger};\n  font-weight: ${props => props.theme.fontWeights.medium};\n`;\n\nconst EmptyState = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 200px;\n  color: ${props => props.theme.colors.textLight};\n  text-align: center;\n`;\n\nconst EmptyIcon = styled.div`\n  font-size: 3rem;\n  margin-bottom: ${props => props.theme.spacing.md};\n`;\n\nconst EmptyText = styled.p`\n  font-size: ${props => props.theme.fontSizes.md};\n`;\n\nconst getRoleIcon = (role: Role): string => {\n  const roleIcons = {\n    [Role.VILLAGER]: '👨‍🌾',\n    [Role.WEREWOLF]: '🐺',\n    [Role.SEER]: '🔮',\n    [Role.WITCH]: '🧙‍♀️',\n    [Role.GUARD]: '🛡️',\n    [Role.HUNTER]: '🏹'\n  };\n  return roleIcons[role] || '❓';\n};\n\nconst getRoleName = (role: Role): string => {\n  const roleNames = {\n    [Role.VILLAGER]: '村民',\n    [Role.WEREWOLF]: '狼人',\n    [Role.SEER]: '预言家',\n    [Role.WITCH]: '女巫',\n    [Role.GUARD]: '守卫',\n    [Role.HUNTER]: '猎人'\n  };\n  return roleNames[role] || '未知角色';\n};\n\nconst PlayerList: React.FC<PlayerListProps> = ({\n  players,\n  currentPhase,\n  onPlayerSelect\n}) => {\n  const [selectedPlayerId, setSelectedPlayerId] = useState<number | null>(null);\n\n  const playerList = Object.values(players);\n  const aliveCount = playerList.filter(p => p.status === 'ALIVE').length;\n  const totalCount = playerList.length;\n\n  // 根据游戏阶段决定是否显示角色\n  const showRoles = currentPhase === GamePhase.GAME_OVER;\n\n  const handlePlayerClick = (playerId: number) => {\n    setSelectedPlayerId(playerId);\n    onPlayerSelect?.(playerId);\n  };\n\n  if (playerList.length === 0) {\n    return (\n      <ListContainer>\n        <ListHeader>\n          <ListTitle>玩家列表</ListTitle>\n          <PlayerCount>0/0</PlayerCount>\n        </ListHeader>\n        <EmptyState>\n          <EmptyIcon>👥</EmptyIcon>\n          <EmptyText>暂无玩家</EmptyText>\n        </EmptyState>\n      </ListContainer>\n    );\n  }\n\n  // 按状态排序：存活玩家在前\n  const sortedPlayers = [...playerList].sort((a, b) => {\n    if (a.status === 'ALIVE' && b.status !== 'ALIVE') return -1;\n    if (a.status !== 'ALIVE' && b.status === 'ALIVE') return 1;\n    return a.player_id - b.player_id;\n  });\n\n  return (\n    <ListContainer>\n      <ListHeader>\n        <ListTitle>玩家列表</ListTitle>\n        <PlayerCount>{aliveCount}/{totalCount}</PlayerCount>\n      </ListHeader>\n\n      <PlayersGrid>\n        <StaggeredList staggerDelay={0.1}>\n          {sortedPlayers.map(player => (\n            <DeathAnimation\n              key={player.player_id}\n              isDead={player.status !== 'ALIVE'}\n            >\n              <PlayerCardFlip\n                isRevealed={showRoles}\n                backContent={\n                  <PlayerCard\n                    isAlive={player.status === 'ALIVE'}\n                    isSelected={selectedPlayerId === player.player_id}\n                    onClick={() => handlePlayerClick(player.player_id)}\n                  >\n                    <PlayerHeader>\n                      <PlayerName>{player.name}</PlayerName>\n                      <StatusIndicator isAlive={player.status === 'ALIVE'} />\n                    </PlayerHeader>\n                    <PlayerInfo>\n                      <RoleInfo role={player.role} showRole={true}>\n                        <RoleIcon>{getRoleIcon(player.role)}</RoleIcon>\n                        {getRoleName(player.role)}\n                      </RoleInfo>\n                      <StatusText isAlive={player.status === 'ALIVE'}>\n                        {player.status === 'ALIVE' ? '存活' : '死亡'}\n                      </StatusText>\n                    </PlayerInfo>\n                  </PlayerCard>\n                }\n              >\n                <PlayerCard\n                  isAlive={player.status === 'ALIVE'}\n                  isSelected={selectedPlayerId === player.player_id}\n                  onClick={() => handlePlayerClick(player.player_id)}\n                >\n                  <PlayerHeader>\n                    <PlayerName>{player.name}</PlayerName>\n                    <StatusIndicator isAlive={player.status === 'ALIVE'} />\n                  </PlayerHeader>\n\n                  <PlayerInfo>\n                    <RoleInfo role={player.role} showRole={showRoles && !showRoles}>\n                      <RoleIcon>{getRoleIcon(player.role)}</RoleIcon>\n                      {getRoleName(player.role)}\n                    </RoleInfo>\n\n                    <StatusText isAlive={player.status === 'ALIVE'}>\n                      {player.status === 'ALIVE' ? '存活' : '死亡'}\n                    </StatusText>\n                  </PlayerInfo>\n                </PlayerCard>\n              </PlayerCardFlip>\n            </DeathAnimation>\n          ))}\n        </StaggeredList>\n      </PlayersGrid>\n    </ListContainer>\n  );\n};\n\nexport default PlayerList;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAAiBC,SAAS,EAAEC,IAAI,QAAQ,UAAU;AAClD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAgB,iCAAiC;AACvE,SAASC,cAAc,EAAEC,cAAc,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQ7E,MAAMC,aAAa,GAAGT,MAAM,CAACU,GAAG;AAChC;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAJIF,aAAa;AAMnB,MAAMG,UAAU,GAAGZ,MAAM,CAACU,GAAG;AAC7B;AACA;AACA;AACA,mBAAmBG,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACC,EAAE;AAClD,CAAC;AAACC,GAAA,GALIL,UAAU;AAOhB,MAAMM,SAAS,GAAGlB,MAAM,CAACmB,EAAE;AAC3B,eAAeN,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACM,SAAS,CAACC,EAAE;AAChD,iBAAiBR,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACQ,WAAW,CAACC,QAAQ;AAC1D,WAAWV,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACU,MAAM,CAACC,IAAI;AAC3C;AACA,CAAC;AAACC,GAAA,GALIR,SAAS;AAOf,MAAMS,WAAW,GAAG3B,MAAM,CAAC4B,IAAI;AAC/B,eAAef,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACM,SAAS,CAACS,EAAE;AAChD,WAAWhB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACU,MAAM,CAACM,SAAS;AAChD,gBAAgBjB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACU,MAAM,CAACO,UAAU;AACtD,aAAalB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACiB,EAAE,IAAInB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACc,EAAE;AAC/E,mBAAmBhB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACmB,YAAY,CAACC,EAAE;AACvD,CAAC;AAACC,GAAA,GANIR,WAAW;AAQjB,MAAMS,WAAW,GAAGpC,MAAM,CAACU,GAAG;AAC9B;AACA;AACA,SAASG,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACc,EAAE;AACxC;AACA;AACA,CAAC;AAACQ,GAAA,GANID,WAAW;AAQjB,MAAME,UAAU,GAAGtC,MAAM,CAACU,GAA8C;AACxE,gBAAgBG,KAAK,IAAIA,KAAK,CAAC0B,OAAO,GAAG1B,KAAK,CAACC,KAAK,CAACU,MAAM,CAACgB,eAAe,GAAG3B,KAAK,CAACC,KAAK,CAACU,MAAM,CAACiB,cAAc;AAC/G,sBAAsB5B,KAAK,IAAIA,KAAK,CAAC6B,UAAU,GAAG7B,KAAK,CAACC,KAAK,CAACU,MAAM,CAACmB,OAAO,GAAG9B,KAAK,CAACC,KAAK,CAACU,MAAM,CAACoB,MAAM;AACxG,mBAAmB/B,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACmB,YAAY,CAACjB,EAAE;AACvD,aAAaH,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACmB,EAAE;AAC5C;AACA,gBAAgBrB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAAC+B,WAAW,CAACC,IAAI;AACrD,aAAajC,KAAK,IAAIA,KAAK,CAAC0B,OAAO,GAAG,CAAC,GAAG,GAAG;AAC7C;AACA;AACA,oBAAoB1B,KAAK,IAAIA,KAAK,CAAC6B,UAAU,GAAG7B,KAAK,CAACC,KAAK,CAACU,MAAM,CAACmB,OAAO,GAAG9B,KAAK,CAACC,KAAK,CAACU,MAAM,CAACuB,YAAY;AAC5G,kBAAkBlC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACkC,OAAO,CAACd,EAAE;AACjD;AACA,CAAC;AAACe,GAAA,GAbIX,UAAU;AAehB,MAAMY,YAAY,GAAGlD,MAAM,CAACU,GAAG;AAC/B;AACA;AACA;AACA,mBAAmBG,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACc,EAAE;AAClD,CAAC;AAACsB,GAAA,GALID,YAAY;AAOlB,MAAME,UAAU,GAAGpD,MAAM,CAACqD,EAAE;AAC5B,eAAexC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACM,SAAS,CAACc,EAAE;AAChD,iBAAiBrB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACQ,WAAW,CAACgC,MAAM;AACxD,WAAWzC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACU,MAAM,CAACC,IAAI;AAC3C;AACA,CAAC;AAAC8B,GAAA,GALIH,UAAU;AAOhB,MAAMI,eAAe,GAAGxD,MAAM,CAACU,GAAyB;AACxD;AACA;AACA;AACA,sBAAsBG,KAAK,IAAIA,KAAK,CAAC0B,OAAO,GAAG1B,KAAK,CAACC,KAAK,CAACU,MAAM,CAACiC,OAAO,GAAG5C,KAAK,CAACC,KAAK,CAACU,MAAM,CAACkC,MAAM;AACrG,CAAC;AAACC,GAAA,GALIH,eAAe;AAOrB,MAAMI,UAAU,GAAG5D,MAAM,CAACU,GAAG;AAC7B;AACA;AACA,SAASG,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACiB,EAAE;AACxC,CAAC;AAAC6B,GAAA,GAJID,UAAU;AAMhB,MAAME,QAAQ,GAAG9D,MAAM,CAACU,GAAsC;AAC9D,aAAaG,KAAK,IAAIA,KAAK,CAACkD,QAAQ,GAAG,MAAM,GAAG,MAAM;AACtD;AACA,SAASlD,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACiB,EAAE;AACxC,eAAenB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACM,SAAS,CAACS,EAAE;AAChD,WAAWhB,KAAK,IAAIV,YAAY,CAACU,KAAK,CAACmD,IAAI,CAAC;AAC5C,iBAAiBnD,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACQ,WAAW,CAACgC,MAAM;AACxD,CAAC;AAACW,GAAA,GAPIH,QAAQ;AASd,MAAMI,QAAQ,GAAGlE,MAAM,CAAC4B,IAAI;AAC5B,eAAef,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACM,SAAS,CAACc,EAAE;AAChD,CAAC;AAACiC,IAAA,GAFID,QAAQ;AAId,MAAME,UAAU,GAAGpE,MAAM,CAAC4B,IAA0B;AACpD,eAAef,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACM,SAAS,CAACS,EAAE;AAChD,WAAWhB,KAAK,IAAIA,KAAK,CAAC0B,OAAO,GAAG1B,KAAK,CAACC,KAAK,CAACU,MAAM,CAACiC,OAAO,GAAG5C,KAAK,CAACC,KAAK,CAACU,MAAM,CAACkC,MAAM;AAC1F,iBAAiB7C,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACQ,WAAW,CAACgC,MAAM;AACxD,CAAC;AAACe,IAAA,GAJID,UAAU;AAMhB,MAAME,UAAU,GAAGtE,MAAM,CAACU,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA,WAAWG,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACU,MAAM,CAACM,SAAS;AAChD;AACA,CAAC;AAACyC,IAAA,GARID,UAAU;AAUhB,MAAME,SAAS,GAAGxE,MAAM,CAACU,GAAG;AAC5B;AACA,mBAAmBG,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACmB,EAAE;AAClD,CAAC;AAACuC,IAAA,GAHID,SAAS;AAKf,MAAME,SAAS,GAAG1E,MAAM,CAAC2E,CAAC;AAC1B,eAAe9D,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACM,SAAS,CAACc,EAAE;AAChD,CAAC;AAAC0C,IAAA,GAFIF,SAAS;AAIf,MAAMG,WAAW,GAAIb,IAAU,IAAa;EAC1C,MAAMc,SAAS,GAAG;IAChB,CAAC5E,IAAI,CAAC6E,QAAQ,GAAG,OAAO;IACxB,CAAC7E,IAAI,CAAC8E,QAAQ,GAAG,IAAI;IACrB,CAAC9E,IAAI,CAAC+E,IAAI,GAAG,IAAI;IACjB,CAAC/E,IAAI,CAACgF,KAAK,GAAG,OAAO;IACrB,CAAChF,IAAI,CAACiF,KAAK,GAAG,KAAK;IACnB,CAACjF,IAAI,CAACkF,MAAM,GAAG;EACjB,CAAC;EACD,OAAON,SAAS,CAACd,IAAI,CAAC,IAAI,GAAG;AAC/B,CAAC;AAED,MAAMqB,WAAW,GAAIrB,IAAU,IAAa;EAC1C,MAAMsB,SAAS,GAAG;IAChB,CAACpF,IAAI,CAAC6E,QAAQ,GAAG,IAAI;IACrB,CAAC7E,IAAI,CAAC8E,QAAQ,GAAG,IAAI;IACrB,CAAC9E,IAAI,CAAC+E,IAAI,GAAG,KAAK;IAClB,CAAC/E,IAAI,CAACgF,KAAK,GAAG,IAAI;IAClB,CAAChF,IAAI,CAACiF,KAAK,GAAG,IAAI;IAClB,CAACjF,IAAI,CAACkF,MAAM,GAAG;EACjB,CAAC;EACD,OAAOE,SAAS,CAACtB,IAAI,CAAC,IAAI,MAAM;AAClC,CAAC;AAED,MAAMuB,UAAqC,GAAGA,CAAC;EAC7CC,OAAO;EACPC,YAAY;EACZC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG9F,QAAQ,CAAgB,IAAI,CAAC;EAE7E,MAAM+F,UAAU,GAAGC,MAAM,CAACC,MAAM,CAACR,OAAO,CAAC;EACzC,MAAMS,UAAU,GAAGH,UAAU,CAACI,MAAM,CAACvB,CAAC,IAAIA,CAAC,CAACwB,MAAM,KAAK,OAAO,CAAC,CAACC,MAAM;EACtE,MAAMC,UAAU,GAAGP,UAAU,CAACM,MAAM;;EAEpC;EACA,MAAME,SAAS,GAAGb,YAAY,KAAKxF,SAAS,CAACsG,SAAS;EAEtD,MAAMC,iBAAiB,GAAIC,QAAgB,IAAK;IAC9CZ,mBAAmB,CAACY,QAAQ,CAAC;IAC7Bf,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAGe,QAAQ,CAAC;EAC5B,CAAC;EAED,IAAIX,UAAU,CAACM,MAAM,KAAK,CAAC,EAAE;IAC3B,oBACE5F,OAAA,CAACC,aAAa;MAAAiG,QAAA,gBACZlG,OAAA,CAACI,UAAU;QAAA8F,QAAA,gBACTlG,OAAA,CAACU,SAAS;UAAAwF,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eAC3BtG,OAAA,CAACmB,WAAW;UAAA+E,QAAA,EAAC;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC,eACbtG,OAAA,CAAC8D,UAAU;QAAAoC,QAAA,gBACTlG,OAAA,CAACgE,SAAS;UAAAkC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eACzBtG,OAAA,CAACkE,SAAS;UAAAgC,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAEpB;;EAEA;EACA,MAAMC,aAAa,GAAG,CAAC,GAAGjB,UAAU,CAAC,CAACkB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;IACnD,IAAID,CAAC,CAACd,MAAM,KAAK,OAAO,IAAIe,CAAC,CAACf,MAAM,KAAK,OAAO,EAAE,OAAO,CAAC,CAAC;IAC3D,IAAIc,CAAC,CAACd,MAAM,KAAK,OAAO,IAAIe,CAAC,CAACf,MAAM,KAAK,OAAO,EAAE,OAAO,CAAC;IAC1D,OAAOc,CAAC,CAACE,SAAS,GAAGD,CAAC,CAACC,SAAS;EAClC,CAAC,CAAC;EAEF,oBACE3G,OAAA,CAACC,aAAa;IAAAiG,QAAA,gBACZlG,OAAA,CAACI,UAAU;MAAA8F,QAAA,gBACTlG,OAAA,CAACU,SAAS;QAAAwF,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC,eAC3BtG,OAAA,CAACmB,WAAW;QAAA+E,QAAA,GAAET,UAAU,EAAC,GAAC,EAACI,UAAU;MAAA;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CAAC,eAEbtG,OAAA,CAAC4B,WAAW;MAAAsE,QAAA,eACVlG,OAAA,CAACJ,aAAa;QAACgH,YAAY,EAAE,GAAI;QAAAV,QAAA,EAC9BK,aAAa,CAACM,GAAG,CAACC,MAAM,iBACvB9G,OAAA,CAACH,cAAc;UAEbkH,MAAM,EAAED,MAAM,CAACnB,MAAM,KAAK,OAAQ;UAAAO,QAAA,eAElClG,OAAA,CAACF,cAAc;YACbkH,UAAU,EAAElB,SAAU;YACtBmB,WAAW,eACTjH,OAAA,CAAC8B,UAAU;cACTC,OAAO,EAAE+E,MAAM,CAACnB,MAAM,KAAK,OAAQ;cACnCzD,UAAU,EAAEkD,gBAAgB,KAAK0B,MAAM,CAACH,SAAU;cAClDO,OAAO,EAAEA,CAAA,KAAMlB,iBAAiB,CAACc,MAAM,CAACH,SAAS,CAAE;cAAAT,QAAA,gBAEnDlG,OAAA,CAAC0C,YAAY;gBAAAwD,QAAA,gBACXlG,OAAA,CAAC4C,UAAU;kBAAAsD,QAAA,EAAEY,MAAM,CAACK;gBAAI;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eACtCtG,OAAA,CAACgD,eAAe;kBAACjB,OAAO,EAAE+E,MAAM,CAACnB,MAAM,KAAK;gBAAQ;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC,eACftG,OAAA,CAACoD,UAAU;gBAAA8C,QAAA,gBACTlG,OAAA,CAACsD,QAAQ;kBAACE,IAAI,EAAEsD,MAAM,CAACtD,IAAK;kBAACD,QAAQ,EAAE,IAAK;kBAAA2C,QAAA,gBAC1ClG,OAAA,CAAC0D,QAAQ;oBAAAwC,QAAA,EAAE7B,WAAW,CAACyC,MAAM,CAACtD,IAAI;kBAAC;oBAAA2C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,EAC9CzB,WAAW,CAACiC,MAAM,CAACtD,IAAI,CAAC;gBAAA;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC,eACXtG,OAAA,CAAC4D,UAAU;kBAAC7B,OAAO,EAAE+E,MAAM,CAACnB,MAAM,KAAK,OAAQ;kBAAAO,QAAA,EAC5CY,MAAM,CAACnB,MAAM,KAAK,OAAO,GAAG,IAAI,GAAG;gBAAI;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACb;YAAAJ,QAAA,eAEDlG,OAAA,CAAC8B,UAAU;cACTC,OAAO,EAAE+E,MAAM,CAACnB,MAAM,KAAK,OAAQ;cACnCzD,UAAU,EAAEkD,gBAAgB,KAAK0B,MAAM,CAACH,SAAU;cAClDO,OAAO,EAAEA,CAAA,KAAMlB,iBAAiB,CAACc,MAAM,CAACH,SAAS,CAAE;cAAAT,QAAA,gBAEnDlG,OAAA,CAAC0C,YAAY;gBAAAwD,QAAA,gBACXlG,OAAA,CAAC4C,UAAU;kBAAAsD,QAAA,EAAEY,MAAM,CAACK;gBAAI;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eACtCtG,OAAA,CAACgD,eAAe;kBAACjB,OAAO,EAAE+E,MAAM,CAACnB,MAAM,KAAK;gBAAQ;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC,eAEftG,OAAA,CAACoD,UAAU;gBAAA8C,QAAA,gBACTlG,OAAA,CAACsD,QAAQ;kBAACE,IAAI,EAAEsD,MAAM,CAACtD,IAAK;kBAACD,QAAQ,EAAEuC,SAAS,IAAI,CAACA,SAAU;kBAAAI,QAAA,gBAC7DlG,OAAA,CAAC0D,QAAQ;oBAAAwC,QAAA,EAAE7B,WAAW,CAACyC,MAAM,CAACtD,IAAI;kBAAC;oBAAA2C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,EAC9CzB,WAAW,CAACiC,MAAM,CAACtD,IAAI,CAAC;gBAAA;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC,eAEXtG,OAAA,CAAC4D,UAAU;kBAAC7B,OAAO,EAAE+E,MAAM,CAACnB,MAAM,KAAK,OAAQ;kBAAAO,QAAA,EAC5CY,MAAM,CAACnB,MAAM,KAAK,OAAO,GAAG,IAAI,GAAG;gBAAI;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC,GAhDZQ,MAAM,CAACH,SAAS;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAiDP,CACjB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEpB,CAAC;AAACnB,EAAA,CA3GIJ,UAAqC;AAAAqC,IAAA,GAArCrC,UAAqC;AA6G3C,eAAeA,UAAU;AAAC,IAAA5E,EAAA,EAAAM,GAAA,EAAAS,GAAA,EAAAS,GAAA,EAAAE,GAAA,EAAAY,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAgD,IAAA;AAAAC,YAAA,CAAAlH,EAAA;AAAAkH,YAAA,CAAA5G,GAAA;AAAA4G,YAAA,CAAAnG,GAAA;AAAAmG,YAAA,CAAA1F,GAAA;AAAA0F,YAAA,CAAAxF,GAAA;AAAAwF,YAAA,CAAA5E,GAAA;AAAA4E,YAAA,CAAA1E,GAAA;AAAA0E,YAAA,CAAAtE,GAAA;AAAAsE,YAAA,CAAAlE,GAAA;AAAAkE,YAAA,CAAAhE,GAAA;AAAAgE,YAAA,CAAA5D,GAAA;AAAA4D,YAAA,CAAA1D,IAAA;AAAA0D,YAAA,CAAAxD,IAAA;AAAAwD,YAAA,CAAAtD,IAAA;AAAAsD,YAAA,CAAApD,IAAA;AAAAoD,YAAA,CAAAjD,IAAA;AAAAiD,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}