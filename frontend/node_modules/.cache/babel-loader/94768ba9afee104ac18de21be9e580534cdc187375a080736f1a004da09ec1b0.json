{"ast": null, "code": "import axios from 'axios';\n// API基础配置\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';\nclass GameAPI {\n  constructor() {\n    this.client = void 0;\n    this.client = axios.create({\n      baseURL: API_BASE_URL,\n      timeout: 10000,\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n\n    // 请求拦截器\n    this.client.interceptors.request.use(config => {\n      // 添加认证token等\n      const token = localStorage.getItem('auth_token');\n      if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n      }\n      return config;\n    }, error => {\n      return Promise.reject(error);\n    });\n\n    // 响应拦截器\n    this.client.interceptors.response.use(response => {\n      return response;\n    }, error => {\n      console.error('API Error:', error);\n\n      // 处理网络错误\n      if (!error.response) {\n        return Promise.reject(new Error('网络连接失败，请检查网络设置'));\n      }\n\n      // 处理HTTP错误\n      const {\n        status,\n        data\n      } = error.response;\n      let errorMessage = '请求失败';\n      switch (status) {\n        case 400:\n          errorMessage = (data === null || data === void 0 ? void 0 : data.message) || '请求参数错误';\n          break;\n        case 401:\n          errorMessage = '未授权访问';\n          // 清除本地token\n          localStorage.removeItem('auth_token');\n          break;\n        case 403:\n          errorMessage = '访问被拒绝';\n          break;\n        case 404:\n          errorMessage = '请求的资源不存在';\n          break;\n        case 500:\n          errorMessage = '服务器内部错误';\n          break;\n        default:\n          errorMessage = (data === null || data === void 0 ? void 0 : data.message) || `请求失败 (${status})`;\n      }\n      return Promise.reject(new Error(errorMessage));\n    });\n  }\n\n  // 创建游戏\n  async createGame(config) {\n    try {\n      const response = await this.client.post('/games', config);\n      return {\n        success: true,\n        data: response.data,\n        message: '游戏创建成功'\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '创建游戏失败'\n      };\n    }\n  }\n\n  // 加入游戏\n  async joinGame(gameId) {\n    try {\n      const response = await this.client.post(`/games/${gameId}/join`);\n      return {\n        success: true,\n        data: response.data,\n        message: '成功加入游戏'\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '加入游戏失败'\n      };\n    }\n  }\n\n  // 获取游戏状态\n  async getGameState(gameId) {\n    try {\n      const response = await this.client.get(`/games/${gameId}`);\n      return {\n        success: true,\n        data: response.data\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '获取游戏状态失败'\n      };\n    }\n  }\n\n  // 开始游戏\n  async startGame(gameId) {\n    try {\n      const response = await this.client.post(`/games/${gameId}/start`);\n      return {\n        success: true,\n        data: response.data,\n        message: '游戏已开始'\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '开始游戏失败'\n      };\n    }\n  }\n\n  // 投票\n  async submitVote(gameId, vote) {\n    try {\n      const response = await this.client.post(`/games/${gameId}/vote`, vote);\n      return {\n        success: true,\n        data: response.data,\n        message: '投票提交成功'\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '投票失败'\n      };\n    }\n  }\n\n  // 执行行动\n  async submitAction(gameId, action) {\n    try {\n      const response = await this.client.post(`/games/${gameId}/action`, action);\n      return {\n        success: true,\n        data: response.data,\n        message: '行动执行成功'\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '行动执行失败'\n      };\n    }\n  }\n\n  // 发送聊天消息\n  async sendChatMessage(gameId, message) {\n    try {\n      const response = await this.client.post(`/games/${gameId}/chat`, {\n        message\n      });\n      return {\n        success: true,\n        data: response.data,\n        message: '消息发送成功'\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '消息发送失败'\n      };\n    }\n  }\n\n  // 获取游戏历史\n  async getGameHistory(gameId) {\n    try {\n      const response = await this.client.get(`/games/${gameId}/history`);\n      return {\n        success: true,\n        data: response.data\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '获取游戏历史失败'\n      };\n    }\n  }\n\n  // 健康检查\n  async healthCheck() {\n    try {\n      const response = await this.client.get('/health');\n      return {\n        success: true,\n        data: response.data\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '服务器连接失败'\n      };\n    }\n  }\n}\n\n// 导出API实例\nexport const gameApi = new GameAPI();", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "GameAPI", "constructor", "client", "create", "baseURL", "timeout", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "console", "Error", "status", "data", "errorMessage", "message", "removeItem", "createGame", "post", "success", "joinGame", "gameId", "getGameState", "get", "startGame", "submitVote", "vote", "submitAction", "action", "sendChatMessage", "getGameHistory", "healthCheck", "gameApi"], "sources": ["/private/peiwy/work/ai_projects/wolfkill/frontend/src/services/api.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosResponse } from 'axios';\nimport { GameState, GameConfig, Vote, Action, ApiResponse } from '../types';\n\n// API基础配置\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';\n\nclass GameAPI {\n  private client: AxiosInstance;\n\n  constructor() {\n    this.client = axios.create({\n      baseURL: API_BASE_URL,\n      timeout: 10000,\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n\n    // 请求拦截器\n    this.client.interceptors.request.use(\n      (config) => {\n        // 添加认证token等\n        const token = localStorage.getItem('auth_token');\n        if (token) {\n          config.headers.Authorization = `Bearer ${token}`;\n        }\n        return config;\n      },\n      (error) => {\n        return Promise.reject(error);\n      }\n    );\n\n    // 响应拦截器\n    this.client.interceptors.response.use(\n      (response: AxiosResponse) => {\n        return response;\n      },\n      (error) => {\n        console.error('API Error:', error);\n\n        // 处理网络错误\n        if (!error.response) {\n          return Promise.reject(new Error('网络连接失败，请检查网络设置'));\n        }\n\n        // 处理HTTP错误\n        const { status, data } = error.response;\n        let errorMessage = '请求失败';\n\n        switch (status) {\n          case 400:\n            errorMessage = data?.message || '请求参数错误';\n            break;\n          case 401:\n            errorMessage = '未授权访问';\n            // 清除本地token\n            localStorage.removeItem('auth_token');\n            break;\n          case 403:\n            errorMessage = '访问被拒绝';\n            break;\n          case 404:\n            errorMessage = '请求的资源不存在';\n            break;\n          case 500:\n            errorMessage = '服务器内部错误';\n            break;\n          default:\n            errorMessage = data?.message || `请求失败 (${status})`;\n        }\n\n        return Promise.reject(new Error(errorMessage));\n      }\n    );\n  }\n\n  // 创建游戏\n  async createGame(config: GameConfig): Promise<ApiResponse<GameState>> {\n    try {\n      const response = await this.client.post('/games', config);\n      return {\n        success: true,\n        data: response.data,\n        message: '游戏创建成功'\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '创建游戏失败'\n      };\n    }\n  }\n\n  // 加入游戏\n  async joinGame(gameId: string): Promise<ApiResponse<GameState>> {\n    try {\n      const response = await this.client.post(`/games/${gameId}/join`);\n      return {\n        success: true,\n        data: response.data,\n        message: '成功加入游戏'\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '加入游戏失败'\n      };\n    }\n  }\n\n  // 获取游戏状态\n  async getGameState(gameId: string): Promise<ApiResponse<GameState>> {\n    try {\n      const response = await this.client.get(`/games/${gameId}`);\n      return {\n        success: true,\n        data: response.data\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '获取游戏状态失败'\n      };\n    }\n  }\n\n  // 开始游戏\n  async startGame(gameId: string): Promise<ApiResponse<GameState>> {\n    try {\n      const response = await this.client.post(`/games/${gameId}/start`);\n      return {\n        success: true,\n        data: response.data,\n        message: '游戏已开始'\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '开始游戏失败'\n      };\n    }\n  }\n\n  // 投票\n  async submitVote(gameId: string, vote: Vote): Promise<ApiResponse<any>> {\n    try {\n      const response = await this.client.post(`/games/${gameId}/vote`, vote);\n      return {\n        success: true,\n        data: response.data,\n        message: '投票提交成功'\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '投票失败'\n      };\n    }\n  }\n\n  // 执行行动\n  async submitAction(gameId: string, action: Action): Promise<ApiResponse<any>> {\n    try {\n      const response = await this.client.post(`/games/${gameId}/action`, action);\n      return {\n        success: true,\n        data: response.data,\n        message: '行动执行成功'\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '行动执行失败'\n      };\n    }\n  }\n\n  // 发送聊天消息\n  async sendChatMessage(gameId: string, message: string): Promise<ApiResponse<any>> {\n    try {\n      const response = await this.client.post(`/games/${gameId}/chat`, { message });\n      return {\n        success: true,\n        data: response.data,\n        message: '消息发送成功'\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '消息发送失败'\n      };\n    }\n  }\n\n  // 获取游戏历史\n  async getGameHistory(gameId: string): Promise<ApiResponse<any>> {\n    try {\n      const response = await this.client.get(`/games/${gameId}/history`);\n      return {\n        success: true,\n        data: response.data\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '获取游戏历史失败'\n      };\n    }\n  }\n\n  // 健康检查\n  async healthCheck(): Promise<ApiResponse<any>> {\n    try {\n      const response = await this.client.get('/health');\n      return {\n        success: true,\n        data: response.data\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '服务器连接失败'\n      };\n    }\n  }\n}\n\n// 导出API实例\nexport const gameApi = new GameAPI();"], "mappings": "AAAA,OAAOA,KAAK,MAAwC,OAAO;AAG3D;AACA,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;AAEjF,MAAMC,OAAO,CAAC;EAGZC,WAAWA,CAAA,EAAG;IAAA,KAFNC,MAAM;IAGZ,IAAI,CAACA,MAAM,GAAGP,KAAK,CAACQ,MAAM,CAAC;MACzBC,OAAO,EAAER,YAAY;MACrBS,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;;IAEF;IACA,IAAI,CAACJ,MAAM,CAACK,YAAY,CAACC,OAAO,CAACC,GAAG,CACjCC,MAAM,IAAK;MACV;MACA,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;MAChD,IAAIF,KAAK,EAAE;QACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;MAClD;MACA,OAAOD,MAAM;IACf,CAAC,EACAK,KAAK,IAAK;MACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;IAC9B,CACF,CAAC;;IAED;IACA,IAAI,CAACb,MAAM,CAACK,YAAY,CAACW,QAAQ,CAACT,GAAG,CAClCS,QAAuB,IAAK;MAC3B,OAAOA,QAAQ;IACjB,CAAC,EACAH,KAAK,IAAK;MACTI,OAAO,CAACJ,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;;MAElC;MACA,IAAI,CAACA,KAAK,CAACG,QAAQ,EAAE;QACnB,OAAOF,OAAO,CAACC,MAAM,CAAC,IAAIG,KAAK,CAAC,gBAAgB,CAAC,CAAC;MACpD;;MAEA;MACA,MAAM;QAAEC,MAAM;QAAEC;MAAK,CAAC,GAAGP,KAAK,CAACG,QAAQ;MACvC,IAAIK,YAAY,GAAG,MAAM;MAEzB,QAAQF,MAAM;QACZ,KAAK,GAAG;UACNE,YAAY,GAAG,CAAAD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,OAAO,KAAI,QAAQ;UACxC;QACF,KAAK,GAAG;UACND,YAAY,GAAG,OAAO;UACtB;UACAX,YAAY,CAACa,UAAU,CAAC,YAAY,CAAC;UACrC;QACF,KAAK,GAAG;UACNF,YAAY,GAAG,OAAO;UACtB;QACF,KAAK,GAAG;UACNA,YAAY,GAAG,UAAU;UACzB;QACF,KAAK,GAAG;UACNA,YAAY,GAAG,SAAS;UACxB;QACF;UACEA,YAAY,GAAG,CAAAD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,OAAO,KAAI,SAASH,MAAM,GAAG;MACtD;MAEA,OAAOL,OAAO,CAACC,MAAM,CAAC,IAAIG,KAAK,CAACG,YAAY,CAAC,CAAC;IAChD,CACF,CAAC;EACH;;EAEA;EACA,MAAMG,UAAUA,CAAChB,MAAkB,EAAmC;IACpE,IAAI;MACF,MAAMQ,QAAQ,GAAG,MAAM,IAAI,CAAChB,MAAM,CAACyB,IAAI,CAAC,QAAQ,EAAEjB,MAAM,CAAC;MACzD,OAAO;QACLkB,OAAO,EAAE,IAAI;QACbN,IAAI,EAAEJ,QAAQ,CAACI,IAAI;QACnBE,OAAO,EAAE;MACX,CAAC;IACH,CAAC,CAAC,OAAOT,KAAK,EAAE;MACd,OAAO;QACLa,OAAO,EAAE,KAAK;QACdb,KAAK,EAAEA,KAAK,YAAYK,KAAK,GAAGL,KAAK,CAACS,OAAO,GAAG;MAClD,CAAC;IACH;EACF;;EAEA;EACA,MAAMK,QAAQA,CAACC,MAAc,EAAmC;IAC9D,IAAI;MACF,MAAMZ,QAAQ,GAAG,MAAM,IAAI,CAAChB,MAAM,CAACyB,IAAI,CAAC,UAAUG,MAAM,OAAO,CAAC;MAChE,OAAO;QACLF,OAAO,EAAE,IAAI;QACbN,IAAI,EAAEJ,QAAQ,CAACI,IAAI;QACnBE,OAAO,EAAE;MACX,CAAC;IACH,CAAC,CAAC,OAAOT,KAAK,EAAE;MACd,OAAO;QACLa,OAAO,EAAE,KAAK;QACdb,KAAK,EAAEA,KAAK,YAAYK,KAAK,GAAGL,KAAK,CAACS,OAAO,GAAG;MAClD,CAAC;IACH;EACF;;EAEA;EACA,MAAMO,YAAYA,CAACD,MAAc,EAAmC;IAClE,IAAI;MACF,MAAMZ,QAAQ,GAAG,MAAM,IAAI,CAAChB,MAAM,CAAC8B,GAAG,CAAC,UAAUF,MAAM,EAAE,CAAC;MAC1D,OAAO;QACLF,OAAO,EAAE,IAAI;QACbN,IAAI,EAAEJ,QAAQ,CAACI;MACjB,CAAC;IACH,CAAC,CAAC,OAAOP,KAAK,EAAE;MACd,OAAO;QACLa,OAAO,EAAE,KAAK;QACdb,KAAK,EAAEA,KAAK,YAAYK,KAAK,GAAGL,KAAK,CAACS,OAAO,GAAG;MAClD,CAAC;IACH;EACF;;EAEA;EACA,MAAMS,SAASA,CAACH,MAAc,EAAmC;IAC/D,IAAI;MACF,MAAMZ,QAAQ,GAAG,MAAM,IAAI,CAAChB,MAAM,CAACyB,IAAI,CAAC,UAAUG,MAAM,QAAQ,CAAC;MACjE,OAAO;QACLF,OAAO,EAAE,IAAI;QACbN,IAAI,EAAEJ,QAAQ,CAACI,IAAI;QACnBE,OAAO,EAAE;MACX,CAAC;IACH,CAAC,CAAC,OAAOT,KAAK,EAAE;MACd,OAAO;QACLa,OAAO,EAAE,KAAK;QACdb,KAAK,EAAEA,KAAK,YAAYK,KAAK,GAAGL,KAAK,CAACS,OAAO,GAAG;MAClD,CAAC;IACH;EACF;;EAEA;EACA,MAAMU,UAAUA,CAACJ,MAAc,EAAEK,IAAU,EAA6B;IACtE,IAAI;MACF,MAAMjB,QAAQ,GAAG,MAAM,IAAI,CAAChB,MAAM,CAACyB,IAAI,CAAC,UAAUG,MAAM,OAAO,EAAEK,IAAI,CAAC;MACtE,OAAO;QACLP,OAAO,EAAE,IAAI;QACbN,IAAI,EAAEJ,QAAQ,CAACI,IAAI;QACnBE,OAAO,EAAE;MACX,CAAC;IACH,CAAC,CAAC,OAAOT,KAAK,EAAE;MACd,OAAO;QACLa,OAAO,EAAE,KAAK;QACdb,KAAK,EAAEA,KAAK,YAAYK,KAAK,GAAGL,KAAK,CAACS,OAAO,GAAG;MAClD,CAAC;IACH;EACF;;EAEA;EACA,MAAMY,YAAYA,CAACN,MAAc,EAAEO,MAAc,EAA6B;IAC5E,IAAI;MACF,MAAMnB,QAAQ,GAAG,MAAM,IAAI,CAAChB,MAAM,CAACyB,IAAI,CAAC,UAAUG,MAAM,SAAS,EAAEO,MAAM,CAAC;MAC1E,OAAO;QACLT,OAAO,EAAE,IAAI;QACbN,IAAI,EAAEJ,QAAQ,CAACI,IAAI;QACnBE,OAAO,EAAE;MACX,CAAC;IACH,CAAC,CAAC,OAAOT,KAAK,EAAE;MACd,OAAO;QACLa,OAAO,EAAE,KAAK;QACdb,KAAK,EAAEA,KAAK,YAAYK,KAAK,GAAGL,KAAK,CAACS,OAAO,GAAG;MAClD,CAAC;IACH;EACF;;EAEA;EACA,MAAMc,eAAeA,CAACR,MAAc,EAAEN,OAAe,EAA6B;IAChF,IAAI;MACF,MAAMN,QAAQ,GAAG,MAAM,IAAI,CAAChB,MAAM,CAACyB,IAAI,CAAC,UAAUG,MAAM,OAAO,EAAE;QAAEN;MAAQ,CAAC,CAAC;MAC7E,OAAO;QACLI,OAAO,EAAE,IAAI;QACbN,IAAI,EAAEJ,QAAQ,CAACI,IAAI;QACnBE,OAAO,EAAE;MACX,CAAC;IACH,CAAC,CAAC,OAAOT,KAAK,EAAE;MACd,OAAO;QACLa,OAAO,EAAE,KAAK;QACdb,KAAK,EAAEA,KAAK,YAAYK,KAAK,GAAGL,KAAK,CAACS,OAAO,GAAG;MAClD,CAAC;IACH;EACF;;EAEA;EACA,MAAMe,cAAcA,CAACT,MAAc,EAA6B;IAC9D,IAAI;MACF,MAAMZ,QAAQ,GAAG,MAAM,IAAI,CAAChB,MAAM,CAAC8B,GAAG,CAAC,UAAUF,MAAM,UAAU,CAAC;MAClE,OAAO;QACLF,OAAO,EAAE,IAAI;QACbN,IAAI,EAAEJ,QAAQ,CAACI;MACjB,CAAC;IACH,CAAC,CAAC,OAAOP,KAAK,EAAE;MACd,OAAO;QACLa,OAAO,EAAE,KAAK;QACdb,KAAK,EAAEA,KAAK,YAAYK,KAAK,GAAGL,KAAK,CAACS,OAAO,GAAG;MAClD,CAAC;IACH;EACF;;EAEA;EACA,MAAMgB,WAAWA,CAAA,EAA8B;IAC7C,IAAI;MACF,MAAMtB,QAAQ,GAAG,MAAM,IAAI,CAAChB,MAAM,CAAC8B,GAAG,CAAC,SAAS,CAAC;MACjD,OAAO;QACLJ,OAAO,EAAE,IAAI;QACbN,IAAI,EAAEJ,QAAQ,CAACI;MACjB,CAAC;IACH,CAAC,CAAC,OAAOP,KAAK,EAAE;MACd,OAAO;QACLa,OAAO,EAAE,KAAK;QACdb,KAAK,EAAEA,KAAK,YAAYK,KAAK,GAAGL,KAAK,CAACS,OAAO,GAAG;MAClD,CAAC;IACH;EACF;AACF;;AAEA;AACA,OAAO,MAAMiB,OAAO,GAAG,IAAIzC,OAAO,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}