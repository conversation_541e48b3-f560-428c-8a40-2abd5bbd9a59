{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState, useEffect, useCallback, useRef } from 'react';\nimport { io } from 'socket.io-client';\nconst WEBSOCKET_URL = process.env.REACT_APP_WEBSOCKET_URL || 'http://localhost:8000';\nexport const useWebSocket = () => {\n  _s();\n  const [connected, setConnected] = useState(false);\n  const [lastMessage, setLastMessage] = useState(null);\n  const [connectionError, setConnectionError] = useState(null);\n  const socketRef = useRef(null);\n\n  // 发送消息\n  const sendMessage = useCallback(message => {\n    if (socketRef.current && connected) {\n      socketRef.current.emit('message', message);\n    } else {\n      console.warn('WebSocket not connected, message not sent:', message);\n    }\n  }, [connected]);\n\n  // 初始化WebSocket连接\n  useEffect(() => {\n    // 创建socket连接\n    const socket = io(WEBSOCKET_URL, {\n      transports: ['websocket', 'polling'],\n      timeout: 5000,\n      reconnection: true,\n      reconnectionAttempts: 5,\n      reconnectionDelay: 1000\n    });\n    socketRef.current = socket;\n\n    // 连接成功\n    socket.on('connect', () => {\n      console.log('WebSocket connected');\n      setConnected(true);\n      setConnectionError(null);\n    });\n\n    // 连接断开\n    socket.on('disconnect', reason => {\n      console.log('WebSocket disconnected:', reason);\n      setConnected(false);\n    });\n\n    // 连接错误\n    socket.on('connect_error', error => {\n      console.error('WebSocket connection error:', error);\n      setConnectionError(error.message);\n      setConnected(false);\n    });\n\n    // 接收消息\n    socket.on('message', data => {\n      const message = {\n        type: data.type || 'unknown',\n        data: data.data || data,\n        timestamp: new Date().toISOString()\n      };\n      setLastMessage(message);\n    });\n\n    // 游戏状态更新\n    socket.on('game_state_update', gameState => {\n      const message = {\n        type: 'game_state_update',\n        data: gameState,\n        timestamp: new Date().toISOString()\n      };\n      setLastMessage(message);\n    });\n\n    // 聊天消息\n    socket.on('chat_message', chatData => {\n      const message = {\n        type: 'chat_message',\n        data: chatData,\n        timestamp: new Date().toISOString()\n      };\n      setLastMessage(message);\n    });\n\n    // 投票更新\n    socket.on('vote_update', voteData => {\n      const message = {\n        type: 'vote_update',\n        data: voteData,\n        timestamp: new Date().toISOString()\n      };\n      setLastMessage(message);\n    });\n\n    // 行动更新\n    socket.on('action_update', actionData => {\n      const message = {\n        type: 'action_update',\n        data: actionData,\n        timestamp: new Date().toISOString()\n      };\n      setLastMessage(message);\n    });\n\n    // 阶段变化\n    socket.on('phase_change', phaseData => {\n      const message = {\n        type: 'phase_change',\n        data: phaseData,\n        timestamp: new Date().toISOString()\n      };\n      setLastMessage(message);\n    });\n\n    // 游戏结束\n    socket.on('game_over', resultData => {\n      const message = {\n        type: 'game_over',\n        data: resultData,\n        timestamp: new Date().toISOString()\n      };\n      setLastMessage(message);\n    });\n\n    // 清理函数\n    return () => {\n      socket.disconnect();\n      socketRef.current = null;\n    };\n  }, []);\n\n  // 重连逻辑\n  useEffect(() => {\n    if (!connected && socketRef.current) {\n      const reconnectTimer = setTimeout(() => {\n        if (socketRef.current && !socketRef.current.connected) {\n          console.log('Attempting to reconnect...');\n          socketRef.current.connect();\n        }\n      }, 3000);\n      return () => clearTimeout(reconnectTimer);\n    }\n  }, [connected]);\n  return {\n    connected,\n    sendMessage,\n    lastMessage,\n    connectionError\n  };\n};\n_s(useWebSocket, \"VazK8+kxF7n6pZuErya0/VDOImk=\");", "map": {"version": 3, "names": ["useState", "useEffect", "useCallback", "useRef", "io", "WEBSOCKET_URL", "process", "env", "REACT_APP_WEBSOCKET_URL", "useWebSocket", "_s", "connected", "setConnected", "lastMessage", "setLastMessage", "connectionError", "setConnectionError", "socketRef", "sendMessage", "message", "current", "emit", "console", "warn", "socket", "transports", "timeout", "reconnection", "reconnectionAttempts", "reconnectionDelay", "on", "log", "reason", "error", "data", "type", "timestamp", "Date", "toISOString", "gameState", "chatData", "voteData", "actionData", "phaseData", "resultData", "disconnect", "reconnectTimer", "setTimeout", "connect", "clearTimeout"], "sources": ["/private/peiwy/work/ai_projects/wolfkill/frontend/src/hooks/useWebSocket.ts"], "sourcesContent": ["import { useState, useEffect, useCallback, useRef } from 'react';\nimport { io, Socket } from 'socket.io-client';\nimport { WebSocketMessage } from '../types';\n\ninterface UseWebSocketReturn {\n  connected: boolean;\n  sendMessage: (message: any) => void;\n  lastMessage: WebSocketMessage | null;\n  connectionError: string | null;\n}\n\nconst WEBSOCKET_URL = process.env.REACT_APP_WEBSOCKET_URL || 'http://localhost:8000';\n\nexport const useWebSocket = (): UseWebSocketReturn => {\n  const [connected, setConnected] = useState<boolean>(false);\n  const [lastMessage, setLastMessage] = useState<WebSocketMessage | null>(null);\n  const [connectionError, setConnectionError] = useState<string | null>(null);\n  const socketRef = useRef<Socket | null>(null);\n\n  // 发送消息\n  const sendMessage = useCallback((message: any) => {\n    if (socketRef.current && connected) {\n      socketRef.current.emit('message', message);\n    } else {\n      console.warn('WebSocket not connected, message not sent:', message);\n    }\n  }, [connected]);\n\n  // 初始化WebSocket连接\n  useEffect(() => {\n    // 创建socket连接\n    const socket = io(WEBSOCKET_URL, {\n      transports: ['websocket', 'polling'],\n      timeout: 5000,\n      reconnection: true,\n      reconnectionAttempts: 5,\n      reconnectionDelay: 1000\n    });\n\n    socketRef.current = socket;\n\n    // 连接成功\n    socket.on('connect', () => {\n      console.log('WebSocket connected');\n      setConnected(true);\n      setConnectionError(null);\n    });\n\n    // 连接断开\n    socket.on('disconnect', (reason) => {\n      console.log('WebSocket disconnected:', reason);\n      setConnected(false);\n    });\n\n    // 连接错误\n    socket.on('connect_error', (error) => {\n      console.error('WebSocket connection error:', error);\n      setConnectionError(error.message);\n      setConnected(false);\n    });\n\n    // 接收消息\n    socket.on('message', (data: any) => {\n      const message: WebSocketMessage = {\n        type: data.type || 'unknown',\n        data: data.data || data,\n        timestamp: new Date().toISOString()\n      };\n      setLastMessage(message);\n    });\n\n    // 游戏状态更新\n    socket.on('game_state_update', (gameState: any) => {\n      const message: WebSocketMessage = {\n        type: 'game_state_update',\n        data: gameState,\n        timestamp: new Date().toISOString()\n      };\n      setLastMessage(message);\n    });\n\n    // 聊天消息\n    socket.on('chat_message', (chatData: any) => {\n      const message: WebSocketMessage = {\n        type: 'chat_message',\n        data: chatData,\n        timestamp: new Date().toISOString()\n      };\n      setLastMessage(message);\n    });\n\n    // 投票更新\n    socket.on('vote_update', (voteData: any) => {\n      const message: WebSocketMessage = {\n        type: 'vote_update',\n        data: voteData,\n        timestamp: new Date().toISOString()\n      };\n      setLastMessage(message);\n    });\n\n    // 行动更新\n    socket.on('action_update', (actionData: any) => {\n      const message: WebSocketMessage = {\n        type: 'action_update',\n        data: actionData,\n        timestamp: new Date().toISOString()\n      };\n      setLastMessage(message);\n    });\n\n    // 阶段变化\n    socket.on('phase_change', (phaseData: any) => {\n      const message: WebSocketMessage = {\n        type: 'phase_change',\n        data: phaseData,\n        timestamp: new Date().toISOString()\n      };\n      setLastMessage(message);\n    });\n\n    // 游戏结束\n    socket.on('game_over', (resultData: any) => {\n      const message: WebSocketMessage = {\n        type: 'game_over',\n        data: resultData,\n        timestamp: new Date().toISOString()\n      };\n      setLastMessage(message);\n    });\n\n    // 清理函数\n    return () => {\n      socket.disconnect();\n      socketRef.current = null;\n    };\n  }, []);\n\n  // 重连逻辑\n  useEffect(() => {\n    if (!connected && socketRef.current) {\n      const reconnectTimer = setTimeout(() => {\n        if (socketRef.current && !socketRef.current.connected) {\n          console.log('Attempting to reconnect...');\n          socketRef.current.connect();\n        }\n      }, 3000);\n\n      return () => clearTimeout(reconnectTimer);\n    }\n  }, [connected]);\n\n  return {\n    connected,\n    sendMessage,\n    lastMessage,\n    connectionError\n  };\n};"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,MAAM,QAAQ,OAAO;AAChE,SAASC,EAAE,QAAgB,kBAAkB;AAU7C,MAAMC,aAAa,GAAGC,OAAO,CAACC,GAAG,CAACC,uBAAuB,IAAI,uBAAuB;AAEpF,OAAO,MAAMC,YAAY,GAAGA,CAAA,KAA0B;EAAAC,EAAA;EACpD,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGZ,QAAQ,CAAU,KAAK,CAAC;EAC1D,MAAM,CAACa,WAAW,EAAEC,cAAc,CAAC,GAAGd,QAAQ,CAA0B,IAAI,CAAC;EAC7E,MAAM,CAACe,eAAe,EAAEC,kBAAkB,CAAC,GAAGhB,QAAQ,CAAgB,IAAI,CAAC;EAC3E,MAAMiB,SAAS,GAAGd,MAAM,CAAgB,IAAI,CAAC;;EAE7C;EACA,MAAMe,WAAW,GAAGhB,WAAW,CAAEiB,OAAY,IAAK;IAChD,IAAIF,SAAS,CAACG,OAAO,IAAIT,SAAS,EAAE;MAClCM,SAAS,CAACG,OAAO,CAACC,IAAI,CAAC,SAAS,EAAEF,OAAO,CAAC;IAC5C,CAAC,MAAM;MACLG,OAAO,CAACC,IAAI,CAAC,4CAA4C,EAAEJ,OAAO,CAAC;IACrE;EACF,CAAC,EAAE,CAACR,SAAS,CAAC,CAAC;;EAEf;EACAV,SAAS,CAAC,MAAM;IACd;IACA,MAAMuB,MAAM,GAAGpB,EAAE,CAACC,aAAa,EAAE;MAC/BoB,UAAU,EAAE,CAAC,WAAW,EAAE,SAAS,CAAC;MACpCC,OAAO,EAAE,IAAI;MACbC,YAAY,EAAE,IAAI;MAClBC,oBAAoB,EAAE,CAAC;MACvBC,iBAAiB,EAAE;IACrB,CAAC,CAAC;IAEFZ,SAAS,CAACG,OAAO,GAAGI,MAAM;;IAE1B;IACAA,MAAM,CAACM,EAAE,CAAC,SAAS,EAAE,MAAM;MACzBR,OAAO,CAACS,GAAG,CAAC,qBAAqB,CAAC;MAClCnB,YAAY,CAAC,IAAI,CAAC;MAClBI,kBAAkB,CAAC,IAAI,CAAC;IAC1B,CAAC,CAAC;;IAEF;IACAQ,MAAM,CAACM,EAAE,CAAC,YAAY,EAAGE,MAAM,IAAK;MAClCV,OAAO,CAACS,GAAG,CAAC,yBAAyB,EAAEC,MAAM,CAAC;MAC9CpB,YAAY,CAAC,KAAK,CAAC;IACrB,CAAC,CAAC;;IAEF;IACAY,MAAM,CAACM,EAAE,CAAC,eAAe,EAAGG,KAAK,IAAK;MACpCX,OAAO,CAACW,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnDjB,kBAAkB,CAACiB,KAAK,CAACd,OAAO,CAAC;MACjCP,YAAY,CAAC,KAAK,CAAC;IACrB,CAAC,CAAC;;IAEF;IACAY,MAAM,CAACM,EAAE,CAAC,SAAS,EAAGI,IAAS,IAAK;MAClC,MAAMf,OAAyB,GAAG;QAChCgB,IAAI,EAAED,IAAI,CAACC,IAAI,IAAI,SAAS;QAC5BD,IAAI,EAAEA,IAAI,CAACA,IAAI,IAAIA,IAAI;QACvBE,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC;MACDxB,cAAc,CAACK,OAAO,CAAC;IACzB,CAAC,CAAC;;IAEF;IACAK,MAAM,CAACM,EAAE,CAAC,mBAAmB,EAAGS,SAAc,IAAK;MACjD,MAAMpB,OAAyB,GAAG;QAChCgB,IAAI,EAAE,mBAAmB;QACzBD,IAAI,EAAEK,SAAS;QACfH,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC;MACDxB,cAAc,CAACK,OAAO,CAAC;IACzB,CAAC,CAAC;;IAEF;IACAK,MAAM,CAACM,EAAE,CAAC,cAAc,EAAGU,QAAa,IAAK;MAC3C,MAAMrB,OAAyB,GAAG;QAChCgB,IAAI,EAAE,cAAc;QACpBD,IAAI,EAAEM,QAAQ;QACdJ,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC;MACDxB,cAAc,CAACK,OAAO,CAAC;IACzB,CAAC,CAAC;;IAEF;IACAK,MAAM,CAACM,EAAE,CAAC,aAAa,EAAGW,QAAa,IAAK;MAC1C,MAAMtB,OAAyB,GAAG;QAChCgB,IAAI,EAAE,aAAa;QACnBD,IAAI,EAAEO,QAAQ;QACdL,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC;MACDxB,cAAc,CAACK,OAAO,CAAC;IACzB,CAAC,CAAC;;IAEF;IACAK,MAAM,CAACM,EAAE,CAAC,eAAe,EAAGY,UAAe,IAAK;MAC9C,MAAMvB,OAAyB,GAAG;QAChCgB,IAAI,EAAE,eAAe;QACrBD,IAAI,EAAEQ,UAAU;QAChBN,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC;MACDxB,cAAc,CAACK,OAAO,CAAC;IACzB,CAAC,CAAC;;IAEF;IACAK,MAAM,CAACM,EAAE,CAAC,cAAc,EAAGa,SAAc,IAAK;MAC5C,MAAMxB,OAAyB,GAAG;QAChCgB,IAAI,EAAE,cAAc;QACpBD,IAAI,EAAES,SAAS;QACfP,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC;MACDxB,cAAc,CAACK,OAAO,CAAC;IACzB,CAAC,CAAC;;IAEF;IACAK,MAAM,CAACM,EAAE,CAAC,WAAW,EAAGc,UAAe,IAAK;MAC1C,MAAMzB,OAAyB,GAAG;QAChCgB,IAAI,EAAE,WAAW;QACjBD,IAAI,EAAEU,UAAU;QAChBR,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC;MACDxB,cAAc,CAACK,OAAO,CAAC;IACzB,CAAC,CAAC;;IAEF;IACA,OAAO,MAAM;MACXK,MAAM,CAACqB,UAAU,CAAC,CAAC;MACnB5B,SAAS,CAACG,OAAO,GAAG,IAAI;IAC1B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAnB,SAAS,CAAC,MAAM;IACd,IAAI,CAACU,SAAS,IAAIM,SAAS,CAACG,OAAO,EAAE;MACnC,MAAM0B,cAAc,GAAGC,UAAU,CAAC,MAAM;QACtC,IAAI9B,SAAS,CAACG,OAAO,IAAI,CAACH,SAAS,CAACG,OAAO,CAACT,SAAS,EAAE;UACrDW,OAAO,CAACS,GAAG,CAAC,4BAA4B,CAAC;UACzCd,SAAS,CAACG,OAAO,CAAC4B,OAAO,CAAC,CAAC;QAC7B;MACF,CAAC,EAAE,IAAI,CAAC;MAER,OAAO,MAAMC,YAAY,CAACH,cAAc,CAAC;IAC3C;EACF,CAAC,EAAE,CAACnC,SAAS,CAAC,CAAC;EAEf,OAAO;IACLA,SAAS;IACTO,WAAW;IACXL,WAAW;IACXE;EACF,CAAC;AACH,CAAC;AAACL,EAAA,CAjJWD,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}