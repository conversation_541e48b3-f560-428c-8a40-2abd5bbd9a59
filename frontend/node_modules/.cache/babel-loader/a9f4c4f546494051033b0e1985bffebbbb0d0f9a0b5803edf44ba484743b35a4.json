{"ast": null, "code": "var _jsxFileName = \"/private/peiwy/work/ai_projects/wolfkill/frontend/src/components/GameBoard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\nimport { GamePhase, GameResult, VoteType, Role } from '../types';\nimport { getPhaseColor } from '../styles/theme';\nimport VoteDialog from './dialogs/VoteDialog';\nimport ActionDialog from './dialogs/ActionDialog';\nimport { useGameContext } from '../contexts/GameContext';\nimport { ScaleIn, Pulse } from './animations/AnimatedComponents';\nimport { VoteAnimation, SkillAnimation, PhaseTransition } from './animations/GameAnimations';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BoardContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  gap: ${props => props.theme.spacing.lg};\n`;\n_c = BoardContainer;\nconst StatusSection = styled.section`\n  background: ${props => props.theme.colors.background};\n  border-radius: ${props => props.theme.borderRadius.lg};\n  padding: ${props => props.theme.spacing.lg};\n  border: 1px solid ${props => props.theme.colors.border};\n`;\n_c2 = StatusSection;\nconst StatusTitle = styled.h2`\n  font-size: ${props => props.theme.fontSizes.xl};\n  font-weight: ${props => props.theme.fontWeights.semibold};\n  color: ${props => props.theme.colors.text};\n  margin-bottom: ${props => props.theme.spacing.md};\n`;\n_c3 = StatusTitle;\nconst StatsGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n  gap: ${props => props.theme.spacing.md};\n  margin-bottom: ${props => props.theme.spacing.lg};\n`;\n_c4 = StatsGrid;\nconst StatCard = styled.div`\n  background: ${props => props.theme.colors.backgroundLight};\n  border-radius: ${props => props.theme.borderRadius.md};\n  padding: ${props => props.theme.spacing.md};\n  text-align: center;\n  border: 1px solid ${props => props.theme.colors.borderLight};\n`;\n_c5 = StatCard;\nconst StatValue = styled.div`\n  font-size: ${props => props.theme.fontSizes['2xl']};\n  font-weight: ${props => props.theme.fontWeights.bold};\n  color: ${props => props.theme.colors.primary};\n  margin-bottom: ${props => props.theme.spacing.xs};\n`;\n_c6 = StatValue;\nconst StatLabel = styled.div`\n  font-size: ${props => props.theme.fontSizes.sm};\n  color: ${props => props.theme.colors.textLight};\n`;\n_c7 = StatLabel;\nconst PhaseSection = styled.section`\n  background: ${props => props.theme.colors.background};\n  border-radius: ${props => props.theme.borderRadius.lg};\n  padding: ${props => props.theme.spacing.lg};\n  border: 1px solid ${props => props.theme.colors.border};\n  text-align: center;\n`;\n_c8 = PhaseSection;\nconst PhaseTitle = styled.h3`\n  font-size: ${props => props.theme.fontSizes['2xl']};\n  font-weight: ${props => props.theme.fontWeights.bold};\n  color: ${props => props.phase ? getPhaseColor(props.phase) : props.theme.colors.text};\n  margin-bottom: ${props => props.theme.spacing.sm};\n`;\n_c9 = PhaseTitle;\nconst PhaseDescription = styled.p`\n  font-size: ${props => props.theme.fontSizes.md};\n  color: ${props => props.theme.colors.textLight};\n  line-height: ${props => props.theme.lineHeights.relaxed};\n  margin-bottom: ${props => props.theme.spacing.lg};\n`;\n_c0 = PhaseDescription;\nconst ActionSection = styled.section`\n  background: ${props => props.theme.colors.background};\n  border-radius: ${props => props.theme.borderRadius.lg};\n  padding: ${props => props.theme.spacing.lg};\n  border: 1px solid ${props => props.theme.colors.border};\n  flex: 1;\n`;\n_c1 = ActionSection;\nconst ActionTitle = styled.h3`\n  font-size: ${props => props.theme.fontSizes.lg};\n  font-weight: ${props => props.theme.fontWeights.semibold};\n  color: ${props => props.theme.colors.text};\n  margin-bottom: ${props => props.theme.spacing.md};\n`;\n_c10 = ActionTitle;\nconst ButtonGroup = styled.div`\n  display: flex;\n  gap: ${props => props.theme.spacing.md};\n  margin-bottom: ${props => props.theme.spacing.lg};\n  flex-wrap: wrap;\n`;\n_c11 = ButtonGroup;\nconst ActionButton = styled.button`\n  padding: ${props => props.theme.spacing.md} ${props => props.theme.spacing.lg};\n  border-radius: ${props => props.theme.borderRadius.md};\n  font-size: ${props => props.theme.fontSizes.md};\n  font-weight: ${props => props.theme.fontWeights.medium};\n  transition: ${props => props.theme.transitions.fast};\n  min-width: 120px;\n\n  ${props => {\n  switch (props.variant) {\n    case 'secondary':\n      return `\n          background: ${props.theme.colors.secondary};\n          color: ${props.theme.colors.textWhite};\n          &:hover:not(:disabled) { background: ${props.theme.colors.secondaryLight}; }\n        `;\n    case 'danger':\n      return `\n          background: ${props.theme.colors.danger};\n          color: ${props.theme.colors.textWhite};\n          &:hover:not(:disabled) { background: ${props.theme.colors.dangerLight}; }\n        `;\n    case 'warning':\n      return `\n          background: ${props.theme.colors.warning};\n          color: ${props.theme.colors.textWhite};\n          &:hover:not(:disabled) { background: ${props.theme.colors.warningLight}; }\n        `;\n    default:\n      return `\n          background: ${props.theme.colors.primary};\n          color: ${props.theme.colors.textWhite};\n          &:hover:not(:disabled) { background: ${props.theme.colors.primaryLight}; }\n        `;\n  }\n}}\n\n  &:disabled {\n    background: ${props => props.theme.colors.backgroundDark};\n    color: ${props => props.theme.colors.textLight};\n    cursor: not-allowed;\n  }\n`;\n_c12 = ActionButton;\nconst HintArea = styled.div`\n  background: ${props => props.theme.colors.backgroundLight};\n  border-radius: ${props => props.theme.borderRadius.md};\n  padding: ${props => props.theme.spacing.md};\n  border-left: 4px solid ${props => props.theme.colors.info};\n  font-size: ${props => props.theme.fontSizes.sm};\n  color: ${props => props.theme.colors.textLight};\n  line-height: ${props => props.theme.lineHeights.relaxed};\n`;\n_c13 = HintArea;\nconst EmptyState = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 300px;\n  color: ${props => props.theme.colors.textLight};\n  text-align: center;\n`;\n_c14 = EmptyState;\nconst EmptyIcon = styled.div`\n  font-size: 4rem;\n  margin-bottom: ${props => props.theme.spacing.md};\n`;\n_c15 = EmptyIcon;\nconst EmptyText = styled.p`\n  font-size: ${props => props.theme.fontSizes.lg};\n  margin-bottom: ${props => props.theme.spacing.sm};\n`;\n_c16 = EmptyText;\nconst EmptySubtext = styled.p`\n  font-size: ${props => props.theme.fontSizes.md};\n  color: ${props => props.theme.colors.textLight};\n`;\n_c17 = EmptySubtext;\nconst getPhaseText = phase => {\n  const phaseTexts = {\n    [GamePhase.SETUP]: '游戏设置',\n    [GamePhase.NIGHT]: '夜晚阶段',\n    [GamePhase.DAY_DISCUSSION]: '白天讨论',\n    [GamePhase.DAY_VOTING]: '白天投票',\n    [GamePhase.GAME_OVER]: '游戏结束'\n  };\n  return phaseTexts[phase] || '未知阶段';\n};\nconst getPhaseDescription = phase => {\n  const descriptions = {\n    [GamePhase.SETUP]: '游戏准备中，等待所有玩家加入...',\n    [GamePhase.NIGHT]: '夜晚降临，狼人开始行动，特殊角色使用技能',\n    [GamePhase.DAY_DISCUSSION]: '白天到来，所有玩家开始发言讨论昨夜发生的事情',\n    [GamePhase.DAY_VOTING]: '投票阶段，选择要淘汰的可疑玩家',\n    [GamePhase.GAME_OVER]: '游戏结束，查看最终结果'\n  };\n  return descriptions[phase] || '';\n};\nconst getActionHints = phase => {\n  const hints = {\n    [GamePhase.SETUP]: '等待游戏开始...',\n    [GamePhase.NIGHT]: '夜晚阶段：狼人选择杀害目标，预言家查验身份，女巫使用药剂，守卫保护玩家',\n    [GamePhase.DAY_DISCUSSION]: '白天讨论：所有玩家发言，分析昨夜情况，讨论可疑对象',\n    [GamePhase.DAY_VOTING]: '投票阶段：选择要淘汰的玩家，点击投票按钮确认，也可以选择弃权',\n    [GamePhase.GAME_OVER]: '游戏已结束，查看最终结果'\n  };\n  return hints[phase] || '';\n};\nconst getResultText = result => {\n  const resultTexts = {\n    [GameResult.VILLAGERS_WIN]: '村民阵营胜利！',\n    [GameResult.WEREWOLVES_WIN]: '狼人阵营胜利！',\n    [GameResult.DRAW]: '平局！',\n    [GameResult.ONGOING]: '游戏进行中'\n  };\n  return resultTexts[result] || '未知结果';\n};\nconst GameBoard = ({\n  gameState,\n  onAction\n}) => {\n  _s();\n  const [selectedAction, setSelectedAction] = useState(null);\n  const [showVoteDialog, setShowVoteDialog] = useState(false);\n  const [showActionDialog, setShowActionDialog] = useState(false);\n  const [isPhaseTransitioning, setIsPhaseTransitioning] = useState(false);\n  const [previousPhase, setPreviousPhase] = useState(null);\n  const {\n    submitVote,\n    submitAction\n  } = useGameContext();\n\n  // 监听阶段变化\n  useEffect(() => {\n    if (gameState && previousPhase && previousPhase !== gameState.current_phase) {\n      setIsPhaseTransitioning(true);\n      const timer = setTimeout(() => {\n        setIsPhaseTransitioning(false);\n      }, 1000);\n      return () => clearTimeout(timer);\n    }\n    if (gameState) {\n      setPreviousPhase(gameState.current_phase);\n    }\n  }, [gameState === null || gameState === void 0 ? void 0 : gameState.current_phase, previousPhase]);\n  if (!gameState) {\n    return /*#__PURE__*/_jsxDEV(BoardContainer, {\n      children: /*#__PURE__*/_jsxDEV(EmptyState, {\n        children: [/*#__PURE__*/_jsxDEV(EmptyIcon, {\n          children: \"\\uD83C\\uDFAE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(EmptyText, {\n          children: \"\\u6682\\u65E0\\u6E38\\u620F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(EmptySubtext, {\n          children: \"\\u70B9\\u51FB\\u53F3\\u4E0A\\u89D2\\\"\\u521B\\u5EFA\\u6E38\\u620F\\\"\\u5F00\\u59CB\\u65B0\\u7684\\u72FC\\u4EBA\\u6740\\u6E38\\u620F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 255,\n      columnNumber: 7\n    }, this);\n  }\n  const totalPlayers = Object.keys(gameState.players).length;\n  const alivePlayers = gameState.alive_players.length;\n  const deadPlayers = gameState.dead_players.length;\n\n  // 计算阵营统计\n  const villagerCount = gameState.alive_players.filter(id => {\n    const player = gameState.players[id];\n    return player && player.faction === 'VILLAGERS';\n  }).length;\n  const werewolfCount = gameState.alive_players.filter(id => {\n    const player = gameState.players[id];\n    return player && player.faction === 'WEREWOLVES';\n  }).length;\n  const handleVote = () => {\n    setShowVoteDialog(true);\n  };\n  const handleSkill = () => {\n    setShowActionDialog(true);\n  };\n  const handleAbstain = () => {\n    submitVote(null, '选择弃权');\n  };\n  const handleVoteConfirm = async (targetId, reason) => {\n    try {\n      await submitVote(targetId, reason);\n      setShowVoteDialog(false);\n    } catch (error) {\n      console.error('Vote failed:', error);\n    }\n  };\n  const handleActionConfirm = async (actionType, targetId) => {\n    try {\n      await submitAction(actionType, targetId);\n      setShowActionDialog(false);\n    } catch (error) {\n      console.error('Action failed:', error);\n    }\n  };\n  const canVote = gameState.current_phase === GamePhase.DAY_VOTING;\n  const canUseSkill = gameState.current_phase === GamePhase.NIGHT;\n  const isGameOver = gameState.current_phase === GamePhase.GAME_OVER;\n  return /*#__PURE__*/_jsxDEV(BoardContainer, {\n    children: [/*#__PURE__*/_jsxDEV(StatusSection, {\n      children: [/*#__PURE__*/_jsxDEV(StatusTitle, {\n        children: \"\\u6E38\\u620F\\u72B6\\u6001\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 318,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatsGrid, {\n        children: [/*#__PURE__*/_jsxDEV(StatCard, {\n          children: [/*#__PURE__*/_jsxDEV(StatValue, {\n            children: totalPlayers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n            children: \"\\u603B\\u73A9\\u5BB6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          children: [/*#__PURE__*/_jsxDEV(StatValue, {\n            children: alivePlayers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n            children: \"\\u5B58\\u6D3B\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          children: [/*#__PURE__*/_jsxDEV(StatValue, {\n            children: deadPlayers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n            children: \"\\u6B7B\\u4EA1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          children: [/*#__PURE__*/_jsxDEV(StatValue, {\n            children: villagerCount\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n            children: \"\\u6751\\u6C11\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          children: [/*#__PURE__*/_jsxDEV(StatValue, {\n            children: werewolfCount\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n            children: \"\\u72FC\\u4EBA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 319,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 317,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PhaseSection, {\n      children: /*#__PURE__*/_jsxDEV(PhaseTransition, {\n        isTransitioning: isPhaseTransitioning,\n        children: [/*#__PURE__*/_jsxDEV(Pulse, {\n          isActive: !isGameOver,\n          children: /*#__PURE__*/_jsxDEV(PhaseTitle, {\n            phase: gameState.current_phase,\n            children: getPhaseText(gameState.current_phase)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(PhaseDescription, {\n          children: getPhaseDescription(gameState.current_phase)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 11\n        }, this), isGameOver && /*#__PURE__*/_jsxDEV(ScaleIn, {\n          children: /*#__PURE__*/_jsxDEV(PhaseTitle, {\n            phase: gameState.current_phase,\n            children: getResultText(gameState.game_result)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 345,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 344,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ActionSection, {\n      children: [/*#__PURE__*/_jsxDEV(ActionTitle, {\n        children: \"\\u53EF\\u7528\\u64CD\\u4F5C\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 367,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ButtonGroup, {\n        children: [/*#__PURE__*/_jsxDEV(VoteAnimation, {\n          isVoting: showVoteDialog,\n          children: /*#__PURE__*/_jsxDEV(ActionButton, {\n            onClick: handleVote,\n            disabled: !canVote,\n            variant: \"primary\",\n            children: \"\\u6295\\u7968\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SkillAnimation, {\n          isActive: showActionDialog,\n          skillColor: \"#E67E22\",\n          children: /*#__PURE__*/_jsxDEV(ActionButton, {\n            onClick: handleSkill,\n            disabled: !canUseSkill,\n            variant: \"secondary\",\n            children: \"\\u4F7F\\u7528\\u6280\\u80FD\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n          onClick: handleAbstain,\n          disabled: !canVote,\n          variant: \"warning\",\n          children: \"\\u5F03\\u6743\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 390,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 369,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(HintArea, {\n        children: getActionHints(gameState.current_phase)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 399,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 366,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(VoteDialog, {\n      isOpen: showVoteDialog,\n      players: gameState.players,\n      voteType: VoteType.ELIMINATION,\n      onClose: () => setShowVoteDialog(false),\n      onConfirm: handleVoteConfirm\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 405,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ActionDialog, {\n      isOpen: showActionDialog,\n      playerRole: Role.SEER // TODO: 获取当前玩家角色\n      ,\n      players: gameState.players,\n      availableActions: ['seer_check'] // TODO: 根据角色获取可用行动\n      ,\n      onClose: () => setShowActionDialog(false),\n      onConfirm: handleActionConfirm\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 414,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 315,\n    columnNumber: 5\n  }, this);\n};\n_s(GameBoard, \"+ltj+FJ65apIPvOlZRY+00Ye2RY=\", false, function () {\n  return [useGameContext];\n});\n_c18 = GameBoard;\nexport default GameBoard;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18;\n$RefreshReg$(_c, \"BoardContainer\");\n$RefreshReg$(_c2, \"StatusSection\");\n$RefreshReg$(_c3, \"StatusTitle\");\n$RefreshReg$(_c4, \"StatsGrid\");\n$RefreshReg$(_c5, \"StatCard\");\n$RefreshReg$(_c6, \"StatValue\");\n$RefreshReg$(_c7, \"StatLabel\");\n$RefreshReg$(_c8, \"PhaseSection\");\n$RefreshReg$(_c9, \"PhaseTitle\");\n$RefreshReg$(_c0, \"PhaseDescription\");\n$RefreshReg$(_c1, \"ActionSection\");\n$RefreshReg$(_c10, \"ActionTitle\");\n$RefreshReg$(_c11, \"ButtonGroup\");\n$RefreshReg$(_c12, \"ActionButton\");\n$RefreshReg$(_c13, \"HintArea\");\n$RefreshReg$(_c14, \"EmptyState\");\n$RefreshReg$(_c15, \"EmptyIcon\");\n$RefreshReg$(_c16, \"EmptyText\");\n$RefreshReg$(_c17, \"EmptySubtext\");\n$RefreshReg$(_c18, \"GameBoard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "styled", "GamePhase", "GameResult", "VoteType", "Role", "getPhaseColor", "VoteDialog", "ActionDialog", "useGameContext", "ScaleIn", "Pulse", "VoteAnimation", "SkillAnimation", "PhaseTransition", "jsxDEV", "_jsxDEV", "BoardContainer", "div", "props", "theme", "spacing", "lg", "_c", "StatusSection", "section", "colors", "background", "borderRadius", "border", "_c2", "StatusTitle", "h2", "fontSizes", "xl", "fontWeights", "semibold", "text", "md", "_c3", "StatsGrid", "_c4", "StatCard", "backgroundLight", "borderLight", "_c5", "StatValue", "bold", "primary", "xs", "_c6", "StatLabel", "sm", "textLight", "_c7", "PhaseSection", "_c8", "PhaseTitle", "h3", "phase", "_c9", "PhaseDescription", "p", "lineHeights", "relaxed", "_c0", "ActionSection", "_c1", "ActionTitle", "_c10", "ButtonGroup", "_c11", "ActionButton", "button", "medium", "transitions", "fast", "variant", "secondary", "textWhite", "secondaryLight", "danger", "dangerLight", "warning", "warningLight", "primaryLight", "backgroundDark", "_c12", "<PERSON><PERSON><PERSON><PERSON>", "info", "_c13", "EmptyState", "_c14", "EmptyIcon", "_c15", "EmptyText", "_c16", "EmptySubtext", "_c17", "getPhaseText", "phaseTexts", "SETUP", "NIGHT", "DAY_DISCUSSION", "DAY_VOTING", "GAME_OVER", "getPhaseDescription", "descriptions", "getActionHints", "hints", "getResultText", "result", "resultTexts", "VILLAGERS_WIN", "WEREWOLVES_WIN", "DRAW", "ONGOING", "GameBoard", "gameState", "onAction", "_s", "selectedAction", "setSelectedAction", "showVoteDialog", "setShowVoteDialog", "showActionDialog", "setShowActionDialog", "isPhaseTransitioning", "setIsPhaseTransitioning", "previousPhase", "setPreviousPhase", "submitVote", "submitAction", "current_phase", "timer", "setTimeout", "clearTimeout", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "totalPlayers", "Object", "keys", "players", "length", "alivePlayers", "alive_players", "deadPlayers", "dead_players", "villager<PERSON><PERSON>nt", "filter", "id", "player", "faction", "werewolfCount", "handleVote", "handleSkill", "handleAbstain", "handleVoteConfirm", "targetId", "reason", "error", "console", "handleActionConfirm", "actionType", "canVote", "canUseSkill", "isGameOver", "isTransitioning", "isActive", "game_result", "isVoting", "onClick", "disabled", "skillColor", "isOpen", "voteType", "ELIMINATION", "onClose", "onConfirm", "player<PERSON><PERSON>", "SEER", "availableActions", "_c18", "$RefreshReg$"], "sources": ["/private/peiwy/work/ai_projects/wolfkill/frontend/src/components/GameBoard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\nimport { GameState, GamePhase, GameResult, VoteType, Role } from '../types';\nimport { getPhaseColor } from '../styles/theme';\nimport VoteDialog from './dialogs/VoteDialog';\nimport ActionDialog from './dialogs/ActionDialog';\nimport { useGameContext } from '../contexts/GameContext';\nimport { ScaleIn, Pulse } from './animations/AnimatedComponents';\nimport { VoteAnimation, SkillAnimation, PhaseTransition } from './animations/GameAnimations';\n\ninterface GameBoardProps {\n  gameState: GameState | null;\n  onAction: (action: any) => void;\n}\n\nconst BoardContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  gap: ${props => props.theme.spacing.lg};\n`;\n\nconst StatusSection = styled.section`\n  background: ${props => props.theme.colors.background};\n  border-radius: ${props => props.theme.borderRadius.lg};\n  padding: ${props => props.theme.spacing.lg};\n  border: 1px solid ${props => props.theme.colors.border};\n`;\n\nconst StatusTitle = styled.h2`\n  font-size: ${props => props.theme.fontSizes.xl};\n  font-weight: ${props => props.theme.fontWeights.semibold};\n  color: ${props => props.theme.colors.text};\n  margin-bottom: ${props => props.theme.spacing.md};\n`;\n\nconst StatsGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n  gap: ${props => props.theme.spacing.md};\n  margin-bottom: ${props => props.theme.spacing.lg};\n`;\n\nconst StatCard = styled.div`\n  background: ${props => props.theme.colors.backgroundLight};\n  border-radius: ${props => props.theme.borderRadius.md};\n  padding: ${props => props.theme.spacing.md};\n  text-align: center;\n  border: 1px solid ${props => props.theme.colors.borderLight};\n`;\n\nconst StatValue = styled.div`\n  font-size: ${props => props.theme.fontSizes['2xl']};\n  font-weight: ${props => props.theme.fontWeights.bold};\n  color: ${props => props.theme.colors.primary};\n  margin-bottom: ${props => props.theme.spacing.xs};\n`;\n\nconst StatLabel = styled.div`\n  font-size: ${props => props.theme.fontSizes.sm};\n  color: ${props => props.theme.colors.textLight};\n`;\n\nconst PhaseSection = styled.section`\n  background: ${props => props.theme.colors.background};\n  border-radius: ${props => props.theme.borderRadius.lg};\n  padding: ${props => props.theme.spacing.lg};\n  border: 1px solid ${props => props.theme.colors.border};\n  text-align: center;\n`;\n\nconst PhaseTitle = styled.h3<{ phase?: GamePhase }>`\n  font-size: ${props => props.theme.fontSizes['2xl']};\n  font-weight: ${props => props.theme.fontWeights.bold};\n  color: ${props => props.phase ? getPhaseColor(props.phase) : props.theme.colors.text};\n  margin-bottom: ${props => props.theme.spacing.sm};\n`;\n\nconst PhaseDescription = styled.p`\n  font-size: ${props => props.theme.fontSizes.md};\n  color: ${props => props.theme.colors.textLight};\n  line-height: ${props => props.theme.lineHeights.relaxed};\n  margin-bottom: ${props => props.theme.spacing.lg};\n`;\n\nconst ActionSection = styled.section`\n  background: ${props => props.theme.colors.background};\n  border-radius: ${props => props.theme.borderRadius.lg};\n  padding: ${props => props.theme.spacing.lg};\n  border: 1px solid ${props => props.theme.colors.border};\n  flex: 1;\n`;\n\nconst ActionTitle = styled.h3`\n  font-size: ${props => props.theme.fontSizes.lg};\n  font-weight: ${props => props.theme.fontWeights.semibold};\n  color: ${props => props.theme.colors.text};\n  margin-bottom: ${props => props.theme.spacing.md};\n`;\n\nconst ButtonGroup = styled.div`\n  display: flex;\n  gap: ${props => props.theme.spacing.md};\n  margin-bottom: ${props => props.theme.spacing.lg};\n  flex-wrap: wrap;\n`;\n\nconst ActionButton = styled.button<{ variant?: 'primary' | 'secondary' | 'danger' | 'warning' }>`\n  padding: ${props => props.theme.spacing.md} ${props => props.theme.spacing.lg};\n  border-radius: ${props => props.theme.borderRadius.md};\n  font-size: ${props => props.theme.fontSizes.md};\n  font-weight: ${props => props.theme.fontWeights.medium};\n  transition: ${props => props.theme.transitions.fast};\n  min-width: 120px;\n\n  ${props => {\n    switch (props.variant) {\n      case 'secondary':\n        return `\n          background: ${props.theme.colors.secondary};\n          color: ${props.theme.colors.textWhite};\n          &:hover:not(:disabled) { background: ${props.theme.colors.secondaryLight}; }\n        `;\n      case 'danger':\n        return `\n          background: ${props.theme.colors.danger};\n          color: ${props.theme.colors.textWhite};\n          &:hover:not(:disabled) { background: ${props.theme.colors.dangerLight}; }\n        `;\n      case 'warning':\n        return `\n          background: ${props.theme.colors.warning};\n          color: ${props.theme.colors.textWhite};\n          &:hover:not(:disabled) { background: ${props.theme.colors.warningLight}; }\n        `;\n      default:\n        return `\n          background: ${props.theme.colors.primary};\n          color: ${props.theme.colors.textWhite};\n          &:hover:not(:disabled) { background: ${props.theme.colors.primaryLight}; }\n        `;\n    }\n  }}\n\n  &:disabled {\n    background: ${props => props.theme.colors.backgroundDark};\n    color: ${props => props.theme.colors.textLight};\n    cursor: not-allowed;\n  }\n`;\n\nconst HintArea = styled.div`\n  background: ${props => props.theme.colors.backgroundLight};\n  border-radius: ${props => props.theme.borderRadius.md};\n  padding: ${props => props.theme.spacing.md};\n  border-left: 4px solid ${props => props.theme.colors.info};\n  font-size: ${props => props.theme.fontSizes.sm};\n  color: ${props => props.theme.colors.textLight};\n  line-height: ${props => props.theme.lineHeights.relaxed};\n`;\n\nconst EmptyState = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 300px;\n  color: ${props => props.theme.colors.textLight};\n  text-align: center;\n`;\n\nconst EmptyIcon = styled.div`\n  font-size: 4rem;\n  margin-bottom: ${props => props.theme.spacing.md};\n`;\n\nconst EmptyText = styled.p`\n  font-size: ${props => props.theme.fontSizes.lg};\n  margin-bottom: ${props => props.theme.spacing.sm};\n`;\n\nconst EmptySubtext = styled.p`\n  font-size: ${props => props.theme.fontSizes.md};\n  color: ${props => props.theme.colors.textLight};\n`;\n\nconst getPhaseText = (phase: GamePhase): string => {\n  const phaseTexts = {\n    [GamePhase.SETUP]: '游戏设置',\n    [GamePhase.NIGHT]: '夜晚阶段',\n    [GamePhase.DAY_DISCUSSION]: '白天讨论',\n    [GamePhase.DAY_VOTING]: '白天投票',\n    [GamePhase.GAME_OVER]: '游戏结束'\n  };\n  return phaseTexts[phase] || '未知阶段';\n};\n\nconst getPhaseDescription = (phase: GamePhase): string => {\n  const descriptions = {\n    [GamePhase.SETUP]: '游戏准备中，等待所有玩家加入...',\n    [GamePhase.NIGHT]: '夜晚降临，狼人开始行动，特殊角色使用技能',\n    [GamePhase.DAY_DISCUSSION]: '白天到来，所有玩家开始发言讨论昨夜发生的事情',\n    [GamePhase.DAY_VOTING]: '投票阶段，选择要淘汰的可疑玩家',\n    [GamePhase.GAME_OVER]: '游戏结束，查看最终结果'\n  };\n  return descriptions[phase] || '';\n};\n\nconst getActionHints = (phase: GamePhase): string => {\n  const hints = {\n    [GamePhase.SETUP]: '等待游戏开始...',\n    [GamePhase.NIGHT]: '夜晚阶段：狼人选择杀害目标，预言家查验身份，女巫使用药剂，守卫保护玩家',\n    [GamePhase.DAY_DISCUSSION]: '白天讨论：所有玩家发言，分析昨夜情况，讨论可疑对象',\n    [GamePhase.DAY_VOTING]: '投票阶段：选择要淘汰的玩家，点击投票按钮确认，也可以选择弃权',\n    [GamePhase.GAME_OVER]: '游戏已结束，查看最终结果'\n  };\n  return hints[phase] || '';\n};\n\nconst getResultText = (result: GameResult): string => {\n  const resultTexts = {\n    [GameResult.VILLAGERS_WIN]: '村民阵营胜利！',\n    [GameResult.WEREWOLVES_WIN]: '狼人阵营胜利！',\n    [GameResult.DRAW]: '平局！',\n    [GameResult.ONGOING]: '游戏进行中'\n  };\n  return resultTexts[result] || '未知结果';\n};\n\nconst GameBoard: React.FC<GameBoardProps> = ({ gameState, onAction }) => {\n  const [selectedAction, setSelectedAction] = useState<string | null>(null);\n  const [showVoteDialog, setShowVoteDialog] = useState(false);\n  const [showActionDialog, setShowActionDialog] = useState(false);\n  const [isPhaseTransitioning, setIsPhaseTransitioning] = useState(false);\n  const [previousPhase, setPreviousPhase] = useState<GamePhase | null>(null);\n\n  const { submitVote, submitAction } = useGameContext();\n\n  // 监听阶段变化\n  useEffect(() => {\n    if (gameState && previousPhase && previousPhase !== gameState.current_phase) {\n      setIsPhaseTransitioning(true);\n      const timer = setTimeout(() => {\n        setIsPhaseTransitioning(false);\n      }, 1000);\n      return () => clearTimeout(timer);\n    }\n    if (gameState) {\n      setPreviousPhase(gameState.current_phase);\n    }\n  }, [gameState?.current_phase, previousPhase]);\n\n  if (!gameState) {\n    return (\n      <BoardContainer>\n        <EmptyState>\n          <EmptyIcon>🎮</EmptyIcon>\n          <EmptyText>暂无游戏</EmptyText>\n          <EmptySubtext>点击右上角\"创建游戏\"开始新的狼人杀游戏</EmptySubtext>\n        </EmptyState>\n      </BoardContainer>\n    );\n  }\n\n  const totalPlayers = Object.keys(gameState.players).length;\n  const alivePlayers = gameState.alive_players.length;\n  const deadPlayers = gameState.dead_players.length;\n\n  // 计算阵营统计\n  const villagerCount = gameState.alive_players.filter(id => {\n    const player = gameState.players[id];\n    return player && player.faction === 'VILLAGERS';\n  }).length;\n\n  const werewolfCount = gameState.alive_players.filter(id => {\n    const player = gameState.players[id];\n    return player && player.faction === 'WEREWOLVES';\n  }).length;\n\n  const handleVote = () => {\n    setShowVoteDialog(true);\n  };\n\n  const handleSkill = () => {\n    setShowActionDialog(true);\n  };\n\n  const handleAbstain = () => {\n    submitVote(null, '选择弃权');\n  };\n\n  const handleVoteConfirm = async (targetId: number | null, reason?: string) => {\n    try {\n      await submitVote(targetId, reason);\n      setShowVoteDialog(false);\n    } catch (error) {\n      console.error('Vote failed:', error);\n    }\n  };\n\n  const handleActionConfirm = async (actionType: string, targetId?: number) => {\n    try {\n      await submitAction(actionType, targetId);\n      setShowActionDialog(false);\n    } catch (error) {\n      console.error('Action failed:', error);\n    }\n  };\n\n  const canVote = gameState.current_phase === GamePhase.DAY_VOTING;\n  const canUseSkill = gameState.current_phase === GamePhase.NIGHT;\n  const isGameOver = gameState.current_phase === GamePhase.GAME_OVER;\n\n  return (\n    <BoardContainer>\n      {/* 游戏状态统计 */}\n      <StatusSection>\n        <StatusTitle>游戏状态</StatusTitle>\n        <StatsGrid>\n          <StatCard>\n            <StatValue>{totalPlayers}</StatValue>\n            <StatLabel>总玩家</StatLabel>\n          </StatCard>\n          <StatCard>\n            <StatValue>{alivePlayers}</StatValue>\n            <StatLabel>存活</StatLabel>\n          </StatCard>\n          <StatCard>\n            <StatValue>{deadPlayers}</StatValue>\n            <StatLabel>死亡</StatLabel>\n          </StatCard>\n          <StatCard>\n            <StatValue>{villagerCount}</StatValue>\n            <StatLabel>村民</StatLabel>\n          </StatCard>\n          <StatCard>\n            <StatValue>{werewolfCount}</StatValue>\n            <StatLabel>狼人</StatLabel>\n          </StatCard>\n        </StatsGrid>\n      </StatusSection>\n\n      {/* 当前阶段 */}\n      <PhaseSection>\n        <PhaseTransition isTransitioning={isPhaseTransitioning}>\n          <Pulse isActive={!isGameOver}>\n            <PhaseTitle phase={gameState.current_phase}>\n              {getPhaseText(gameState.current_phase)}\n            </PhaseTitle>\n          </Pulse>\n          <PhaseDescription>\n            {getPhaseDescription(gameState.current_phase)}\n          </PhaseDescription>\n\n          {isGameOver && (\n            <ScaleIn>\n              <PhaseTitle phase={gameState.current_phase}>\n                {getResultText(gameState.game_result)}\n              </PhaseTitle>\n            </ScaleIn>\n          )}\n        </PhaseTransition>\n      </PhaseSection>\n\n      {/* 操作区域 */}\n      <ActionSection>\n        <ActionTitle>可用操作</ActionTitle>\n\n        <ButtonGroup>\n          <VoteAnimation isVoting={showVoteDialog}>\n            <ActionButton\n              onClick={handleVote}\n              disabled={!canVote}\n              variant=\"primary\"\n            >\n              投票\n            </ActionButton>\n          </VoteAnimation>\n\n          <SkillAnimation isActive={showActionDialog} skillColor=\"#E67E22\">\n            <ActionButton\n              onClick={handleSkill}\n              disabled={!canUseSkill}\n              variant=\"secondary\"\n            >\n              使用技能\n            </ActionButton>\n          </SkillAnimation>\n\n          <ActionButton\n            onClick={handleAbstain}\n            disabled={!canVote}\n            variant=\"warning\"\n          >\n            弃权\n          </ActionButton>\n        </ButtonGroup>\n\n        <HintArea>\n          {getActionHints(gameState.current_phase)}\n        </HintArea>\n      </ActionSection>\n\n      {/* 投票对话框 */}\n      <VoteDialog\n        isOpen={showVoteDialog}\n        players={gameState.players}\n        voteType={VoteType.ELIMINATION}\n        onClose={() => setShowVoteDialog(false)}\n        onConfirm={handleVoteConfirm}\n      />\n\n      {/* 行动对话框 */}\n      <ActionDialog\n        isOpen={showActionDialog}\n        playerRole={Role.SEER} // TODO: 获取当前玩家角色\n        players={gameState.players}\n        availableActions={['seer_check']} // TODO: 根据角色获取可用行动\n        onClose={() => setShowActionDialog(false)}\n        onConfirm={handleActionConfirm}\n      />\n    </BoardContainer>\n  );\n};\n\nexport default GameBoard;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAAoBC,SAAS,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,IAAI,QAAQ,UAAU;AAC3E,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,OAAOC,UAAU,MAAM,sBAAsB;AAC7C,OAAOC,YAAY,MAAM,wBAAwB;AACjD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,OAAO,EAAEC,KAAK,QAAQ,iCAAiC;AAChE,SAASC,aAAa,EAAEC,cAAc,EAAEC,eAAe,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAO7F,MAAMC,cAAc,GAAGhB,MAAM,CAACiB,GAAG;AACjC;AACA;AACA;AACA,SAASC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACC,EAAE;AACxC,CAAC;AAACC,EAAA,GALIN,cAAc;AAOpB,MAAMO,aAAa,GAAGvB,MAAM,CAACwB,OAAO;AACpC,gBAAgBN,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACM,MAAM,CAACC,UAAU;AACtD,mBAAmBR,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACQ,YAAY,CAACN,EAAE;AACvD,aAAaH,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACC,EAAE;AAC5C,sBAAsBH,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACM,MAAM,CAACG,MAAM;AACxD,CAAC;AAACC,GAAA,GALIN,aAAa;AAOnB,MAAMO,WAAW,GAAG9B,MAAM,CAAC+B,EAAE;AAC7B,eAAeb,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACa,SAAS,CAACC,EAAE;AAChD,iBAAiBf,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACe,WAAW,CAACC,QAAQ;AAC1D,WAAWjB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACM,MAAM,CAACW,IAAI;AAC3C,mBAAmBlB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACiB,EAAE;AAClD,CAAC;AAACC,GAAA,GALIR,WAAW;AAOjB,MAAMS,SAAS,GAAGvC,MAAM,CAACiB,GAAG;AAC5B;AACA;AACA,SAASC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACiB,EAAE;AACxC,mBAAmBnB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACC,EAAE;AAClD,CAAC;AAACmB,GAAA,GALID,SAAS;AAOf,MAAME,QAAQ,GAAGzC,MAAM,CAACiB,GAAG;AAC3B,gBAAgBC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACM,MAAM,CAACiB,eAAe;AAC3D,mBAAmBxB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACQ,YAAY,CAACU,EAAE;AACvD,aAAanB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACiB,EAAE;AAC5C;AACA,sBAAsBnB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACM,MAAM,CAACkB,WAAW;AAC7D,CAAC;AAACC,GAAA,GANIH,QAAQ;AAQd,MAAMI,SAAS,GAAG7C,MAAM,CAACiB,GAAG;AAC5B,eAAeC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACa,SAAS,CAAC,KAAK,CAAC;AACpD,iBAAiBd,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACe,WAAW,CAACY,IAAI;AACtD,WAAW5B,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACM,MAAM,CAACsB,OAAO;AAC9C,mBAAmB7B,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAAC4B,EAAE;AAClD,CAAC;AAACC,GAAA,GALIJ,SAAS;AAOf,MAAMK,SAAS,GAAGlD,MAAM,CAACiB,GAAG;AAC5B,eAAeC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACa,SAAS,CAACmB,EAAE;AAChD,WAAWjC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACM,MAAM,CAAC2B,SAAS;AAChD,CAAC;AAACC,GAAA,GAHIH,SAAS;AAKf,MAAMI,YAAY,GAAGtD,MAAM,CAACwB,OAAO;AACnC,gBAAgBN,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACM,MAAM,CAACC,UAAU;AACtD,mBAAmBR,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACQ,YAAY,CAACN,EAAE;AACvD,aAAaH,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACC,EAAE;AAC5C,sBAAsBH,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACM,MAAM,CAACG,MAAM;AACxD;AACA,CAAC;AAAC2B,GAAA,GANID,YAAY;AAQlB,MAAME,UAAU,GAAGxD,MAAM,CAACyD,EAAyB;AACnD,eAAevC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACa,SAAS,CAAC,KAAK,CAAC;AACpD,iBAAiBd,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACe,WAAW,CAACY,IAAI;AACtD,WAAW5B,KAAK,IAAIA,KAAK,CAACwC,KAAK,GAAGrD,aAAa,CAACa,KAAK,CAACwC,KAAK,CAAC,GAAGxC,KAAK,CAACC,KAAK,CAACM,MAAM,CAACW,IAAI;AACtF,mBAAmBlB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAAC+B,EAAE;AAClD,CAAC;AAACQ,GAAA,GALIH,UAAU;AAOhB,MAAMI,gBAAgB,GAAG5D,MAAM,CAAC6D,CAAC;AACjC,eAAe3C,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACa,SAAS,CAACK,EAAE;AAChD,WAAWnB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACM,MAAM,CAAC2B,SAAS;AAChD,iBAAiBlC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAAC2C,WAAW,CAACC,OAAO;AACzD,mBAAmB7C,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACC,EAAE;AAClD,CAAC;AAAC2C,GAAA,GALIJ,gBAAgB;AAOtB,MAAMK,aAAa,GAAGjE,MAAM,CAACwB,OAAO;AACpC,gBAAgBN,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACM,MAAM,CAACC,UAAU;AACtD,mBAAmBR,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACQ,YAAY,CAACN,EAAE;AACvD,aAAaH,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACC,EAAE;AAC5C,sBAAsBH,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACM,MAAM,CAACG,MAAM;AACxD;AACA,CAAC;AAACsC,GAAA,GANID,aAAa;AAQnB,MAAME,WAAW,GAAGnE,MAAM,CAACyD,EAAE;AAC7B,eAAevC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACa,SAAS,CAACX,EAAE;AAChD,iBAAiBH,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACe,WAAW,CAACC,QAAQ;AAC1D,WAAWjB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACM,MAAM,CAACW,IAAI;AAC3C,mBAAmBlB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACiB,EAAE;AAClD,CAAC;AAAC+B,IAAA,GALID,WAAW;AAOjB,MAAME,WAAW,GAAGrE,MAAM,CAACiB,GAAG;AAC9B;AACA,SAASC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACiB,EAAE;AACxC,mBAAmBnB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACC,EAAE;AAClD;AACA,CAAC;AAACiD,IAAA,GALID,WAAW;AAOjB,MAAME,YAAY,GAAGvE,MAAM,CAACwE,MAAoE;AAChG,aAAatD,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACiB,EAAE,IAAInB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACC,EAAE;AAC/E,mBAAmBH,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACQ,YAAY,CAACU,EAAE;AACvD,eAAenB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACa,SAAS,CAACK,EAAE;AAChD,iBAAiBnB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACe,WAAW,CAACuC,MAAM;AACxD,gBAAgBvD,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACuD,WAAW,CAACC,IAAI;AACrD;AACA;AACA,IAAIzD,KAAK,IAAI;EACT,QAAQA,KAAK,CAAC0D,OAAO;IACnB,KAAK,WAAW;MACd,OAAO;AACf,wBAAwB1D,KAAK,CAACC,KAAK,CAACM,MAAM,CAACoD,SAAS;AACpD,mBAAmB3D,KAAK,CAACC,KAAK,CAACM,MAAM,CAACqD,SAAS;AAC/C,iDAAiD5D,KAAK,CAACC,KAAK,CAACM,MAAM,CAACsD,cAAc;AAClF,SAAS;IACH,KAAK,QAAQ;MACX,OAAO;AACf,wBAAwB7D,KAAK,CAACC,KAAK,CAACM,MAAM,CAACuD,MAAM;AACjD,mBAAmB9D,KAAK,CAACC,KAAK,CAACM,MAAM,CAACqD,SAAS;AAC/C,iDAAiD5D,KAAK,CAACC,KAAK,CAACM,MAAM,CAACwD,WAAW;AAC/E,SAAS;IACH,KAAK,SAAS;MACZ,OAAO;AACf,wBAAwB/D,KAAK,CAACC,KAAK,CAACM,MAAM,CAACyD,OAAO;AAClD,mBAAmBhE,KAAK,CAACC,KAAK,CAACM,MAAM,CAACqD,SAAS;AAC/C,iDAAiD5D,KAAK,CAACC,KAAK,CAACM,MAAM,CAAC0D,YAAY;AAChF,SAAS;IACH;MACE,OAAO;AACf,wBAAwBjE,KAAK,CAACC,KAAK,CAACM,MAAM,CAACsB,OAAO;AAClD,mBAAmB7B,KAAK,CAACC,KAAK,CAACM,MAAM,CAACqD,SAAS;AAC/C,iDAAiD5D,KAAK,CAACC,KAAK,CAACM,MAAM,CAAC2D,YAAY;AAChF,SAAS;EACL;AACF,CAAC;AACH;AACA;AACA,kBAAkBlE,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACM,MAAM,CAAC4D,cAAc;AAC5D,aAAanE,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACM,MAAM,CAAC2B,SAAS;AAClD;AACA;AACA,CAAC;AAACkC,IAAA,GA1CIf,YAAY;AA4ClB,MAAMgB,QAAQ,GAAGvF,MAAM,CAACiB,GAAG;AAC3B,gBAAgBC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACM,MAAM,CAACiB,eAAe;AAC3D,mBAAmBxB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACQ,YAAY,CAACU,EAAE;AACvD,aAAanB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACiB,EAAE;AAC5C,2BAA2BnB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACM,MAAM,CAAC+D,IAAI;AAC3D,eAAetE,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACa,SAAS,CAACmB,EAAE;AAChD,WAAWjC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACM,MAAM,CAAC2B,SAAS;AAChD,iBAAiBlC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAAC2C,WAAW,CAACC,OAAO;AACzD,CAAC;AAAC0B,IAAA,GARIF,QAAQ;AAUd,MAAMG,UAAU,GAAG1F,MAAM,CAACiB,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA,WAAWC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACM,MAAM,CAAC2B,SAAS;AAChD;AACA,CAAC;AAACuC,IAAA,GARID,UAAU;AAUhB,MAAME,SAAS,GAAG5F,MAAM,CAACiB,GAAG;AAC5B;AACA,mBAAmBC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACiB,EAAE;AAClD,CAAC;AAACwD,IAAA,GAHID,SAAS;AAKf,MAAME,SAAS,GAAG9F,MAAM,CAAC6D,CAAC;AAC1B,eAAe3C,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACa,SAAS,CAACX,EAAE;AAChD,mBAAmBH,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAAC+B,EAAE;AAClD,CAAC;AAAC4C,IAAA,GAHID,SAAS;AAKf,MAAME,YAAY,GAAGhG,MAAM,CAAC6D,CAAC;AAC7B,eAAe3C,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACa,SAAS,CAACK,EAAE;AAChD,WAAWnB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACM,MAAM,CAAC2B,SAAS;AAChD,CAAC;AAAC6C,IAAA,GAHID,YAAY;AAKlB,MAAME,YAAY,GAAIxC,KAAgB,IAAa;EACjD,MAAMyC,UAAU,GAAG;IACjB,CAAClG,SAAS,CAACmG,KAAK,GAAG,MAAM;IACzB,CAACnG,SAAS,CAACoG,KAAK,GAAG,MAAM;IACzB,CAACpG,SAAS,CAACqG,cAAc,GAAG,MAAM;IAClC,CAACrG,SAAS,CAACsG,UAAU,GAAG,MAAM;IAC9B,CAACtG,SAAS,CAACuG,SAAS,GAAG;EACzB,CAAC;EACD,OAAOL,UAAU,CAACzC,KAAK,CAAC,IAAI,MAAM;AACpC,CAAC;AAED,MAAM+C,mBAAmB,GAAI/C,KAAgB,IAAa;EACxD,MAAMgD,YAAY,GAAG;IACnB,CAACzG,SAAS,CAACmG,KAAK,GAAG,mBAAmB;IACtC,CAACnG,SAAS,CAACoG,KAAK,GAAG,sBAAsB;IACzC,CAACpG,SAAS,CAACqG,cAAc,GAAG,wBAAwB;IACpD,CAACrG,SAAS,CAACsG,UAAU,GAAG,iBAAiB;IACzC,CAACtG,SAAS,CAACuG,SAAS,GAAG;EACzB,CAAC;EACD,OAAOE,YAAY,CAAChD,KAAK,CAAC,IAAI,EAAE;AAClC,CAAC;AAED,MAAMiD,cAAc,GAAIjD,KAAgB,IAAa;EACnD,MAAMkD,KAAK,GAAG;IACZ,CAAC3G,SAAS,CAACmG,KAAK,GAAG,WAAW;IAC9B,CAACnG,SAAS,CAACoG,KAAK,GAAG,qCAAqC;IACxD,CAACpG,SAAS,CAACqG,cAAc,GAAG,2BAA2B;IACvD,CAACrG,SAAS,CAACsG,UAAU,GAAG,gCAAgC;IACxD,CAACtG,SAAS,CAACuG,SAAS,GAAG;EACzB,CAAC;EACD,OAAOI,KAAK,CAAClD,KAAK,CAAC,IAAI,EAAE;AAC3B,CAAC;AAED,MAAMmD,aAAa,GAAIC,MAAkB,IAAa;EACpD,MAAMC,WAAW,GAAG;IAClB,CAAC7G,UAAU,CAAC8G,aAAa,GAAG,SAAS;IACrC,CAAC9G,UAAU,CAAC+G,cAAc,GAAG,SAAS;IACtC,CAAC/G,UAAU,CAACgH,IAAI,GAAG,KAAK;IACxB,CAAChH,UAAU,CAACiH,OAAO,GAAG;EACxB,CAAC;EACD,OAAOJ,WAAW,CAACD,MAAM,CAAC,IAAI,MAAM;AACtC,CAAC;AAED,MAAMM,SAAmC,GAAGA,CAAC;EAAEC,SAAS;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACvE,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG3H,QAAQ,CAAgB,IAAI,CAAC;EACzE,MAAM,CAAC4H,cAAc,EAAEC,iBAAiB,CAAC,GAAG7H,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC8H,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/H,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACgI,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGjI,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACkI,aAAa,EAAEC,gBAAgB,CAAC,GAAGnI,QAAQ,CAAmB,IAAI,CAAC;EAE1E,MAAM;IAAEoI,UAAU;IAAEC;EAAa,CAAC,GAAG3H,cAAc,CAAC,CAAC;;EAErD;EACAT,SAAS,CAAC,MAAM;IACd,IAAIsH,SAAS,IAAIW,aAAa,IAAIA,aAAa,KAAKX,SAAS,CAACe,aAAa,EAAE;MAC3EL,uBAAuB,CAAC,IAAI,CAAC;MAC7B,MAAMM,KAAK,GAAGC,UAAU,CAAC,MAAM;QAC7BP,uBAAuB,CAAC,KAAK,CAAC;MAChC,CAAC,EAAE,IAAI,CAAC;MACR,OAAO,MAAMQ,YAAY,CAACF,KAAK,CAAC;IAClC;IACA,IAAIhB,SAAS,EAAE;MACbY,gBAAgB,CAACZ,SAAS,CAACe,aAAa,CAAC;IAC3C;EACF,CAAC,EAAE,CAACf,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEe,aAAa,EAAEJ,aAAa,CAAC,CAAC;EAE7C,IAAI,CAACX,SAAS,EAAE;IACd,oBACEtG,OAAA,CAACC,cAAc;MAAAwH,QAAA,eACbzH,OAAA,CAAC2E,UAAU;QAAA8C,QAAA,gBACTzH,OAAA,CAAC6E,SAAS;UAAA4C,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eACzB7H,OAAA,CAAC+E,SAAS;UAAA0C,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eAC3B7H,OAAA,CAACiF,YAAY;UAAAwC,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAErB;EAEA,MAAMC,YAAY,GAAGC,MAAM,CAACC,IAAI,CAAC1B,SAAS,CAAC2B,OAAO,CAAC,CAACC,MAAM;EAC1D,MAAMC,YAAY,GAAG7B,SAAS,CAAC8B,aAAa,CAACF,MAAM;EACnD,MAAMG,WAAW,GAAG/B,SAAS,CAACgC,YAAY,CAACJ,MAAM;;EAEjD;EACA,MAAMK,aAAa,GAAGjC,SAAS,CAAC8B,aAAa,CAACI,MAAM,CAACC,EAAE,IAAI;IACzD,MAAMC,MAAM,GAAGpC,SAAS,CAAC2B,OAAO,CAACQ,EAAE,CAAC;IACpC,OAAOC,MAAM,IAAIA,MAAM,CAACC,OAAO,KAAK,WAAW;EACjD,CAAC,CAAC,CAACT,MAAM;EAET,MAAMU,aAAa,GAAGtC,SAAS,CAAC8B,aAAa,CAACI,MAAM,CAACC,EAAE,IAAI;IACzD,MAAMC,MAAM,GAAGpC,SAAS,CAAC2B,OAAO,CAACQ,EAAE,CAAC;IACpC,OAAOC,MAAM,IAAIA,MAAM,CAACC,OAAO,KAAK,YAAY;EAClD,CAAC,CAAC,CAACT,MAAM;EAET,MAAMW,UAAU,GAAGA,CAAA,KAAM;IACvBjC,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMkC,WAAW,GAAGA,CAAA,KAAM;IACxBhC,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAMiC,aAAa,GAAGA,CAAA,KAAM;IAC1B5B,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC;EAC1B,CAAC;EAED,MAAM6B,iBAAiB,GAAG,MAAAA,CAAOC,QAAuB,EAAEC,MAAe,KAAK;IAC5E,IAAI;MACF,MAAM/B,UAAU,CAAC8B,QAAQ,EAAEC,MAAM,CAAC;MAClCtC,iBAAiB,CAAC,KAAK,CAAC;IAC1B,CAAC,CAAC,OAAOuC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;IACtC;EACF,CAAC;EAED,MAAME,mBAAmB,GAAG,MAAAA,CAAOC,UAAkB,EAAEL,QAAiB,KAAK;IAC3E,IAAI;MACF,MAAM7B,YAAY,CAACkC,UAAU,EAAEL,QAAQ,CAAC;MACxCnC,mBAAmB,CAAC,KAAK,CAAC;IAC5B,CAAC,CAAC,OAAOqC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;IACxC;EACF,CAAC;EAED,MAAMI,OAAO,GAAGjD,SAAS,CAACe,aAAa,KAAKnI,SAAS,CAACsG,UAAU;EAChE,MAAMgE,WAAW,GAAGlD,SAAS,CAACe,aAAa,KAAKnI,SAAS,CAACoG,KAAK;EAC/D,MAAMmE,UAAU,GAAGnD,SAAS,CAACe,aAAa,KAAKnI,SAAS,CAACuG,SAAS;EAElE,oBACEzF,OAAA,CAACC,cAAc;IAAAwH,QAAA,gBAEbzH,OAAA,CAACQ,aAAa;MAAAiH,QAAA,gBACZzH,OAAA,CAACe,WAAW;QAAA0G,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAC/B7H,OAAA,CAACwB,SAAS;QAAAiG,QAAA,gBACRzH,OAAA,CAAC0B,QAAQ;UAAA+F,QAAA,gBACPzH,OAAA,CAAC8B,SAAS;YAAA2F,QAAA,EAAEK;UAAY;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACrC7H,OAAA,CAACmC,SAAS;YAAAsF,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,eACX7H,OAAA,CAAC0B,QAAQ;UAAA+F,QAAA,gBACPzH,OAAA,CAAC8B,SAAS;YAAA2F,QAAA,EAAEU;UAAY;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACrC7H,OAAA,CAACmC,SAAS;YAAAsF,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,eACX7H,OAAA,CAAC0B,QAAQ;UAAA+F,QAAA,gBACPzH,OAAA,CAAC8B,SAAS;YAAA2F,QAAA,EAAEY;UAAW;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACpC7H,OAAA,CAACmC,SAAS;YAAAsF,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,eACX7H,OAAA,CAAC0B,QAAQ;UAAA+F,QAAA,gBACPzH,OAAA,CAAC8B,SAAS;YAAA2F,QAAA,EAAEc;UAAa;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACtC7H,OAAA,CAACmC,SAAS;YAAAsF,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,eACX7H,OAAA,CAAC0B,QAAQ;UAAA+F,QAAA,gBACPzH,OAAA,CAAC8B,SAAS;YAAA2F,QAAA,EAAEmB;UAAa;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACtC7H,OAAA,CAACmC,SAAS;YAAAsF,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGhB7H,OAAA,CAACuC,YAAY;MAAAkF,QAAA,eACXzH,OAAA,CAACF,eAAe;QAAC4J,eAAe,EAAE3C,oBAAqB;QAAAU,QAAA,gBACrDzH,OAAA,CAACL,KAAK;UAACgK,QAAQ,EAAE,CAACF,UAAW;UAAAhC,QAAA,eAC3BzH,OAAA,CAACyC,UAAU;YAACE,KAAK,EAAE2D,SAAS,CAACe,aAAc;YAAAI,QAAA,EACxCtC,YAAY,CAACmB,SAAS,CAACe,aAAa;UAAC;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eACR7H,OAAA,CAAC6C,gBAAgB;UAAA4E,QAAA,EACd/B,mBAAmB,CAACY,SAAS,CAACe,aAAa;QAAC;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,EAElB4B,UAAU,iBACTzJ,OAAA,CAACN,OAAO;UAAA+H,QAAA,eACNzH,OAAA,CAACyC,UAAU;YAACE,KAAK,EAAE2D,SAAS,CAACe,aAAc;YAAAI,QAAA,EACxC3B,aAAa,CAACQ,SAAS,CAACsD,WAAW;UAAC;YAAAlC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACV;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACc;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGf7H,OAAA,CAACkD,aAAa;MAAAuE,QAAA,gBACZzH,OAAA,CAACoD,WAAW;QAAAqE,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAE/B7H,OAAA,CAACsD,WAAW;QAAAmE,QAAA,gBACVzH,OAAA,CAACJ,aAAa;UAACiK,QAAQ,EAAElD,cAAe;UAAAc,QAAA,eACtCzH,OAAA,CAACwD,YAAY;YACXsG,OAAO,EAAEjB,UAAW;YACpBkB,QAAQ,EAAE,CAACR,OAAQ;YACnB1F,OAAO,EAAC,SAAS;YAAA4D,QAAA,EAClB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEhB7H,OAAA,CAACH,cAAc;UAAC8J,QAAQ,EAAE9C,gBAAiB;UAACmD,UAAU,EAAC,SAAS;UAAAvC,QAAA,eAC9DzH,OAAA,CAACwD,YAAY;YACXsG,OAAO,EAAEhB,WAAY;YACrBiB,QAAQ,EAAE,CAACP,WAAY;YACvB3F,OAAO,EAAC,WAAW;YAAA4D,QAAA,EACpB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAEjB7H,OAAA,CAACwD,YAAY;UACXsG,OAAO,EAAEf,aAAc;UACvBgB,QAAQ,EAAE,CAACR,OAAQ;UACnB1F,OAAO,EAAC,SAAS;UAAA4D,QAAA,EAClB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEd7H,OAAA,CAACwE,QAAQ;QAAAiD,QAAA,EACN7B,cAAc,CAACU,SAAS,CAACe,aAAa;MAAC;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGhB7H,OAAA,CAACT,UAAU;MACT0K,MAAM,EAAEtD,cAAe;MACvBsB,OAAO,EAAE3B,SAAS,CAAC2B,OAAQ;MAC3BiC,QAAQ,EAAE9K,QAAQ,CAAC+K,WAAY;MAC/BC,OAAO,EAAEA,CAAA,KAAMxD,iBAAiB,CAAC,KAAK,CAAE;MACxCyD,SAAS,EAAErB;IAAkB;MAAAtB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC,eAGF7H,OAAA,CAACR,YAAY;MACXyK,MAAM,EAAEpD,gBAAiB;MACzByD,UAAU,EAAEjL,IAAI,CAACkL,IAAK,CAAC;MAAA;MACvBtC,OAAO,EAAE3B,SAAS,CAAC2B,OAAQ;MAC3BuC,gBAAgB,EAAE,CAAC,YAAY,CAAE,CAAC;MAAA;MAClCJ,OAAO,EAAEA,CAAA,KAAMtD,mBAAmB,CAAC,KAAK,CAAE;MAC1CuD,SAAS,EAAEhB;IAAoB;MAAA3B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACY,CAAC;AAErB,CAAC;AAACrB,EAAA,CAlMIH,SAAmC;EAAA,QAOF5G,cAAc;AAAA;AAAAgL,IAAA,GAP/CpE,SAAmC;AAoMzC,eAAeA,SAAS;AAAC,IAAA9F,EAAA,EAAAO,GAAA,EAAAS,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAK,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAK,GAAA,EAAAE,GAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAgB,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAuF,IAAA;AAAAC,YAAA,CAAAnK,EAAA;AAAAmK,YAAA,CAAA5J,GAAA;AAAA4J,YAAA,CAAAnJ,GAAA;AAAAmJ,YAAA,CAAAjJ,GAAA;AAAAiJ,YAAA,CAAA7I,GAAA;AAAA6I,YAAA,CAAAxI,GAAA;AAAAwI,YAAA,CAAApI,GAAA;AAAAoI,YAAA,CAAAlI,GAAA;AAAAkI,YAAA,CAAA9H,GAAA;AAAA8H,YAAA,CAAAzH,GAAA;AAAAyH,YAAA,CAAAvH,GAAA;AAAAuH,YAAA,CAAArH,IAAA;AAAAqH,YAAA,CAAAnH,IAAA;AAAAmH,YAAA,CAAAnG,IAAA;AAAAmG,YAAA,CAAAhG,IAAA;AAAAgG,YAAA,CAAA9F,IAAA;AAAA8F,YAAA,CAAA5F,IAAA;AAAA4F,YAAA,CAAA1F,IAAA;AAAA0F,YAAA,CAAAxF,IAAA;AAAAwF,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}