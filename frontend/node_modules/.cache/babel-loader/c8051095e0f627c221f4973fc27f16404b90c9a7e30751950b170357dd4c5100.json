{"ast": null, "code": "var _jsxFileName = \"/private/peiwy/work/ai_projects/wolfkill/frontend/src/App.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport styled, { ThemeProvider, createGlobalStyle } from 'styled-components';\nimport { theme } from './styles/theme';\nimport { GamePhase } from './types';\nimport GameBoard from './components/GameBoard';\nimport PlayerList from './components/PlayerList';\nimport ChatPanel from './components/ChatPanel';\nimport Header from './components/Header';\nimport { useGameContext } from './contexts/GameContext';\n\n// 全局样式\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst GlobalStyle = createGlobalStyle`\n  * {\n    margin: 0;\n    padding: 0;\n    box-sizing: border-box;\n  }\n\n  body {\n    font-family: ${props => props.theme.fonts.primary};\n    background-color: ${props => props.theme.colors.background};\n    color: ${props => props.theme.colors.text};\n    line-height: ${props => props.theme.lineHeights.normal};\n  }\n\n  button {\n    font-family: inherit;\n    cursor: pointer;\n    border: none;\n    outline: none;\n    transition: ${props => props.theme.transitions.fast};\n  }\n\n  input, textarea {\n    font-family: inherit;\n    outline: none;\n  }\n`;\n\n// 样式组件\n_c = GlobalStyle;\nconst AppContainer = styled.div`\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n`;\n_c2 = AppContainer;\nconst MainContent = styled.main`\n  flex: 1;\n  display: grid;\n  grid-template-columns: 250px 1fr 300px;\n  grid-template-rows: 1fr;\n  gap: ${props => props.theme.spacing.md};\n  padding: ${props => props.theme.spacing.md};\n  max-width: 1400px;\n  margin: 0 auto;\n  width: 100%;\n\n  @media (max-width: ${props => props.theme.breakpoints.lg}) {\n    grid-template-columns: 1fr;\n    grid-template-rows: auto auto 1fr;\n  }\n`;\n_c3 = MainContent;\nconst LeftPanel = styled.aside`\n  background: ${props => props.theme.colors.backgroundLight};\n  border-radius: ${props => props.theme.borderRadius.lg};\n  box-shadow: ${props => props.theme.shadows.md};\n  padding: ${props => props.theme.spacing.lg};\n  overflow-y: auto;\n\n  @media (max-width: ${props => props.theme.breakpoints.lg}) {\n    order: 2;\n  }\n`;\n_c4 = LeftPanel;\nconst CenterPanel = styled.section`\n  background: ${props => props.theme.colors.backgroundLight};\n  border-radius: ${props => props.theme.borderRadius.lg};\n  box-shadow: ${props => props.theme.shadows.md};\n  padding: ${props => props.theme.spacing.lg};\n  overflow-y: auto;\n\n  @media (max-width: ${props => props.theme.breakpoints.lg}) {\n    order: 1;\n  }\n`;\n_c5 = CenterPanel;\nconst RightPanel = styled.aside`\n  background: ${props => props.theme.colors.backgroundLight};\n  border-radius: ${props => props.theme.borderRadius.lg};\n  box-shadow: ${props => props.theme.shadows.md};\n  padding: ${props => props.theme.spacing.lg};\n  overflow-y: auto;\n\n  @media (max-width: ${props => props.theme.breakpoints.lg}) {\n    order: 3;\n  }\n`;\n_c6 = RightPanel;\nconst LoadingContainer = styled.div`\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100vh;\n  font-size: ${props => props.theme.fontSizes.xl};\n  color: ${props => props.theme.colors.textLight};\n`;\n_c7 = LoadingContainer;\nconst ErrorContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  height: 100vh;\n  padding: ${props => props.theme.spacing.xl};\n  text-align: center;\n`;\n_c8 = ErrorContainer;\nconst ErrorTitle = styled.h1`\n  color: ${props => props.theme.colors.danger};\n  font-size: ${props => props.theme.fontSizes['2xl']};\n  margin-bottom: ${props => props.theme.spacing.md};\n`;\n_c9 = ErrorTitle;\nconst ErrorMessage = styled.p`\n  color: ${props => props.theme.colors.textLight};\n  font-size: ${props => props.theme.fontSizes.lg};\n  margin-bottom: ${props => props.theme.spacing.lg};\n`;\n_c0 = ErrorMessage;\nconst RetryButton = styled.button`\n  background: ${props => props.theme.colors.primary};\n  color: ${props => props.theme.colors.textWhite};\n  padding: ${props => props.theme.spacing.md} ${props => props.theme.spacing.lg};\n  border-radius: ${props => props.theme.borderRadius.md};\n  font-size: ${props => props.theme.fontSizes.md};\n  font-weight: ${props => props.theme.fontWeights.medium};\n\n  &:hover {\n    background: ${props => props.theme.colors.primaryLight};\n  }\n`;\n\n// 主应用组件\n_c1 = RetryButton;\nconst AppContent = () => {\n  _s();\n  const {\n    gameState,\n    loading,\n    error,\n    connected,\n    messages,\n    selectedPlayerId,\n    createGame,\n    selectPlayer,\n    sendChatMessage,\n    resetError\n  } = useGameContext();\n\n  // 处理游戏创建\n  const handleCreateGame = async () => {\n    try {\n      await createGame({\n        total_players: 6,\n        role_distribution: {\n          VILLAGER: 2,\n          WEREWOLF: 2,\n          SEER: 1,\n          WITCH: 1\n        }\n      });\n    } catch (err) {\n      console.error('Failed to create game:', err);\n    }\n  };\n\n  // 处理重试\n  const handleRetry = () => {\n    resetError();\n    window.location.reload();\n  };\n\n  // 处理游戏操作\n  const handleGameAction = action => {\n    console.log('Game action:', action);\n    // 这里可以根据action类型调用不同的方法\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(LoadingContainer, {\n      children: \"\\u6B63\\u5728\\u52A0\\u8F7D\\u6E38\\u620F...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(ErrorContainer, {\n      children: [/*#__PURE__*/_jsxDEV(ErrorTitle, {\n        children: \"\\u8FDE\\u63A5\\u5931\\u8D25\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ErrorMessage, {\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(RetryButton, {\n        onClick: handleRetry,\n        children: \"\\u91CD\\u8BD5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 198,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(AppContainer, {\n    children: [/*#__PURE__*/_jsxDEV(Header, {\n      gameState: gameState,\n      onCreateGame: handleCreateGame,\n      connected: connected\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 210,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(MainContent, {\n      children: [/*#__PURE__*/_jsxDEV(LeftPanel, {\n        children: /*#__PURE__*/_jsxDEV(PlayerList, {\n          players: (gameState === null || gameState === void 0 ? void 0 : gameState.players) || {},\n          currentPhase: (gameState === null || gameState === void 0 ? void 0 : gameState.current_phase) || GamePhase.SETUP,\n          onPlayerSelect: selectPlayer\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CenterPanel, {\n        children: /*#__PURE__*/_jsxDEV(GameBoard, {\n          gameState: gameState,\n          onAction: handleGameAction\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(RightPanel, {\n        children: /*#__PURE__*/_jsxDEV(ChatPanel, {\n          messages: messages,\n          onSendMessage: sendChatMessage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 209,\n    columnNumber: 5\n  }, this);\n};\n\n// 简单的测试组件\n_s(AppContent, \"s9/skTgkD9BsiBBuKHMxcP/OnXE=\", false, function () {\n  return [useGameContext];\n});\n_c10 = AppContent;\nfunction TestApp() {\n  return /*#__PURE__*/_jsxDEV(ThemeProvider, {\n    theme: theme,\n    children: [/*#__PURE__*/_jsxDEV(GlobalStyle, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 247,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '20px',\n        fontFamily: 'Arial, sans-serif',\n        backgroundColor: '#f0f0f0',\n        minHeight: '100vh'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        style: {\n          color: '#333',\n          textAlign: 'center'\n        },\n        children: \"\\uD83D\\uDC3A \\u72FC\\u4EBA\\u6740AI\\u6E38\\u620F\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          maxWidth: '800px',\n          margin: '0 auto',\n          backgroundColor: 'white',\n          padding: '20px',\n          borderRadius: '8px',\n          boxShadow: '0 2px 10px rgba(0,0,0,0.1)'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"\\u6E38\\u620F\\u72B6\\u6001\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u2705 \\u524D\\u7AEF\\u670D\\u52A1\\u5668\\u8FD0\\u884C\\u6B63\\u5E38\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\uD83D\\uDD0C \\u6B63\\u5728\\u8FDE\\u63A5\\u540E\\u7AEF\\u670D\\u52A1\\u5668...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          style: {\n            backgroundColor: '#007bff',\n            color: 'white',\n            border: 'none',\n            padding: '10px 20px',\n            borderRadius: '4px',\n            cursor: 'pointer',\n            fontSize: '16px',\n            marginTop: '20px'\n          },\n          onClick: async () => {\n            console.log('测试后端连接按钮被点击');\n            try {\n              console.log('正在请求 http://localhost:3001/api/health');\n              const response = await fetch('http://localhost:3001/api/health', {\n                method: 'GET',\n                headers: {\n                  'Content-Type': 'application/json'\n                }\n              });\n              console.log('响应状态:', response.status);\n              const data = await response.json();\n              console.log('响应数据:', data);\n              alert('后端连接成功！\\n' + JSON.stringify(data, null, 2));\n            } catch (err) {\n              console.error('请求失败:', err);\n              alert('后端连接失败：' + err.message);\n            }\n          },\n          children: \"\\u6D4B\\u8BD5\\u540E\\u7AEF\\u8FDE\\u63A5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          style: {\n            backgroundColor: '#28a745',\n            color: 'white',\n            border: 'none',\n            padding: '10px 20px',\n            borderRadius: '4px',\n            cursor: 'pointer',\n            fontSize: '16px',\n            marginTop: '20px',\n            marginLeft: '10px'\n          },\n          onClick: async () => {\n            console.log('创建游戏按钮被点击');\n            try {\n              console.log('正在创建游戏...');\n              const response = await fetch('http://localhost:3001/api/games', {\n                method: 'POST',\n                headers: {\n                  'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                  total_players: 6,\n                  role_distribution: {\n                    VILLAGER: 2,\n                    WEREWOLF: 2,\n                    SEER: 1,\n                    WITCH: 1\n                  }\n                })\n              });\n              console.log('创建游戏响应状态:', response.status);\n              const data = await response.json();\n              console.log('创建游戏响应数据:', data);\n              if (data.success) {\n                alert('游戏创建成功！\\n游戏ID: ' + data.data.game_id);\n              } else {\n                alert('创建游戏失败：' + data.error);\n              }\n            } catch (err) {\n              console.error('创建游戏失败:', err);\n              alert('创建游戏失败：' + err.message);\n            }\n          },\n          children: \"\\u521B\\u5EFA\\u6D4B\\u8BD5\\u6E38\\u620F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 248,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 246,\n    columnNumber: 5\n  }, this);\n}\n\n// 主App组件，包装Provider\n_c11 = TestApp;\nfunction App() {\n  // 暂时使用简单的测试组件\n  return /*#__PURE__*/_jsxDEV(TestApp, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 357,\n    columnNumber: 10\n  }, this);\n\n  // 完整版本（暂时注释）\n  /*\n  return (\n    <GameProvider>\n      <ThemeProvider theme={theme}>\n        <GlobalStyle />\n        <AppContent />\n      </ThemeProvider>\n    </GameProvider>\n  );\n  */\n}\n_c12 = App;\nexport default App;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12;\n$RefreshReg$(_c, \"GlobalStyle\");\n$RefreshReg$(_c2, \"AppContainer\");\n$RefreshReg$(_c3, \"MainContent\");\n$RefreshReg$(_c4, \"LeftPanel\");\n$RefreshReg$(_c5, \"CenterPanel\");\n$RefreshReg$(_c6, \"RightPanel\");\n$RefreshReg$(_c7, \"LoadingContainer\");\n$RefreshReg$(_c8, \"ErrorContainer\");\n$RefreshReg$(_c9, \"ErrorTitle\");\n$RefreshReg$(_c0, \"ErrorMessage\");\n$RefreshReg$(_c1, \"RetryButton\");\n$RefreshReg$(_c10, \"AppContent\");\n$RefreshReg$(_c11, \"TestApp\");\n$RefreshReg$(_c12, \"App\");", "map": {"version": 3, "names": ["React", "styled", "ThemeProvider", "createGlobalStyle", "theme", "GamePhase", "GameBoard", "PlayerList", "ChatPanel", "Header", "useGameContext", "jsxDEV", "_jsxDEV", "GlobalStyle", "props", "fonts", "primary", "colors", "background", "text", "lineHeights", "normal", "transitions", "fast", "_c", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "div", "_c2", "MainContent", "main", "spacing", "md", "breakpoints", "lg", "_c3", "LeftPanel", "aside", "backgroundLight", "borderRadius", "shadows", "_c4", "CenterPanel", "section", "_c5", "RightPanel", "_c6", "LoadingContainer", "fontSizes", "xl", "textLight", "_c7", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_c8", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "h1", "danger", "_c9", "ErrorMessage", "p", "_c0", "RetryButton", "button", "textWhite", "fontWeights", "medium", "primaryLight", "_c1", "A<PERSON><PERSON><PERSON>nt", "_s", "gameState", "loading", "error", "connected", "messages", "selectedPlayerId", "createGame", "selectPlayer", "sendChatMessage", "resetError", "handleCreateGame", "total_players", "role_distribution", "VILLAGER", "WEREWOLF", "SEER", "WITCH", "err", "console", "handleRetry", "window", "location", "reload", "handleGameAction", "action", "log", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onCreateGame", "players", "currentPhase", "current_phase", "SETUP", "onPlayerSelect", "onAction", "onSendMessage", "_c10", "TestApp", "style", "padding", "fontFamily", "backgroundColor", "minHeight", "color", "textAlign", "max<PERSON><PERSON><PERSON>", "margin", "boxShadow", "border", "cursor", "fontSize", "marginTop", "response", "fetch", "method", "headers", "status", "data", "json", "alert", "JSON", "stringify", "message", "marginLeft", "body", "success", "game_id", "_c11", "App", "_c12", "$RefreshReg$"], "sources": ["/private/peiwy/work/ai_projects/wolfkill/frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport styled, { ThemeProvider, createGlobalStyle } from 'styled-components';\nimport { theme } from './styles/theme';\nimport { GamePhase } from './types';\nimport GameBoard from './components/GameBoard';\nimport PlayerList from './components/PlayerList';\nimport ChatPanel from './components/ChatPanel';\nimport Header from './components/Header';\nimport { GameProvider, useGameContext } from './contexts/GameContext';\n\n// 全局样式\nconst GlobalStyle = createGlobalStyle`\n  * {\n    margin: 0;\n    padding: 0;\n    box-sizing: border-box;\n  }\n\n  body {\n    font-family: ${props => props.theme.fonts.primary};\n    background-color: ${props => props.theme.colors.background};\n    color: ${props => props.theme.colors.text};\n    line-height: ${props => props.theme.lineHeights.normal};\n  }\n\n  button {\n    font-family: inherit;\n    cursor: pointer;\n    border: none;\n    outline: none;\n    transition: ${props => props.theme.transitions.fast};\n  }\n\n  input, textarea {\n    font-family: inherit;\n    outline: none;\n  }\n`;\n\n// 样式组件\nconst AppContainer = styled.div`\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n`;\n\nconst MainContent = styled.main`\n  flex: 1;\n  display: grid;\n  grid-template-columns: 250px 1fr 300px;\n  grid-template-rows: 1fr;\n  gap: ${props => props.theme.spacing.md};\n  padding: ${props => props.theme.spacing.md};\n  max-width: 1400px;\n  margin: 0 auto;\n  width: 100%;\n\n  @media (max-width: ${props => props.theme.breakpoints.lg}) {\n    grid-template-columns: 1fr;\n    grid-template-rows: auto auto 1fr;\n  }\n`;\n\nconst LeftPanel = styled.aside`\n  background: ${props => props.theme.colors.backgroundLight};\n  border-radius: ${props => props.theme.borderRadius.lg};\n  box-shadow: ${props => props.theme.shadows.md};\n  padding: ${props => props.theme.spacing.lg};\n  overflow-y: auto;\n\n  @media (max-width: ${props => props.theme.breakpoints.lg}) {\n    order: 2;\n  }\n`;\n\nconst CenterPanel = styled.section`\n  background: ${props => props.theme.colors.backgroundLight};\n  border-radius: ${props => props.theme.borderRadius.lg};\n  box-shadow: ${props => props.theme.shadows.md};\n  padding: ${props => props.theme.spacing.lg};\n  overflow-y: auto;\n\n  @media (max-width: ${props => props.theme.breakpoints.lg}) {\n    order: 1;\n  }\n`;\n\nconst RightPanel = styled.aside`\n  background: ${props => props.theme.colors.backgroundLight};\n  border-radius: ${props => props.theme.borderRadius.lg};\n  box-shadow: ${props => props.theme.shadows.md};\n  padding: ${props => props.theme.spacing.lg};\n  overflow-y: auto;\n\n  @media (max-width: ${props => props.theme.breakpoints.lg}) {\n    order: 3;\n  }\n`;\n\nconst LoadingContainer = styled.div`\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100vh;\n  font-size: ${props => props.theme.fontSizes.xl};\n  color: ${props => props.theme.colors.textLight};\n`;\n\nconst ErrorContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  height: 100vh;\n  padding: ${props => props.theme.spacing.xl};\n  text-align: center;\n`;\n\nconst ErrorTitle = styled.h1`\n  color: ${props => props.theme.colors.danger};\n  font-size: ${props => props.theme.fontSizes['2xl']};\n  margin-bottom: ${props => props.theme.spacing.md};\n`;\n\nconst ErrorMessage = styled.p`\n  color: ${props => props.theme.colors.textLight};\n  font-size: ${props => props.theme.fontSizes.lg};\n  margin-bottom: ${props => props.theme.spacing.lg};\n`;\n\nconst RetryButton = styled.button`\n  background: ${props => props.theme.colors.primary};\n  color: ${props => props.theme.colors.textWhite};\n  padding: ${props => props.theme.spacing.md} ${props => props.theme.spacing.lg};\n  border-radius: ${props => props.theme.borderRadius.md};\n  font-size: ${props => props.theme.fontSizes.md};\n  font-weight: ${props => props.theme.fontWeights.medium};\n\n  &:hover {\n    background: ${props => props.theme.colors.primaryLight};\n  }\n`;\n\n// 主应用组件\nconst AppContent: React.FC = () => {\n  const {\n    gameState,\n    loading,\n    error,\n    connected,\n    messages,\n    selectedPlayerId,\n    createGame,\n    selectPlayer,\n    sendChatMessage,\n    resetError\n  } = useGameContext();\n\n  // 处理游戏创建\n  const handleCreateGame = async () => {\n    try {\n      await createGame({\n        total_players: 6,\n        role_distribution: {\n          VILLAGER: 2,\n          WEREWOLF: 2,\n          SEER: 1,\n          WITCH: 1\n        }\n      });\n    } catch (err) {\n      console.error('Failed to create game:', err);\n    }\n  };\n\n  // 处理重试\n  const handleRetry = () => {\n    resetError();\n    window.location.reload();\n  };\n\n  // 处理游戏操作\n  const handleGameAction = (action: any) => {\n    console.log('Game action:', action);\n    // 这里可以根据action类型调用不同的方法\n  };\n\n  if (loading) {\n    return (\n      <LoadingContainer>\n        正在加载游戏...\n      </LoadingContainer>\n    );\n  }\n\n  if (error) {\n    return (\n      <ErrorContainer>\n        <ErrorTitle>连接失败</ErrorTitle>\n        <ErrorMessage>{error}</ErrorMessage>\n        <RetryButton onClick={handleRetry}>\n          重试\n        </RetryButton>\n      </ErrorContainer>\n    );\n  }\n\n  return (\n    <AppContainer>\n      <Header\n        gameState={gameState}\n        onCreateGame={handleCreateGame}\n        connected={connected}\n      />\n\n      <MainContent>\n        <LeftPanel>\n          <PlayerList\n            players={gameState?.players || {}}\n            currentPhase={gameState?.current_phase || GamePhase.SETUP}\n            onPlayerSelect={selectPlayer}\n          />\n        </LeftPanel>\n\n        <CenterPanel>\n          <GameBoard\n            gameState={gameState}\n            onAction={handleGameAction}\n          />\n        </CenterPanel>\n\n        <RightPanel>\n          <ChatPanel\n            messages={messages}\n            onSendMessage={sendChatMessage}\n          />\n        </RightPanel>\n      </MainContent>\n    </AppContainer>\n  );\n}\n\n// 简单的测试组件\nfunction TestApp() {\n  return (\n    <ThemeProvider theme={theme}>\n      <GlobalStyle />\n      <div style={{\n        padding: '20px',\n        fontFamily: 'Arial, sans-serif',\n        backgroundColor: '#f0f0f0',\n        minHeight: '100vh'\n      }}>\n        <h1 style={{ color: '#333', textAlign: 'center' }}>🐺 狼人杀AI游戏</h1>\n        <div style={{\n          maxWidth: '800px',\n          margin: '0 auto',\n          backgroundColor: 'white',\n          padding: '20px',\n          borderRadius: '8px',\n          boxShadow: '0 2px 10px rgba(0,0,0,0.1)'\n        }}>\n          <h2>游戏状态</h2>\n          <p>✅ 前端服务器运行正常</p>\n          <p>🔌 正在连接后端服务器...</p>\n\n          <button\n            style={{\n              backgroundColor: '#007bff',\n              color: 'white',\n              border: 'none',\n              padding: '10px 20px',\n              borderRadius: '4px',\n              cursor: 'pointer',\n              fontSize: '16px',\n              marginTop: '20px'\n            }}\n            onClick={async () => {\n              console.log('测试后端连接按钮被点击');\n              try {\n                console.log('正在请求 http://localhost:3001/api/health');\n                const response = await fetch('http://localhost:3001/api/health', {\n                  method: 'GET',\n                  headers: {\n                    'Content-Type': 'application/json',\n                  },\n                });\n                console.log('响应状态:', response.status);\n                const data = await response.json();\n                console.log('响应数据:', data);\n                alert('后端连接成功！\\n' + JSON.stringify(data, null, 2));\n              } catch (err) {\n                console.error('请求失败:', err);\n                alert('后端连接失败：' + (err as Error).message);\n              }\n            }}\n          >\n            测试后端连接\n          </button>\n\n          <button\n            style={{\n              backgroundColor: '#28a745',\n              color: 'white',\n              border: 'none',\n              padding: '10px 20px',\n              borderRadius: '4px',\n              cursor: 'pointer',\n              fontSize: '16px',\n              marginTop: '20px',\n              marginLeft: '10px'\n            }}\n            onClick={async () => {\n              console.log('创建游戏按钮被点击');\n              try {\n                console.log('正在创建游戏...');\n                const response = await fetch('http://localhost:3001/api/games', {\n                  method: 'POST',\n                  headers: {\n                    'Content-Type': 'application/json'\n                  },\n                  body: JSON.stringify({\n                    total_players: 6,\n                    role_distribution: {\n                      VILLAGER: 2,\n                      WEREWOLF: 2,\n                      SEER: 1,\n                      WITCH: 1\n                    }\n                  })\n                });\n                console.log('创建游戏响应状态:', response.status);\n                const data = await response.json();\n                console.log('创建游戏响应数据:', data);\n                if (data.success) {\n                  alert('游戏创建成功！\\n游戏ID: ' + data.data.game_id);\n                } else {\n                  alert('创建游戏失败：' + data.error);\n                }\n              } catch (err) {\n                console.error('创建游戏失败:', err);\n                alert('创建游戏失败：' + (err as Error).message);\n              }\n            }}\n          >\n            创建测试游戏\n          </button>\n        </div>\n      </div>\n    </ThemeProvider>\n  );\n}\n\n// 主App组件，包装Provider\nfunction App() {\n  // 暂时使用简单的测试组件\n  return <TestApp />;\n\n  // 完整版本（暂时注释）\n  /*\n  return (\n    <GameProvider>\n      <ThemeProvider theme={theme}>\n        <GlobalStyle />\n        <AppContent />\n      </ThemeProvider>\n    </GameProvider>\n  );\n  */\n}\n\nexport default App;"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,IAAIC,aAAa,EAAEC,iBAAiB,QAAQ,mBAAmB;AAC5E,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,SAAS,QAAQ,SAAS;AACnC,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,MAAM,MAAM,qBAAqB;AACxC,SAAuBC,cAAc,QAAQ,wBAAwB;;AAErE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,WAAW,GAAGV,iBAAiB;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmBW,KAAK,IAAIA,KAAK,CAACV,KAAK,CAACW,KAAK,CAACC,OAAO;AACrD,wBAAwBF,KAAK,IAAIA,KAAK,CAACV,KAAK,CAACa,MAAM,CAACC,UAAU;AAC9D,aAAaJ,KAAK,IAAIA,KAAK,CAACV,KAAK,CAACa,MAAM,CAACE,IAAI;AAC7C,mBAAmBL,KAAK,IAAIA,KAAK,CAACV,KAAK,CAACgB,WAAW,CAACC,MAAM;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkBP,KAAK,IAAIA,KAAK,CAACV,KAAK,CAACkB,WAAW,CAACC,IAAI;AACvD;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAC,EAAA,GA5BMX,WAAW;AA6BjB,MAAMY,YAAY,GAAGxB,MAAM,CAACyB,GAAG;AAC/B;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,YAAY;AAMlB,MAAMG,WAAW,GAAG3B,MAAM,CAAC4B,IAAI;AAC/B;AACA;AACA;AACA;AACA,SAASf,KAAK,IAAIA,KAAK,CAACV,KAAK,CAAC0B,OAAO,CAACC,EAAE;AACxC,aAAajB,KAAK,IAAIA,KAAK,CAACV,KAAK,CAAC0B,OAAO,CAACC,EAAE;AAC5C;AACA;AACA;AACA;AACA,uBAAuBjB,KAAK,IAAIA,KAAK,CAACV,KAAK,CAAC4B,WAAW,CAACC,EAAE;AAC1D;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAfIN,WAAW;AAiBjB,MAAMO,SAAS,GAAGlC,MAAM,CAACmC,KAAK;AAC9B,gBAAgBtB,KAAK,IAAIA,KAAK,CAACV,KAAK,CAACa,MAAM,CAACoB,eAAe;AAC3D,mBAAmBvB,KAAK,IAAIA,KAAK,CAACV,KAAK,CAACkC,YAAY,CAACL,EAAE;AACvD,gBAAgBnB,KAAK,IAAIA,KAAK,CAACV,KAAK,CAACmC,OAAO,CAACR,EAAE;AAC/C,aAAajB,KAAK,IAAIA,KAAK,CAACV,KAAK,CAAC0B,OAAO,CAACG,EAAE;AAC5C;AACA;AACA,uBAAuBnB,KAAK,IAAIA,KAAK,CAACV,KAAK,CAAC4B,WAAW,CAACC,EAAE;AAC1D;AACA;AACA,CAAC;AAACO,GAAA,GAVIL,SAAS;AAYf,MAAMM,WAAW,GAAGxC,MAAM,CAACyC,OAAO;AAClC,gBAAgB5B,KAAK,IAAIA,KAAK,CAACV,KAAK,CAACa,MAAM,CAACoB,eAAe;AAC3D,mBAAmBvB,KAAK,IAAIA,KAAK,CAACV,KAAK,CAACkC,YAAY,CAACL,EAAE;AACvD,gBAAgBnB,KAAK,IAAIA,KAAK,CAACV,KAAK,CAACmC,OAAO,CAACR,EAAE;AAC/C,aAAajB,KAAK,IAAIA,KAAK,CAACV,KAAK,CAAC0B,OAAO,CAACG,EAAE;AAC5C;AACA;AACA,uBAAuBnB,KAAK,IAAIA,KAAK,CAACV,KAAK,CAAC4B,WAAW,CAACC,EAAE;AAC1D;AACA;AACA,CAAC;AAACU,GAAA,GAVIF,WAAW;AAYjB,MAAMG,UAAU,GAAG3C,MAAM,CAACmC,KAAK;AAC/B,gBAAgBtB,KAAK,IAAIA,KAAK,CAACV,KAAK,CAACa,MAAM,CAACoB,eAAe;AAC3D,mBAAmBvB,KAAK,IAAIA,KAAK,CAACV,KAAK,CAACkC,YAAY,CAACL,EAAE;AACvD,gBAAgBnB,KAAK,IAAIA,KAAK,CAACV,KAAK,CAACmC,OAAO,CAACR,EAAE;AAC/C,aAAajB,KAAK,IAAIA,KAAK,CAACV,KAAK,CAAC0B,OAAO,CAACG,EAAE;AAC5C;AACA;AACA,uBAAuBnB,KAAK,IAAIA,KAAK,CAACV,KAAK,CAAC4B,WAAW,CAACC,EAAE;AAC1D;AACA;AACA,CAAC;AAACY,GAAA,GAVID,UAAU;AAYhB,MAAME,gBAAgB,GAAG7C,MAAM,CAACyB,GAAG;AACnC;AACA;AACA;AACA;AACA,eAAeZ,KAAK,IAAIA,KAAK,CAACV,KAAK,CAAC2C,SAAS,CAACC,EAAE;AAChD,WAAWlC,KAAK,IAAIA,KAAK,CAACV,KAAK,CAACa,MAAM,CAACgC,SAAS;AAChD,CAAC;AAACC,GAAA,GAPIJ,gBAAgB;AAStB,MAAMK,cAAc,GAAGlD,MAAM,CAACyB,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA,aAAaZ,KAAK,IAAIA,KAAK,CAACV,KAAK,CAAC0B,OAAO,CAACkB,EAAE;AAC5C;AACA,CAAC;AAACI,GAAA,GARID,cAAc;AAUpB,MAAME,UAAU,GAAGpD,MAAM,CAACqD,EAAE;AAC5B,WAAWxC,KAAK,IAAIA,KAAK,CAACV,KAAK,CAACa,MAAM,CAACsC,MAAM;AAC7C,eAAezC,KAAK,IAAIA,KAAK,CAACV,KAAK,CAAC2C,SAAS,CAAC,KAAK,CAAC;AACpD,mBAAmBjC,KAAK,IAAIA,KAAK,CAACV,KAAK,CAAC0B,OAAO,CAACC,EAAE;AAClD,CAAC;AAACyB,GAAA,GAJIH,UAAU;AAMhB,MAAMI,YAAY,GAAGxD,MAAM,CAACyD,CAAC;AAC7B,WAAW5C,KAAK,IAAIA,KAAK,CAACV,KAAK,CAACa,MAAM,CAACgC,SAAS;AAChD,eAAenC,KAAK,IAAIA,KAAK,CAACV,KAAK,CAAC2C,SAAS,CAACd,EAAE;AAChD,mBAAmBnB,KAAK,IAAIA,KAAK,CAACV,KAAK,CAAC0B,OAAO,CAACG,EAAE;AAClD,CAAC;AAAC0B,GAAA,GAJIF,YAAY;AAMlB,MAAMG,WAAW,GAAG3D,MAAM,CAAC4D,MAAM;AACjC,gBAAgB/C,KAAK,IAAIA,KAAK,CAACV,KAAK,CAACa,MAAM,CAACD,OAAO;AACnD,WAAWF,KAAK,IAAIA,KAAK,CAACV,KAAK,CAACa,MAAM,CAAC6C,SAAS;AAChD,aAAahD,KAAK,IAAIA,KAAK,CAACV,KAAK,CAAC0B,OAAO,CAACC,EAAE,IAAIjB,KAAK,IAAIA,KAAK,CAACV,KAAK,CAAC0B,OAAO,CAACG,EAAE;AAC/E,mBAAmBnB,KAAK,IAAIA,KAAK,CAACV,KAAK,CAACkC,YAAY,CAACP,EAAE;AACvD,eAAejB,KAAK,IAAIA,KAAK,CAACV,KAAK,CAAC2C,SAAS,CAAChB,EAAE;AAChD,iBAAiBjB,KAAK,IAAIA,KAAK,CAACV,KAAK,CAAC2D,WAAW,CAACC,MAAM;AACxD;AACA;AACA,kBAAkBlD,KAAK,IAAIA,KAAK,CAACV,KAAK,CAACa,MAAM,CAACgD,YAAY;AAC1D;AACA,CAAC;;AAED;AAAAC,GAAA,GAbMN,WAAW;AAcjB,MAAMO,UAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAM;IACJC,SAAS;IACTC,OAAO;IACPC,KAAK;IACLC,SAAS;IACTC,QAAQ;IACRC,gBAAgB;IAChBC,UAAU;IACVC,YAAY;IACZC,eAAe;IACfC;EACF,CAAC,GAAGpE,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAMqE,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAMJ,UAAU,CAAC;QACfK,aAAa,EAAE,CAAC;QAChBC,iBAAiB,EAAE;UACjBC,QAAQ,EAAE,CAAC;UACXC,QAAQ,EAAE,CAAC;UACXC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE;QACT;MACF,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZC,OAAO,CAAChB,KAAK,CAAC,wBAAwB,EAAEe,GAAG,CAAC;IAC9C;EACF,CAAC;;EAED;EACA,MAAME,WAAW,GAAGA,CAAA,KAAM;IACxBV,UAAU,CAAC,CAAC;IACZW,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAIC,MAAW,IAAK;IACxCN,OAAO,CAACO,GAAG,CAAC,cAAc,EAAED,MAAM,CAAC;IACnC;EACF,CAAC;EAED,IAAIvB,OAAO,EAAE;IACX,oBACE1D,OAAA,CAACkC,gBAAgB;MAAAiD,QAAA,EAAC;IAElB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAkB,CAAC;EAEvB;EAEA,IAAI5B,KAAK,EAAE;IACT,oBACE3D,OAAA,CAACuC,cAAc;MAAA4C,QAAA,gBACbnF,OAAA,CAACyC,UAAU;QAAA0C,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC7BvF,OAAA,CAAC6C,YAAY;QAAAsC,QAAA,EAAExB;MAAK;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAe,CAAC,eACpCvF,OAAA,CAACgD,WAAW;QAACwC,OAAO,EAAEZ,WAAY;QAAAO,QAAA,EAAC;MAEnC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAErB;EAEA,oBACEvF,OAAA,CAACa,YAAY;IAAAsE,QAAA,gBACXnF,OAAA,CAACH,MAAM;MACL4D,SAAS,EAAEA,SAAU;MACrBgC,YAAY,EAAEtB,gBAAiB;MAC/BP,SAAS,EAAEA;IAAU;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CAAC,eAEFvF,OAAA,CAACgB,WAAW;MAAAmE,QAAA,gBACVnF,OAAA,CAACuB,SAAS;QAAA4D,QAAA,eACRnF,OAAA,CAACL,UAAU;UACT+F,OAAO,EAAE,CAAAjC,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEiC,OAAO,KAAI,CAAC,CAAE;UAClCC,YAAY,EAAE,CAAAlC,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEmC,aAAa,KAAInG,SAAS,CAACoG,KAAM;UAC1DC,cAAc,EAAE9B;QAAa;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAEZvF,OAAA,CAAC6B,WAAW;QAAAsD,QAAA,eACVnF,OAAA,CAACN,SAAS;UACR+D,SAAS,EAAEA,SAAU;UACrBsC,QAAQ,EAAEf;QAAiB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,eAEdvF,OAAA,CAACgC,UAAU;QAAAmD,QAAA,eACTnF,OAAA,CAACJ,SAAS;UACRiE,QAAQ,EAAEA,QAAS;UACnBmC,aAAa,EAAE/B;QAAgB;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEnB,CAAC;;AAED;AAAA/B,EAAA,CAlGMD,UAAoB;EAAA,QAYpBzD,cAAc;AAAA;AAAAmG,IAAA,GAZd1C,UAAoB;AAmG1B,SAAS2C,OAAOA,CAAA,EAAG;EACjB,oBACElG,OAAA,CAACV,aAAa;IAACE,KAAK,EAAEA,KAAM;IAAA2F,QAAA,gBAC1BnF,OAAA,CAACC,WAAW;MAAAmF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACfvF,OAAA;MAAKmG,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,mBAAmB;QAC/BC,eAAe,EAAE,SAAS;QAC1BC,SAAS,EAAE;MACb,CAAE;MAAApB,QAAA,gBACAnF,OAAA;QAAImG,KAAK,EAAE;UAAEK,KAAK,EAAE,MAAM;UAAEC,SAAS,EAAE;QAAS,CAAE;QAAAtB,QAAA,EAAC;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAClEvF,OAAA;QAAKmG,KAAK,EAAE;UACVO,QAAQ,EAAE,OAAO;UACjBC,MAAM,EAAE,QAAQ;UAChBL,eAAe,EAAE,OAAO;UACxBF,OAAO,EAAE,MAAM;UACf1E,YAAY,EAAE,KAAK;UACnBkF,SAAS,EAAE;QACb,CAAE;QAAAzB,QAAA,gBACAnF,OAAA;UAAAmF,QAAA,EAAI;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACbvF,OAAA;UAAAmF,QAAA,EAAG;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAClBvF,OAAA;UAAAmF,QAAA,EAAG;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAEtBvF,OAAA;UACEmG,KAAK,EAAE;YACLG,eAAe,EAAE,SAAS;YAC1BE,KAAK,EAAE,OAAO;YACdK,MAAM,EAAE,MAAM;YACdT,OAAO,EAAE,WAAW;YACpB1E,YAAY,EAAE,KAAK;YACnBoF,MAAM,EAAE,SAAS;YACjBC,QAAQ,EAAE,MAAM;YAChBC,SAAS,EAAE;UACb,CAAE;UACFxB,OAAO,EAAE,MAAAA,CAAA,KAAY;YACnBb,OAAO,CAACO,GAAG,CAAC,aAAa,CAAC;YAC1B,IAAI;cACFP,OAAO,CAACO,GAAG,CAAC,uCAAuC,CAAC;cACpD,MAAM+B,QAAQ,GAAG,MAAMC,KAAK,CAAC,kCAAkC,EAAE;gBAC/DC,MAAM,EAAE,KAAK;gBACbC,OAAO,EAAE;kBACP,cAAc,EAAE;gBAClB;cACF,CAAC,CAAC;cACFzC,OAAO,CAACO,GAAG,CAAC,OAAO,EAAE+B,QAAQ,CAACI,MAAM,CAAC;cACrC,MAAMC,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;cAClC5C,OAAO,CAACO,GAAG,CAAC,OAAO,EAAEoC,IAAI,CAAC;cAC1BE,KAAK,CAAC,WAAW,GAAGC,IAAI,CAACC,SAAS,CAACJ,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;YACpD,CAAC,CAAC,OAAO5C,GAAG,EAAE;cACZC,OAAO,CAAChB,KAAK,CAAC,OAAO,EAAEe,GAAG,CAAC;cAC3B8C,KAAK,CAAC,SAAS,GAAI9C,GAAG,CAAWiD,OAAO,CAAC;YAC3C;UACF,CAAE;UAAAxC,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETvF,OAAA;UACEmG,KAAK,EAAE;YACLG,eAAe,EAAE,SAAS;YAC1BE,KAAK,EAAE,OAAO;YACdK,MAAM,EAAE,MAAM;YACdT,OAAO,EAAE,WAAW;YACpB1E,YAAY,EAAE,KAAK;YACnBoF,MAAM,EAAE,SAAS;YACjBC,QAAQ,EAAE,MAAM;YAChBC,SAAS,EAAE,MAAM;YACjBY,UAAU,EAAE;UACd,CAAE;UACFpC,OAAO,EAAE,MAAAA,CAAA,KAAY;YACnBb,OAAO,CAACO,GAAG,CAAC,WAAW,CAAC;YACxB,IAAI;cACFP,OAAO,CAACO,GAAG,CAAC,WAAW,CAAC;cACxB,MAAM+B,QAAQ,GAAG,MAAMC,KAAK,CAAC,iCAAiC,EAAE;gBAC9DC,MAAM,EAAE,MAAM;gBACdC,OAAO,EAAE;kBACP,cAAc,EAAE;gBAClB,CAAC;gBACDS,IAAI,EAAEJ,IAAI,CAACC,SAAS,CAAC;kBACnBtD,aAAa,EAAE,CAAC;kBAChBC,iBAAiB,EAAE;oBACjBC,QAAQ,EAAE,CAAC;oBACXC,QAAQ,EAAE,CAAC;oBACXC,IAAI,EAAE,CAAC;oBACPC,KAAK,EAAE;kBACT;gBACF,CAAC;cACH,CAAC,CAAC;cACFE,OAAO,CAACO,GAAG,CAAC,WAAW,EAAE+B,QAAQ,CAACI,MAAM,CAAC;cACzC,MAAMC,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;cAClC5C,OAAO,CAACO,GAAG,CAAC,WAAW,EAAEoC,IAAI,CAAC;cAC9B,IAAIA,IAAI,CAACQ,OAAO,EAAE;gBAChBN,KAAK,CAAC,iBAAiB,GAAGF,IAAI,CAACA,IAAI,CAACS,OAAO,CAAC;cAC9C,CAAC,MAAM;gBACLP,KAAK,CAAC,SAAS,GAAGF,IAAI,CAAC3D,KAAK,CAAC;cAC/B;YACF,CAAC,CAAC,OAAOe,GAAG,EAAE;cACZC,OAAO,CAAChB,KAAK,CAAC,SAAS,EAAEe,GAAG,CAAC;cAC7B8C,KAAK,CAAC,SAAS,GAAI9C,GAAG,CAAWiD,OAAO,CAAC;YAC3C;UACF,CAAE;UAAAxC,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB;;AAEA;AAAAyC,IAAA,GA9GS9B,OAAO;AA+GhB,SAAS+B,GAAGA,CAAA,EAAG;EACb;EACA,oBAAOjI,OAAA,CAACkG,OAAO;IAAAd,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;;EAElB;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAC2C,IAAA,GAfQD,GAAG;AAiBZ,eAAeA,GAAG;AAAC,IAAArH,EAAA,EAAAG,GAAA,EAAAO,GAAA,EAAAM,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAK,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAG,GAAA,EAAAO,GAAA,EAAA2C,IAAA,EAAA+B,IAAA,EAAAE,IAAA;AAAAC,YAAA,CAAAvH,EAAA;AAAAuH,YAAA,CAAApH,GAAA;AAAAoH,YAAA,CAAA7G,GAAA;AAAA6G,YAAA,CAAAvG,GAAA;AAAAuG,YAAA,CAAApG,GAAA;AAAAoG,YAAA,CAAAlG,GAAA;AAAAkG,YAAA,CAAA7F,GAAA;AAAA6F,YAAA,CAAA3F,GAAA;AAAA2F,YAAA,CAAAvF,GAAA;AAAAuF,YAAA,CAAApF,GAAA;AAAAoF,YAAA,CAAA7E,GAAA;AAAA6E,YAAA,CAAAlC,IAAA;AAAAkC,YAAA,CAAAH,IAAA;AAAAG,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}