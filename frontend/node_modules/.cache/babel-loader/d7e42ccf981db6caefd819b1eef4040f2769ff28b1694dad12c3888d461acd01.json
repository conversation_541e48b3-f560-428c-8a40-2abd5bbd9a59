{"ast": null, "code": "const withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nconst isView = obj => {\n  return typeof ArrayBuffer.isView === \"function\" ? ArrayBuffer.isView(obj) : obj.buffer instanceof ArrayBuffer;\n};\nconst toString = Object.prototype.toString;\nconst withNativeBlob = typeof Blob === \"function\" || typeof Blob !== \"undefined\" && toString.call(Blob) === \"[object BlobConstructor]\";\nconst withNativeFile = typeof File === \"function\" || typeof File !== \"undefined\" && toString.call(File) === \"[object FileConstructor]\";\n/**\n * Returns true if obj is a Buffer, an ArrayBuffer, a Blob or a File.\n *\n * @private\n */\nexport function isBinary(obj) {\n  return withNativeArrayBuffer && (obj instanceof ArrayBuffer || isView(obj)) || withNativeBlob && obj instanceof Blob || withNativeFile && obj instanceof File;\n}\nexport function hasBinary(obj, toJSON) {\n  if (!obj || typeof obj !== \"object\") {\n    return false;\n  }\n  if (Array.isArray(obj)) {\n    for (let i = 0, l = obj.length; i < l; i++) {\n      if (hasBinary(obj[i])) {\n        return true;\n      }\n    }\n    return false;\n  }\n  if (isBinary(obj)) {\n    return true;\n  }\n  if (obj.toJSON && typeof obj.toJSON === \"function\" && arguments.length === 1) {\n    return hasBinary(obj.toJSON(), true);\n  }\n  for (const key in obj) {\n    if (Object.prototype.hasOwnProperty.call(obj, key) && hasBinary(obj[key])) {\n      return true;\n    }\n  }\n  return false;\n}", "map": {"version": 3, "names": ["with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "obj", "buffer", "toString", "Object", "prototype", "withNativeBlob", "Blob", "call", "withNativeFile", "File", "isBinary", "hasBinary", "toJSON", "Array", "isArray", "i", "l", "length", "arguments", "key", "hasOwnProperty"], "sources": ["/private/peiwy/work/ai_projects/wolfkill/frontend/node_modules/socket.io-parser/build/esm/is-binary.js"], "sourcesContent": ["const withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nconst isView = (obj) => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj.buffer instanceof ArrayBuffer;\n};\nconst toString = Object.prototype.toString;\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeFile = typeof File === \"function\" ||\n    (typeof File !== \"undefined\" &&\n        toString.call(File) === \"[object FileConstructor]\");\n/**\n * Returns true if obj is a Buffer, an ArrayBuffer, a Blob or a File.\n *\n * @private\n */\nexport function isBinary(obj) {\n    return ((withNativeArrayBuffer && (obj instanceof ArrayBuffer || isView(obj))) ||\n        (withNativeBlob && obj instanceof Blob) ||\n        (withNativeFile && obj instanceof File));\n}\nexport function hasBinary(obj, toJSON) {\n    if (!obj || typeof obj !== \"object\") {\n        return false;\n    }\n    if (Array.isArray(obj)) {\n        for (let i = 0, l = obj.length; i < l; i++) {\n            if (hasBinary(obj[i])) {\n                return true;\n            }\n        }\n        return false;\n    }\n    if (isBinary(obj)) {\n        return true;\n    }\n    if (obj.toJSON &&\n        typeof obj.toJSON === \"function\" &&\n        arguments.length === 1) {\n        return hasBinary(obj.toJSON(), true);\n    }\n    for (const key in obj) {\n        if (Object.prototype.hasOwnProperty.call(obj, key) && hasBinary(obj[key])) {\n            return true;\n        }\n    }\n    return false;\n}\n"], "mappings": "AAAA,MAAMA,qBAAqB,GAAG,OAAOC,WAAW,KAAK,UAAU;AAC/D,MAAMC,MAAM,GAAIC,GAAG,IAAK;EACpB,OAAO,OAAOF,WAAW,CAACC,MAAM,KAAK,UAAU,GACzCD,WAAW,CAACC,MAAM,CAACC,GAAG,CAAC,GACvBA,GAAG,CAACC,MAAM,YAAYH,WAAW;AAC3C,CAAC;AACD,MAAMI,QAAQ,GAAGC,MAAM,CAACC,SAAS,CAACF,QAAQ;AAC1C,MAAMG,cAAc,GAAG,OAAOC,IAAI,KAAK,UAAU,IAC5C,OAAOA,IAAI,KAAK,WAAW,IACxBJ,QAAQ,CAACK,IAAI,CAACD,IAAI,CAAC,KAAK,0BAA2B;AAC3D,MAAME,cAAc,GAAG,OAAOC,IAAI,KAAK,UAAU,IAC5C,OAAOA,IAAI,KAAK,WAAW,IACxBP,QAAQ,CAACK,IAAI,CAACE,IAAI,CAAC,KAAK,0BAA2B;AAC3D;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,QAAQA,CAACV,GAAG,EAAE;EAC1B,OAASH,qBAAqB,KAAKG,GAAG,YAAYF,WAAW,IAAIC,MAAM,CAACC,GAAG,CAAC,CAAC,IACxEK,cAAc,IAAIL,GAAG,YAAYM,IAAK,IACtCE,cAAc,IAAIR,GAAG,YAAYS,IAAK;AAC/C;AACA,OAAO,SAASE,SAASA,CAACX,GAAG,EAAEY,MAAM,EAAE;EACnC,IAAI,CAACZ,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IACjC,OAAO,KAAK;EAChB;EACA,IAAIa,KAAK,CAACC,OAAO,CAACd,GAAG,CAAC,EAAE;IACpB,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGhB,GAAG,CAACiB,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MACxC,IAAIJ,SAAS,CAACX,GAAG,CAACe,CAAC,CAAC,CAAC,EAAE;QACnB,OAAO,IAAI;MACf;IACJ;IACA,OAAO,KAAK;EAChB;EACA,IAAIL,QAAQ,CAACV,GAAG,CAAC,EAAE;IACf,OAAO,IAAI;EACf;EACA,IAAIA,GAAG,CAACY,MAAM,IACV,OAAOZ,GAAG,CAACY,MAAM,KAAK,UAAU,IAChCM,SAAS,CAACD,MAAM,KAAK,CAAC,EAAE;IACxB,OAAON,SAAS,CAACX,GAAG,CAACY,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC;EACxC;EACA,KAAK,MAAMO,GAAG,IAAInB,GAAG,EAAE;IACnB,IAAIG,MAAM,CAACC,SAAS,CAACgB,cAAc,CAACb,IAAI,CAACP,GAAG,EAAEmB,GAAG,CAAC,IAAIR,SAAS,CAACX,GAAG,CAACmB,GAAG,CAAC,CAAC,EAAE;MACvE,OAAO,IAAI;IACf;EACJ;EACA,OAAO,KAAK;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}