{"ast": null, "code": "var unitlessKeys = {\n  animationIterationCount: 1,\n  aspectRatio: 1,\n  borderImageOutset: 1,\n  borderImageSlice: 1,\n  borderImageWidth: 1,\n  boxFlex: 1,\n  boxFlexGroup: 1,\n  boxOrdinalGroup: 1,\n  columnCount: 1,\n  columns: 1,\n  flex: 1,\n  flexGrow: 1,\n  flexPositive: 1,\n  flexShrink: 1,\n  flexNegative: 1,\n  flexOrder: 1,\n  gridRow: 1,\n  gridRowEnd: 1,\n  gridRowSpan: 1,\n  gridRowStart: 1,\n  gridColumn: 1,\n  gridColumnEnd: 1,\n  gridColumnSpan: 1,\n  gridColumnStart: 1,\n  msGridRow: 1,\n  msGridRowSpan: 1,\n  msGridColumn: 1,\n  msGridColumnSpan: 1,\n  fontWeight: 1,\n  lineHeight: 1,\n  opacity: 1,\n  order: 1,\n  orphans: 1,\n  tabSize: 1,\n  widows: 1,\n  zIndex: 1,\n  zoom: 1,\n  WebkitLineClamp: 1,\n  // SVG-related properties\n  fillOpacity: 1,\n  floodOpacity: 1,\n  stopOpacity: 1,\n  strokeDasharray: 1,\n  strokeDashoffset: 1,\n  strokeMiterlimit: 1,\n  strokeOpacity: 1,\n  strokeWidth: 1\n};\nexport { unitlessKeys as default };", "map": {"version": 3, "names": ["unitlessKeys", "animationIterationCount", "aspectRatio", "borderImageOutset", "borderImageSlice", "borderImageWidth", "boxFlex", "boxFlexGroup", "boxOrdinalGroup", "columnCount", "columns", "flex", "flexGrow", "flexPositive", "flexShrink", "flexNegative", "flexOrder", "gridRow", "gridRowEnd", "gridRowSpan", "gridRowStart", "gridColumn", "gridColumnEnd", "gridColumnSpan", "gridColumnStart", "msGridRow", "msGridRowSpan", "msGridColumn", "msGridColumnSpan", "fontWeight", "lineHeight", "opacity", "order", "orphans", "tabSize", "widows", "zIndex", "zoom", "WebkitLineClamp", "fillOpacity", "floodOpacity", "stopOpacity", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "strokeDashoffset", "strokeMiterlimit", "strokeOpacity", "strokeWidth", "default"], "sources": ["/private/peiwy/work/ai_projects/wolfkill/frontend/node_modules/@emotion/unitless/dist/emotion-unitless.esm.js"], "sourcesContent": ["var unitlessKeys = {\n  animationIterationCount: 1,\n  aspectRatio: 1,\n  borderImageOutset: 1,\n  borderImageSlice: 1,\n  borderImageWidth: 1,\n  boxFlex: 1,\n  boxFlexGroup: 1,\n  boxOrdinalGroup: 1,\n  columnCount: 1,\n  columns: 1,\n  flex: 1,\n  flexGrow: 1,\n  flexPositive: 1,\n  flexShrink: 1,\n  flexNegative: 1,\n  flexOrder: 1,\n  gridRow: 1,\n  gridRowEnd: 1,\n  gridRowSpan: 1,\n  gridRowStart: 1,\n  gridColumn: 1,\n  gridColumnEnd: 1,\n  gridColumnSpan: 1,\n  gridColumnStart: 1,\n  msGridRow: 1,\n  msGridRowSpan: 1,\n  msGridColumn: 1,\n  msGridColumnSpan: 1,\n  fontWeight: 1,\n  lineHeight: 1,\n  opacity: 1,\n  order: 1,\n  orphans: 1,\n  tabSize: 1,\n  widows: 1,\n  zIndex: 1,\n  zoom: 1,\n  WebkitLineClamp: 1,\n  // SVG-related properties\n  fillOpacity: 1,\n  floodOpacity: 1,\n  stopOpacity: 1,\n  strokeDasharray: 1,\n  strokeDashoffset: 1,\n  strokeMiterlimit: 1,\n  strokeOpacity: 1,\n  strokeWidth: 1\n};\n\nexport { unitlessKeys as default };\n"], "mappings": "AAAA,IAAIA,YAAY,GAAG;EACjBC,uBAAuB,EAAE,CAAC;EAC1BC,WAAW,EAAE,CAAC;EACdC,iBAAiB,EAAE,CAAC;EACpBC,gBAAgB,EAAE,CAAC;EACnBC,gBAAgB,EAAE,CAAC;EACnBC,OAAO,EAAE,CAAC;EACVC,YAAY,EAAE,CAAC;EACfC,eAAe,EAAE,CAAC;EAClBC,WAAW,EAAE,CAAC;EACdC,OAAO,EAAE,CAAC;EACVC,IAAI,EAAE,CAAC;EACPC,QAAQ,EAAE,CAAC;EACXC,YAAY,EAAE,CAAC;EACfC,UAAU,EAAE,CAAC;EACbC,YAAY,EAAE,CAAC;EACfC,SAAS,EAAE,CAAC;EACZC,OAAO,EAAE,CAAC;EACVC,UAAU,EAAE,CAAC;EACbC,WAAW,EAAE,CAAC;EACdC,YAAY,EAAE,CAAC;EACfC,UAAU,EAAE,CAAC;EACbC,aAAa,EAAE,CAAC;EAChBC,cAAc,EAAE,CAAC;EACjBC,eAAe,EAAE,CAAC;EAClBC,SAAS,EAAE,CAAC;EACZC,aAAa,EAAE,CAAC;EAChBC,YAAY,EAAE,CAAC;EACfC,gBAAgB,EAAE,CAAC;EACnBC,UAAU,EAAE,CAAC;EACbC,UAAU,EAAE,CAAC;EACbC,OAAO,EAAE,CAAC;EACVC,KAAK,EAAE,CAAC;EACRC,OAAO,EAAE,CAAC;EACVC,OAAO,EAAE,CAAC;EACVC,MAAM,EAAE,CAAC;EACTC,MAAM,EAAE,CAAC;EACTC,IAAI,EAAE,CAAC;EACPC,eAAe,EAAE,CAAC;EAClB;EACAC,WAAW,EAAE,CAAC;EACdC,YAAY,EAAE,CAAC;EACfC,WAAW,EAAE,CAAC;EACdC,eAAe,EAAE,CAAC;EAClBC,gBAAgB,EAAE,CAAC;EACnBC,gBAAgB,EAAE,CAAC;EACnBC,aAAa,EAAE,CAAC;EAChBC,WAAW,EAAE;AACf,CAAC;AAED,SAAS9C,YAAY,IAAI+C,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}