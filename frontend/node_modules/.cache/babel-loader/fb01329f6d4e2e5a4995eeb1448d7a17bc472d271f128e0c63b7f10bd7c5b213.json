{"ast": null, "code": "var _jsxFileName = \"/private/peiwy/work/ai_projects/wolfkill/frontend/src/contexts/GameContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useReducer, useEffect } from 'react';\nimport { useWebSocket } from '../hooks/useWebSocket';\nimport { gameApi } from '../services/api';\n\n// 游戏状态类型\n\n// 动作类型\n\n// Context类型\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// 初始状态\nconst initialState = {\n  gameState: null,\n  loading: false,\n  error: null,\n  connected: false,\n  messages: [],\n  selectedPlayerId: null\n};\n\n// Reducer\nconst gameReducer = (state, action) => {\n  switch (action.type) {\n    case 'SET_LOADING':\n      return {\n        ...state,\n        loading: action.payload\n      };\n    case 'SET_ERROR':\n      return {\n        ...state,\n        error: action.payload,\n        loading: false\n      };\n    case 'SET_GAME_STATE':\n      return {\n        ...state,\n        gameState: action.payload\n      };\n    case 'SET_CONNECTED':\n      return {\n        ...state,\n        connected: action.payload\n      };\n    case 'ADD_MESSAGE':\n      return {\n        ...state,\n        messages: [...state.messages, action.payload]\n      };\n    case 'SET_MESSAGES':\n      return {\n        ...state,\n        messages: action.payload\n      };\n    case 'SET_SELECTED_PLAYER':\n      return {\n        ...state,\n        selectedPlayerId: action.payload\n      };\n    case 'RESET_STATE':\n      return {\n        ...initialState,\n        connected: state.connected\n      };\n    default:\n      return state;\n  }\n};\n\n// 创建Context\nconst GameContext = /*#__PURE__*/createContext(undefined);\n\n// Provider组件\n\nexport const GameProvider = ({\n  children\n}) => {\n  _s();\n  var _state$gameState2;\n  const [state, dispatch] = useReducer(gameReducer, initialState);\n  const {\n    connected,\n    sendMessage,\n    lastMessage\n  } = useWebSocket();\n\n  // 更新连接状态\n  useEffect(() => {\n    dispatch({\n      type: 'SET_CONNECTED',\n      payload: connected\n    });\n  }, [connected]);\n\n  // 处理WebSocket消息\n  useEffect(() => {\n    if (!lastMessage) return;\n    switch (lastMessage.type) {\n      case 'game_state_update':\n        dispatch({\n          type: 'SET_GAME_STATE',\n          payload: lastMessage.data\n        });\n        break;\n      case 'chat_message':\n        const chatMessage = {\n          id: Date.now().toString(),\n          sender: lastMessage.data.sender || '系统',\n          message: lastMessage.data.message,\n          timestamp: lastMessage.timestamp,\n          type: lastMessage.data.type || 'system'\n        };\n        dispatch({\n          type: 'ADD_MESSAGE',\n          payload: chatMessage\n        });\n        break;\n      case 'phase_change':\n        addSystemMessage(`游戏阶段变更: ${lastMessage.data.phase}`);\n        break;\n      case 'game_over':\n        addSystemMessage(`游戏结束: ${lastMessage.data.result}`);\n        break;\n      default:\n        console.log('Unhandled WebSocket message:', lastMessage);\n    }\n  }, [lastMessage]);\n\n  // 创建游戏\n  const createGame = async config => {\n    dispatch({\n      type: 'SET_LOADING',\n      payload: true\n    });\n    dispatch({\n      type: 'SET_ERROR',\n      payload: null\n    });\n    try {\n      const response = await gameApi.createGame(config);\n      if (response.success && response.data) {\n        dispatch({\n          type: 'SET_GAME_STATE',\n          payload: response.data\n        });\n        addSystemMessage('游戏创建成功！');\n      } else {\n        throw new Error(response.error || '创建游戏失败');\n      }\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : '创建游戏失败';\n      dispatch({\n        type: 'SET_ERROR',\n        payload: errorMessage\n      });\n    } finally {\n      dispatch({\n        type: 'SET_LOADING',\n        payload: false\n      });\n    }\n  };\n\n  // 加入游戏\n  const joinGame = async gameId => {\n    dispatch({\n      type: 'SET_LOADING',\n      payload: true\n    });\n    dispatch({\n      type: 'SET_ERROR',\n      payload: null\n    });\n    try {\n      const response = await gameApi.joinGame(gameId);\n      if (response.success && response.data) {\n        dispatch({\n          type: 'SET_GAME_STATE',\n          payload: response.data\n        });\n        addSystemMessage('成功加入游戏！');\n      } else {\n        throw new Error(response.error || '加入游戏失败');\n      }\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : '加入游戏失败';\n      dispatch({\n        type: 'SET_ERROR',\n        payload: errorMessage\n      });\n    } finally {\n      dispatch({\n        type: 'SET_LOADING',\n        payload: false\n      });\n    }\n  };\n\n  // 开始游戏\n  const startGame = async () => {\n    if (!state.gameState) return;\n    try {\n      const response = await gameApi.startGame(state.gameState.game_id);\n      if (response.success && response.data) {\n        dispatch({\n          type: 'SET_GAME_STATE',\n          payload: response.data\n        });\n        addSystemMessage('游戏开始！');\n      } else {\n        throw new Error(response.error || '开始游戏失败');\n      }\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : '开始游戏失败';\n      dispatch({\n        type: 'SET_ERROR',\n        payload: errorMessage\n      });\n    }\n  };\n\n  // 选择玩家\n  const selectPlayer = playerId => {\n    dispatch({\n      type: 'SET_SELECTED_PLAYER',\n      payload: playerId\n    });\n  };\n\n  // 提交投票\n  const submitVote = async (targetId, reason) => {\n    if (!state.gameState) return;\n    try {\n      const vote = {\n        voter_id: 1,\n        // TODO: 获取当前玩家ID\n        target_id: targetId,\n        vote_type: 'ELIMINATION',\n        reason: reason || ''\n      };\n      const response = await gameApi.submitVote(state.gameState.game_id, vote);\n      if (response.success) {\n        addSystemMessage(targetId ? `投票给玩家 ${targetId}` : '选择弃权');\n\n        // 通过WebSocket通知其他玩家\n        sendMessage({\n          type: 'vote',\n          data: vote\n        });\n      } else {\n        throw new Error(response.error || '投票失败');\n      }\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : '投票失败';\n      dispatch({\n        type: 'SET_ERROR',\n        payload: errorMessage\n      });\n    }\n  };\n\n  // 提交行动\n  const submitAction = async (actionType, targetId) => {\n    if (!state.gameState) return;\n    try {\n      const action = {\n        player_id: 1,\n        // TODO: 获取当前玩家ID\n        action_type: actionType,\n        target_id: targetId,\n        timestamp: new Date().toISOString()\n      };\n      const response = await gameApi.submitAction(state.gameState.game_id, action);\n      if (response.success) {\n        addSystemMessage(`执行行动: ${actionType}`);\n\n        // 通过WebSocket通知其他玩家\n        sendMessage({\n          type: 'action',\n          data: action\n        });\n      } else {\n        throw new Error(response.error || '行动执行失败');\n      }\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : '行动执行失败';\n      dispatch({\n        type: 'SET_ERROR',\n        payload: errorMessage\n      });\n    }\n  };\n\n  // 发送聊天消息\n  const sendChatMessage = async message => {\n    if (!state.gameState) return;\n    try {\n      const response = await gameApi.sendChatMessage(state.gameState.game_id, message);\n      if (response.success) {\n        const chatMessage = {\n          id: Date.now().toString(),\n          sender: '我',\n          // TODO: 获取当前玩家名称\n          message,\n          timestamp: new Date().toISOString(),\n          type: 'player'\n        };\n        dispatch({\n          type: 'ADD_MESSAGE',\n          payload: chatMessage\n        });\n\n        // 通过WebSocket发送给其他玩家\n        sendMessage({\n          type: 'chat_message',\n          data: {\n            sender: '我',\n            message,\n            type: 'player'\n          }\n        });\n      } else {\n        throw new Error(response.error || '发送消息失败');\n      }\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : '发送消息失败';\n      dispatch({\n        type: 'SET_ERROR',\n        payload: errorMessage\n      });\n    }\n  };\n\n  // 添加系统消息\n  const addSystemMessage = message => {\n    const systemMessage = {\n      id: Date.now().toString(),\n      sender: '系统',\n      message,\n      timestamp: new Date().toISOString(),\n      type: 'system'\n    };\n    dispatch({\n      type: 'ADD_MESSAGE',\n      payload: systemMessage\n    });\n  };\n\n  // 重置错误\n  const resetError = () => {\n    dispatch({\n      type: 'SET_ERROR',\n      payload: null\n    });\n  };\n\n  // 重置游戏\n  const resetGame = () => {\n    dispatch({\n      type: 'RESET_STATE'\n    });\n    localStorage.removeItem('wolfkill_game_id');\n  };\n\n  // 初始化时尝试恢复游戏状态\n  useEffect(() => {\n    const savedGameId = localStorage.getItem('wolfkill_game_id');\n    if (savedGameId && connected) {\n      gameApi.getGameState(savedGameId).then(response => {\n        if (response.success && response.data) {\n          dispatch({\n            type: 'SET_GAME_STATE',\n            payload: response.data\n          });\n        }\n      });\n    }\n  }, [connected]);\n\n  // 保存游戏ID到本地存储\n  useEffect(() => {\n    var _state$gameState;\n    if ((_state$gameState = state.gameState) !== null && _state$gameState !== void 0 && _state$gameState.game_id) {\n      localStorage.setItem('wolfkill_game_id', state.gameState.game_id);\n    }\n  }, [(_state$gameState2 = state.gameState) === null || _state$gameState2 === void 0 ? void 0 : _state$gameState2.game_id]);\n  const contextValue = {\n    ...state,\n    createGame,\n    joinGame,\n    startGame,\n    selectPlayer,\n    submitVote,\n    submitAction,\n    sendChatMessage,\n    addSystemMessage,\n    resetError,\n    resetGame\n  };\n  return /*#__PURE__*/_jsxDEV(GameContext.Provider, {\n    value: contextValue,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 360,\n    columnNumber: 5\n  }, this);\n};\n\n// Hook for using the game context\n_s(GameProvider, \"4NAZGPJESfiY2p+ANrhKh5Rd/KY=\", false, function () {\n  return [useWebSocket];\n});\n_c = GameProvider;\nexport const useGameContext = () => {\n  _s2();\n  const context = useContext(GameContext);\n  if (context === undefined) {\n    throw new Error('useGameContext must be used within a GameProvider');\n  }\n  return context;\n};\n_s2(useGameContext, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport default GameContext;\nvar _c;\n$RefreshReg$(_c, \"GameProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useReducer", "useEffect", "useWebSocket", "gameApi", "jsxDEV", "_jsxDEV", "initialState", "gameState", "loading", "error", "connected", "messages", "selectedPlayerId", "gameReducer", "state", "action", "type", "payload", "GameContext", "undefined", "GameProvider", "children", "_s", "_state$gameState2", "dispatch", "sendMessage", "lastMessage", "data", "chatMessage", "id", "Date", "now", "toString", "sender", "message", "timestamp", "addSystemMessage", "phase", "result", "console", "log", "createGame", "config", "response", "success", "Error", "errorMessage", "joinGame", "gameId", "startGame", "game_id", "selectPlayer", "playerId", "submitVote", "targetId", "reason", "vote", "voter_id", "target_id", "vote_type", "submitAction", "actionType", "player_id", "action_type", "toISOString", "sendChatMessage", "systemMessage", "resetError", "resetGame", "localStorage", "removeItem", "savedGameId", "getItem", "getGameState", "then", "_state$gameState", "setItem", "contextValue", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useGameContext", "_s2", "context", "$RefreshReg$"], "sources": ["/private/peiwy/work/ai_projects/wolfkill/frontend/src/contexts/GameContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';\nimport { GameState, GameConfig, ChatMessage, WebSocketMessage } from '../types';\nimport { useWebSocket } from '../hooks/useWebSocket';\nimport { gameApi } from '../services/api';\n\n// 游戏状态类型\ninterface GameContextState {\n  gameState: GameState | null;\n  loading: boolean;\n  error: string | null;\n  connected: boolean;\n  messages: ChatMessage[];\n  selectedPlayerId: number | null;\n}\n\n// 动作类型\ntype GameAction =\n  | { type: 'SET_LOADING'; payload: boolean }\n  | { type: 'SET_ERROR'; payload: string | null }\n  | { type: 'SET_GAME_STATE'; payload: GameState | null }\n  | { type: 'SET_CONNECTED'; payload: boolean }\n  | { type: 'ADD_MESSAGE'; payload: ChatMessage }\n  | { type: 'SET_MESSAGES'; payload: ChatMessage[] }\n  | { type: 'SET_SELECTED_PLAYER'; payload: number | null }\n  | { type: 'RESET_STATE' };\n\n// Context类型\ninterface GameContextType extends GameContextState {\n  // 游戏操作\n  createGame: (config: GameConfig) => Promise<void>;\n  joinGame: (gameId: string) => Promise<void>;\n  startGame: () => Promise<void>;\n\n  // 玩家操作\n  selectPlayer: (playerId: number | null) => void;\n  submitVote: (targetId: number | null, reason?: string) => Promise<void>;\n  submitAction: (actionType: string, targetId?: number) => Promise<void>;\n\n  // 聊天操作\n  sendChatMessage: (message: string) => Promise<void>;\n  addSystemMessage: (message: string) => void;\n\n  // 工具方法\n  resetError: () => void;\n  resetGame: () => void;\n}\n\n// 初始状态\nconst initialState: GameContextState = {\n  gameState: null,\n  loading: false,\n  error: null,\n  connected: false,\n  messages: [],\n  selectedPlayerId: null\n};\n\n// Reducer\nconst gameReducer = (state: GameContextState, action: GameAction): GameContextState => {\n  switch (action.type) {\n    case 'SET_LOADING':\n      return { ...state, loading: action.payload };\n\n    case 'SET_ERROR':\n      return { ...state, error: action.payload, loading: false };\n\n    case 'SET_GAME_STATE':\n      return { ...state, gameState: action.payload };\n\n    case 'SET_CONNECTED':\n      return { ...state, connected: action.payload };\n\n    case 'ADD_MESSAGE':\n      return {\n        ...state,\n        messages: [...state.messages, action.payload]\n      };\n\n    case 'SET_MESSAGES':\n      return { ...state, messages: action.payload };\n\n    case 'SET_SELECTED_PLAYER':\n      return { ...state, selectedPlayerId: action.payload };\n\n    case 'RESET_STATE':\n      return { ...initialState, connected: state.connected };\n\n    default:\n      return state;\n  }\n};\n\n// 创建Context\nconst GameContext = createContext<GameContextType | undefined>(undefined);\n\n// Provider组件\ninterface GameProviderProps {\n  children: ReactNode;\n}\n\nexport const GameProvider: React.FC<GameProviderProps> = ({ children }) => {\n  const [state, dispatch] = useReducer(gameReducer, initialState);\n  const { connected, sendMessage, lastMessage } = useWebSocket();\n\n  // 更新连接状态\n  useEffect(() => {\n    dispatch({ type: 'SET_CONNECTED', payload: connected });\n  }, [connected]);\n\n  // 处理WebSocket消息\n  useEffect(() => {\n    if (!lastMessage) return;\n\n    switch (lastMessage.type) {\n      case 'game_state_update':\n        dispatch({ type: 'SET_GAME_STATE', payload: lastMessage.data });\n        break;\n\n      case 'chat_message':\n        const chatMessage: ChatMessage = {\n          id: Date.now().toString(),\n          sender: lastMessage.data.sender || '系统',\n          message: lastMessage.data.message,\n          timestamp: lastMessage.timestamp,\n          type: lastMessage.data.type || 'system'\n        };\n        dispatch({ type: 'ADD_MESSAGE', payload: chatMessage });\n        break;\n\n      case 'phase_change':\n        addSystemMessage(`游戏阶段变更: ${lastMessage.data.phase}`);\n        break;\n\n      case 'game_over':\n        addSystemMessage(`游戏结束: ${lastMessage.data.result}`);\n        break;\n\n      default:\n        console.log('Unhandled WebSocket message:', lastMessage);\n    }\n  }, [lastMessage]);\n\n  // 创建游戏\n  const createGame = async (config: GameConfig) => {\n    dispatch({ type: 'SET_LOADING', payload: true });\n    dispatch({ type: 'SET_ERROR', payload: null });\n\n    try {\n      const response = await gameApi.createGame(config);\n      if (response.success && response.data) {\n        dispatch({ type: 'SET_GAME_STATE', payload: response.data });\n        addSystemMessage('游戏创建成功！');\n      } else {\n        throw new Error(response.error || '创建游戏失败');\n      }\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : '创建游戏失败';\n      dispatch({ type: 'SET_ERROR', payload: errorMessage });\n    } finally {\n      dispatch({ type: 'SET_LOADING', payload: false });\n    }\n  };\n\n  // 加入游戏\n  const joinGame = async (gameId: string) => {\n    dispatch({ type: 'SET_LOADING', payload: true });\n    dispatch({ type: 'SET_ERROR', payload: null });\n\n    try {\n      const response = await gameApi.joinGame(gameId);\n      if (response.success && response.data) {\n        dispatch({ type: 'SET_GAME_STATE', payload: response.data });\n        addSystemMessage('成功加入游戏！');\n      } else {\n        throw new Error(response.error || '加入游戏失败');\n      }\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : '加入游戏失败';\n      dispatch({ type: 'SET_ERROR', payload: errorMessage });\n    } finally {\n      dispatch({ type: 'SET_LOADING', payload: false });\n    }\n  };\n\n  // 开始游戏\n  const startGame = async () => {\n    if (!state.gameState) return;\n\n    try {\n      const response = await gameApi.startGame(state.gameState.game_id);\n      if (response.success && response.data) {\n        dispatch({ type: 'SET_GAME_STATE', payload: response.data });\n        addSystemMessage('游戏开始！');\n      } else {\n        throw new Error(response.error || '开始游戏失败');\n      }\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : '开始游戏失败';\n      dispatch({ type: 'SET_ERROR', payload: errorMessage });\n    }\n  };\n\n  // 选择玩家\n  const selectPlayer = (playerId: number | null) => {\n    dispatch({ type: 'SET_SELECTED_PLAYER', payload: playerId });\n  };\n\n  // 提交投票\n  const submitVote = async (targetId: number | null, reason?: string) => {\n    if (!state.gameState) return;\n\n    try {\n      const vote = {\n        voter_id: 1, // TODO: 获取当前玩家ID\n        target_id: targetId,\n        vote_type: 'ELIMINATION' as const,\n        reason: reason || ''\n      };\n\n      const response = await gameApi.submitVote(state.gameState.game_id, vote);\n      if (response.success) {\n        addSystemMessage(targetId ? `投票给玩家 ${targetId}` : '选择弃权');\n\n        // 通过WebSocket通知其他玩家\n        sendMessage({\n          type: 'vote',\n          data: vote\n        });\n      } else {\n        throw new Error(response.error || '投票失败');\n      }\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : '投票失败';\n      dispatch({ type: 'SET_ERROR', payload: errorMessage });\n    }\n  };\n\n  // 提交行动\n  const submitAction = async (actionType: string, targetId?: number) => {\n    if (!state.gameState) return;\n\n    try {\n      const action = {\n        player_id: 1, // TODO: 获取当前玩家ID\n        action_type: actionType,\n        target_id: targetId,\n        timestamp: new Date().toISOString()\n      };\n\n      const response = await gameApi.submitAction(state.gameState.game_id, action);\n      if (response.success) {\n        addSystemMessage(`执行行动: ${actionType}`);\n\n        // 通过WebSocket通知其他玩家\n        sendMessage({\n          type: 'action',\n          data: action\n        });\n      } else {\n        throw new Error(response.error || '行动执行失败');\n      }\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : '行动执行失败';\n      dispatch({ type: 'SET_ERROR', payload: errorMessage });\n    }\n  };\n\n  // 发送聊天消息\n  const sendChatMessage = async (message: string) => {\n    if (!state.gameState) return;\n\n    try {\n      const response = await gameApi.sendChatMessage(state.gameState.game_id, message);\n      if (response.success) {\n        const chatMessage: ChatMessage = {\n          id: Date.now().toString(),\n          sender: '我', // TODO: 获取当前玩家名称\n          message,\n          timestamp: new Date().toISOString(),\n          type: 'player'\n        };\n\n        dispatch({ type: 'ADD_MESSAGE', payload: chatMessage });\n\n        // 通过WebSocket发送给其他玩家\n        sendMessage({\n          type: 'chat_message',\n          data: {\n            sender: '我',\n            message,\n            type: 'player'\n          }\n        });\n      } else {\n        throw new Error(response.error || '发送消息失败');\n      }\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : '发送消息失败';\n      dispatch({ type: 'SET_ERROR', payload: errorMessage });\n    }\n  };\n\n  // 添加系统消息\n  const addSystemMessage = (message: string) => {\n    const systemMessage: ChatMessage = {\n      id: Date.now().toString(),\n      sender: '系统',\n      message,\n      timestamp: new Date().toISOString(),\n      type: 'system'\n    };\n    dispatch({ type: 'ADD_MESSAGE', payload: systemMessage });\n  };\n\n  // 重置错误\n  const resetError = () => {\n    dispatch({ type: 'SET_ERROR', payload: null });\n  };\n\n  // 重置游戏\n  const resetGame = () => {\n    dispatch({ type: 'RESET_STATE' });\n    localStorage.removeItem('wolfkill_game_id');\n  };\n\n  // 初始化时尝试恢复游戏状态\n  useEffect(() => {\n    const savedGameId = localStorage.getItem('wolfkill_game_id');\n    if (savedGameId && connected) {\n      gameApi.getGameState(savedGameId).then(response => {\n        if (response.success && response.data) {\n          dispatch({ type: 'SET_GAME_STATE', payload: response.data });\n        }\n      });\n    }\n  }, [connected]);\n\n  // 保存游戏ID到本地存储\n  useEffect(() => {\n    if (state.gameState?.game_id) {\n      localStorage.setItem('wolfkill_game_id', state.gameState.game_id);\n    }\n  }, [state.gameState?.game_id]);\n\n  const contextValue: GameContextType = {\n    ...state,\n    createGame,\n    joinGame,\n    startGame,\n    selectPlayer,\n    submitVote,\n    submitAction,\n    sendChatMessage,\n    addSystemMessage,\n    resetError,\n    resetGame\n  };\n\n  return (\n    <GameContext.Provider value={contextValue}>\n      {children}\n    </GameContext.Provider>\n  );\n};\n\n// Hook for using the game context\nexport const useGameContext = (): GameContextType => {\n  const context = useContext(GameContext);\n  if (context === undefined) {\n    throw new Error('useGameContext must be used within a GameProvider');\n  }\n  return context;\n};\n\nexport default GameContext;"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,UAAU,EAAEC,SAAS,QAAmB,OAAO;AAE1F,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,OAAO,QAAQ,iBAAiB;;AAEzC;;AAUA;;AAWA;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAqBA;AACA,MAAMC,YAA8B,GAAG;EACrCC,SAAS,EAAE,IAAI;EACfC,OAAO,EAAE,KAAK;EACdC,KAAK,EAAE,IAAI;EACXC,SAAS,EAAE,KAAK;EAChBC,QAAQ,EAAE,EAAE;EACZC,gBAAgB,EAAE;AACpB,CAAC;;AAED;AACA,MAAMC,WAAW,GAAGA,CAACC,KAAuB,EAAEC,MAAkB,KAAuB;EACrF,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK,aAAa;MAChB,OAAO;QAAE,GAAGF,KAAK;QAAEN,OAAO,EAAEO,MAAM,CAACE;MAAQ,CAAC;IAE9C,KAAK,WAAW;MACd,OAAO;QAAE,GAAGH,KAAK;QAAEL,KAAK,EAAEM,MAAM,CAACE,OAAO;QAAET,OAAO,EAAE;MAAM,CAAC;IAE5D,KAAK,gBAAgB;MACnB,OAAO;QAAE,GAAGM,KAAK;QAAEP,SAAS,EAAEQ,MAAM,CAACE;MAAQ,CAAC;IAEhD,KAAK,eAAe;MAClB,OAAO;QAAE,GAAGH,KAAK;QAAEJ,SAAS,EAAEK,MAAM,CAACE;MAAQ,CAAC;IAEhD,KAAK,aAAa;MAChB,OAAO;QACL,GAAGH,KAAK;QACRH,QAAQ,EAAE,CAAC,GAAGG,KAAK,CAACH,QAAQ,EAAEI,MAAM,CAACE,OAAO;MAC9C,CAAC;IAEH,KAAK,cAAc;MACjB,OAAO;QAAE,GAAGH,KAAK;QAAEH,QAAQ,EAAEI,MAAM,CAACE;MAAQ,CAAC;IAE/C,KAAK,qBAAqB;MACxB,OAAO;QAAE,GAAGH,KAAK;QAAEF,gBAAgB,EAAEG,MAAM,CAACE;MAAQ,CAAC;IAEvD,KAAK,aAAa;MAChB,OAAO;QAAE,GAAGX,YAAY;QAAEI,SAAS,EAAEI,KAAK,CAACJ;MAAU,CAAC;IAExD;MACE,OAAOI,KAAK;EAChB;AACF,CAAC;;AAED;AACA,MAAMI,WAAW,gBAAGpB,aAAa,CAA8BqB,SAAS,CAAC;;AAEzE;;AAKA,OAAO,MAAMC,YAAyC,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,iBAAA;EACzE,MAAM,CAACT,KAAK,EAAEU,QAAQ,CAAC,GAAGxB,UAAU,CAACa,WAAW,EAAEP,YAAY,CAAC;EAC/D,MAAM;IAAEI,SAAS;IAAEe,WAAW;IAAEC;EAAY,CAAC,GAAGxB,YAAY,CAAC,CAAC;;EAE9D;EACAD,SAAS,CAAC,MAAM;IACduB,QAAQ,CAAC;MAAER,IAAI,EAAE,eAAe;MAAEC,OAAO,EAAEP;IAAU,CAAC,CAAC;EACzD,CAAC,EAAE,CAACA,SAAS,CAAC,CAAC;;EAEf;EACAT,SAAS,CAAC,MAAM;IACd,IAAI,CAACyB,WAAW,EAAE;IAElB,QAAQA,WAAW,CAACV,IAAI;MACtB,KAAK,mBAAmB;QACtBQ,QAAQ,CAAC;UAAER,IAAI,EAAE,gBAAgB;UAAEC,OAAO,EAAES,WAAW,CAACC;QAAK,CAAC,CAAC;QAC/D;MAEF,KAAK,cAAc;QACjB,MAAMC,WAAwB,GAAG;UAC/BC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;UACzBC,MAAM,EAAEP,WAAW,CAACC,IAAI,CAACM,MAAM,IAAI,IAAI;UACvCC,OAAO,EAAER,WAAW,CAACC,IAAI,CAACO,OAAO;UACjCC,SAAS,EAAET,WAAW,CAACS,SAAS;UAChCnB,IAAI,EAAEU,WAAW,CAACC,IAAI,CAACX,IAAI,IAAI;QACjC,CAAC;QACDQ,QAAQ,CAAC;UAAER,IAAI,EAAE,aAAa;UAAEC,OAAO,EAAEW;QAAY,CAAC,CAAC;QACvD;MAEF,KAAK,cAAc;QACjBQ,gBAAgB,CAAC,WAAWV,WAAW,CAACC,IAAI,CAACU,KAAK,EAAE,CAAC;QACrD;MAEF,KAAK,WAAW;QACdD,gBAAgB,CAAC,SAASV,WAAW,CAACC,IAAI,CAACW,MAAM,EAAE,CAAC;QACpD;MAEF;QACEC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEd,WAAW,CAAC;IAC5D;EACF,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAMe,UAAU,GAAG,MAAOC,MAAkB,IAAK;IAC/ClB,QAAQ,CAAC;MAAER,IAAI,EAAE,aAAa;MAAEC,OAAO,EAAE;IAAK,CAAC,CAAC;IAChDO,QAAQ,CAAC;MAAER,IAAI,EAAE,WAAW;MAAEC,OAAO,EAAE;IAAK,CAAC,CAAC;IAE9C,IAAI;MACF,MAAM0B,QAAQ,GAAG,MAAMxC,OAAO,CAACsC,UAAU,CAACC,MAAM,CAAC;MACjD,IAAIC,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAAChB,IAAI,EAAE;QACrCH,QAAQ,CAAC;UAAER,IAAI,EAAE,gBAAgB;UAAEC,OAAO,EAAE0B,QAAQ,CAAChB;QAAK,CAAC,CAAC;QAC5DS,gBAAgB,CAAC,SAAS,CAAC;MAC7B,CAAC,MAAM;QACL,MAAM,IAAIS,KAAK,CAACF,QAAQ,CAAClC,KAAK,IAAI,QAAQ,CAAC;MAC7C;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd,MAAMqC,YAAY,GAAGrC,KAAK,YAAYoC,KAAK,GAAGpC,KAAK,CAACyB,OAAO,GAAG,QAAQ;MACtEV,QAAQ,CAAC;QAAER,IAAI,EAAE,WAAW;QAAEC,OAAO,EAAE6B;MAAa,CAAC,CAAC;IACxD,CAAC,SAAS;MACRtB,QAAQ,CAAC;QAAER,IAAI,EAAE,aAAa;QAAEC,OAAO,EAAE;MAAM,CAAC,CAAC;IACnD;EACF,CAAC;;EAED;EACA,MAAM8B,QAAQ,GAAG,MAAOC,MAAc,IAAK;IACzCxB,QAAQ,CAAC;MAAER,IAAI,EAAE,aAAa;MAAEC,OAAO,EAAE;IAAK,CAAC,CAAC;IAChDO,QAAQ,CAAC;MAAER,IAAI,EAAE,WAAW;MAAEC,OAAO,EAAE;IAAK,CAAC,CAAC;IAE9C,IAAI;MACF,MAAM0B,QAAQ,GAAG,MAAMxC,OAAO,CAAC4C,QAAQ,CAACC,MAAM,CAAC;MAC/C,IAAIL,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAAChB,IAAI,EAAE;QACrCH,QAAQ,CAAC;UAAER,IAAI,EAAE,gBAAgB;UAAEC,OAAO,EAAE0B,QAAQ,CAAChB;QAAK,CAAC,CAAC;QAC5DS,gBAAgB,CAAC,SAAS,CAAC;MAC7B,CAAC,MAAM;QACL,MAAM,IAAIS,KAAK,CAACF,QAAQ,CAAClC,KAAK,IAAI,QAAQ,CAAC;MAC7C;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd,MAAMqC,YAAY,GAAGrC,KAAK,YAAYoC,KAAK,GAAGpC,KAAK,CAACyB,OAAO,GAAG,QAAQ;MACtEV,QAAQ,CAAC;QAAER,IAAI,EAAE,WAAW;QAAEC,OAAO,EAAE6B;MAAa,CAAC,CAAC;IACxD,CAAC,SAAS;MACRtB,QAAQ,CAAC;QAAER,IAAI,EAAE,aAAa;QAAEC,OAAO,EAAE;MAAM,CAAC,CAAC;IACnD;EACF,CAAC;;EAED;EACA,MAAMgC,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI,CAACnC,KAAK,CAACP,SAAS,EAAE;IAEtB,IAAI;MACF,MAAMoC,QAAQ,GAAG,MAAMxC,OAAO,CAAC8C,SAAS,CAACnC,KAAK,CAACP,SAAS,CAAC2C,OAAO,CAAC;MACjE,IAAIP,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAAChB,IAAI,EAAE;QACrCH,QAAQ,CAAC;UAAER,IAAI,EAAE,gBAAgB;UAAEC,OAAO,EAAE0B,QAAQ,CAAChB;QAAK,CAAC,CAAC;QAC5DS,gBAAgB,CAAC,OAAO,CAAC;MAC3B,CAAC,MAAM;QACL,MAAM,IAAIS,KAAK,CAACF,QAAQ,CAAClC,KAAK,IAAI,QAAQ,CAAC;MAC7C;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd,MAAMqC,YAAY,GAAGrC,KAAK,YAAYoC,KAAK,GAAGpC,KAAK,CAACyB,OAAO,GAAG,QAAQ;MACtEV,QAAQ,CAAC;QAAER,IAAI,EAAE,WAAW;QAAEC,OAAO,EAAE6B;MAAa,CAAC,CAAC;IACxD;EACF,CAAC;;EAED;EACA,MAAMK,YAAY,GAAIC,QAAuB,IAAK;IAChD5B,QAAQ,CAAC;MAAER,IAAI,EAAE,qBAAqB;MAAEC,OAAO,EAAEmC;IAAS,CAAC,CAAC;EAC9D,CAAC;;EAED;EACA,MAAMC,UAAU,GAAG,MAAAA,CAAOC,QAAuB,EAAEC,MAAe,KAAK;IACrE,IAAI,CAACzC,KAAK,CAACP,SAAS,EAAE;IAEtB,IAAI;MACF,MAAMiD,IAAI,GAAG;QACXC,QAAQ,EAAE,CAAC;QAAE;QACbC,SAAS,EAAEJ,QAAQ;QACnBK,SAAS,EAAE,aAAsB;QACjCJ,MAAM,EAAEA,MAAM,IAAI;MACpB,CAAC;MAED,MAAMZ,QAAQ,GAAG,MAAMxC,OAAO,CAACkD,UAAU,CAACvC,KAAK,CAACP,SAAS,CAAC2C,OAAO,EAAEM,IAAI,CAAC;MACxE,IAAIb,QAAQ,CAACC,OAAO,EAAE;QACpBR,gBAAgB,CAACkB,QAAQ,GAAG,SAASA,QAAQ,EAAE,GAAG,MAAM,CAAC;;QAEzD;QACA7B,WAAW,CAAC;UACVT,IAAI,EAAE,MAAM;UACZW,IAAI,EAAE6B;QACR,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,MAAM,IAAIX,KAAK,CAACF,QAAQ,CAAClC,KAAK,IAAI,MAAM,CAAC;MAC3C;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd,MAAMqC,YAAY,GAAGrC,KAAK,YAAYoC,KAAK,GAAGpC,KAAK,CAACyB,OAAO,GAAG,MAAM;MACpEV,QAAQ,CAAC;QAAER,IAAI,EAAE,WAAW;QAAEC,OAAO,EAAE6B;MAAa,CAAC,CAAC;IACxD;EACF,CAAC;;EAED;EACA,MAAMc,YAAY,GAAG,MAAAA,CAAOC,UAAkB,EAAEP,QAAiB,KAAK;IACpE,IAAI,CAACxC,KAAK,CAACP,SAAS,EAAE;IAEtB,IAAI;MACF,MAAMQ,MAAM,GAAG;QACb+C,SAAS,EAAE,CAAC;QAAE;QACdC,WAAW,EAAEF,UAAU;QACvBH,SAAS,EAAEJ,QAAQ;QACnBnB,SAAS,EAAE,IAAIL,IAAI,CAAC,CAAC,CAACkC,WAAW,CAAC;MACpC,CAAC;MAED,MAAMrB,QAAQ,GAAG,MAAMxC,OAAO,CAACyD,YAAY,CAAC9C,KAAK,CAACP,SAAS,CAAC2C,OAAO,EAAEnC,MAAM,CAAC;MAC5E,IAAI4B,QAAQ,CAACC,OAAO,EAAE;QACpBR,gBAAgB,CAAC,SAASyB,UAAU,EAAE,CAAC;;QAEvC;QACApC,WAAW,CAAC;UACVT,IAAI,EAAE,QAAQ;UACdW,IAAI,EAAEZ;QACR,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,MAAM,IAAI8B,KAAK,CAACF,QAAQ,CAAClC,KAAK,IAAI,QAAQ,CAAC;MAC7C;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd,MAAMqC,YAAY,GAAGrC,KAAK,YAAYoC,KAAK,GAAGpC,KAAK,CAACyB,OAAO,GAAG,QAAQ;MACtEV,QAAQ,CAAC;QAAER,IAAI,EAAE,WAAW;QAAEC,OAAO,EAAE6B;MAAa,CAAC,CAAC;IACxD;EACF,CAAC;;EAED;EACA,MAAMmB,eAAe,GAAG,MAAO/B,OAAe,IAAK;IACjD,IAAI,CAACpB,KAAK,CAACP,SAAS,EAAE;IAEtB,IAAI;MACF,MAAMoC,QAAQ,GAAG,MAAMxC,OAAO,CAAC8D,eAAe,CAACnD,KAAK,CAACP,SAAS,CAAC2C,OAAO,EAAEhB,OAAO,CAAC;MAChF,IAAIS,QAAQ,CAACC,OAAO,EAAE;QACpB,MAAMhB,WAAwB,GAAG;UAC/BC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;UACzBC,MAAM,EAAE,GAAG;UAAE;UACbC,OAAO;UACPC,SAAS,EAAE,IAAIL,IAAI,CAAC,CAAC,CAACkC,WAAW,CAAC,CAAC;UACnChD,IAAI,EAAE;QACR,CAAC;QAEDQ,QAAQ,CAAC;UAAER,IAAI,EAAE,aAAa;UAAEC,OAAO,EAAEW;QAAY,CAAC,CAAC;;QAEvD;QACAH,WAAW,CAAC;UACVT,IAAI,EAAE,cAAc;UACpBW,IAAI,EAAE;YACJM,MAAM,EAAE,GAAG;YACXC,OAAO;YACPlB,IAAI,EAAE;UACR;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,MAAM,IAAI6B,KAAK,CAACF,QAAQ,CAAClC,KAAK,IAAI,QAAQ,CAAC;MAC7C;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd,MAAMqC,YAAY,GAAGrC,KAAK,YAAYoC,KAAK,GAAGpC,KAAK,CAACyB,OAAO,GAAG,QAAQ;MACtEV,QAAQ,CAAC;QAAER,IAAI,EAAE,WAAW;QAAEC,OAAO,EAAE6B;MAAa,CAAC,CAAC;IACxD;EACF,CAAC;;EAED;EACA,MAAMV,gBAAgB,GAAIF,OAAe,IAAK;IAC5C,MAAMgC,aAA0B,GAAG;MACjCrC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;MACzBC,MAAM,EAAE,IAAI;MACZC,OAAO;MACPC,SAAS,EAAE,IAAIL,IAAI,CAAC,CAAC,CAACkC,WAAW,CAAC,CAAC;MACnChD,IAAI,EAAE;IACR,CAAC;IACDQ,QAAQ,CAAC;MAAER,IAAI,EAAE,aAAa;MAAEC,OAAO,EAAEiD;IAAc,CAAC,CAAC;EAC3D,CAAC;;EAED;EACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB3C,QAAQ,CAAC;MAAER,IAAI,EAAE,WAAW;MAAEC,OAAO,EAAE;IAAK,CAAC,CAAC;EAChD,CAAC;;EAED;EACA,MAAMmD,SAAS,GAAGA,CAAA,KAAM;IACtB5C,QAAQ,CAAC;MAAER,IAAI,EAAE;IAAc,CAAC,CAAC;IACjCqD,YAAY,CAACC,UAAU,CAAC,kBAAkB,CAAC;EAC7C,CAAC;;EAED;EACArE,SAAS,CAAC,MAAM;IACd,MAAMsE,WAAW,GAAGF,YAAY,CAACG,OAAO,CAAC,kBAAkB,CAAC;IAC5D,IAAID,WAAW,IAAI7D,SAAS,EAAE;MAC5BP,OAAO,CAACsE,YAAY,CAACF,WAAW,CAAC,CAACG,IAAI,CAAC/B,QAAQ,IAAI;QACjD,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAAChB,IAAI,EAAE;UACrCH,QAAQ,CAAC;YAAER,IAAI,EAAE,gBAAgB;YAAEC,OAAO,EAAE0B,QAAQ,CAAChB;UAAK,CAAC,CAAC;QAC9D;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACjB,SAAS,CAAC,CAAC;;EAEf;EACAT,SAAS,CAAC,MAAM;IAAA,IAAA0E,gBAAA;IACd,KAAAA,gBAAA,GAAI7D,KAAK,CAACP,SAAS,cAAAoE,gBAAA,eAAfA,gBAAA,CAAiBzB,OAAO,EAAE;MAC5BmB,YAAY,CAACO,OAAO,CAAC,kBAAkB,EAAE9D,KAAK,CAACP,SAAS,CAAC2C,OAAO,CAAC;IACnE;EACF,CAAC,EAAE,EAAA3B,iBAAA,GAACT,KAAK,CAACP,SAAS,cAAAgB,iBAAA,uBAAfA,iBAAA,CAAiB2B,OAAO,CAAC,CAAC;EAE9B,MAAM2B,YAA6B,GAAG;IACpC,GAAG/D,KAAK;IACR2B,UAAU;IACVM,QAAQ;IACRE,SAAS;IACTE,YAAY;IACZE,UAAU;IACVO,YAAY;IACZK,eAAe;IACf7B,gBAAgB;IAChB+B,UAAU;IACVC;EACF,CAAC;EAED,oBACE/D,OAAA,CAACa,WAAW,CAAC4D,QAAQ;IAACC,KAAK,EAAEF,YAAa;IAAAxD,QAAA,EACvCA;EAAQ;IAAA2D,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;;AAED;AAAA7D,EAAA,CAzQaF,YAAyC;EAAA,QAEJlB,YAAY;AAAA;AAAAkF,EAAA,GAFjDhE,YAAyC;AA0QtD,OAAO,MAAMiE,cAAc,GAAGA,CAAA,KAAuB;EAAAC,GAAA;EACnD,MAAMC,OAAO,GAAGxF,UAAU,CAACmB,WAAW,CAAC;EACvC,IAAIqE,OAAO,KAAKpE,SAAS,EAAE;IACzB,MAAM,IAAI0B,KAAK,CAAC,mDAAmD,CAAC;EACtE;EACA,OAAO0C,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,cAAc;AAQ3B,eAAenE,WAAW;AAAC,IAAAkE,EAAA;AAAAI,YAAA,CAAAJ,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}