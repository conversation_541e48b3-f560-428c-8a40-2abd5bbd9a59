{"name": "wolfkill-frontend", "version": "1.0.0", "private": true, "dependencies": {"@types/node": "^16.18.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "typescript": "^4.9.0", "styled-components": "^6.0.0", "@types/styled-components": "^5.1.0", "axios": "^1.6.0", "socket.io-client": "^4.7.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/jest": "^27.5.0", "@testing-library/jest-dom": "^5.16.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0"}}