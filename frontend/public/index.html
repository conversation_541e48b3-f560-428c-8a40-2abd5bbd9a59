<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#2C3E50" />
    <meta name="description" content="狼人杀AI游戏 - 在线多人狼人杀游戏平台" />
    <title>狼人杀AI游戏</title>
    <style>
      /* 加载动画 */
      .loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #ECF0F1;
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
      }

      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 4px solid #BDC3C7;
        border-top: 4px solid #2C3E50;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      .loading-text {
        margin-top: 20px;
        color: #2C3E50;
        font-family: "Microsoft YaHei", sans-serif;
        font-size: 16px;
      }
    </style>
  </head>
  <body>
    <noscript>您需要启用JavaScript才能运行此应用程序。</noscript>

    <!-- 加载动画 -->
    <div id="loading" class="loading">
      <div>
        <div class="loading-spinner"></div>
        <div class="loading-text">正在加载狼人杀游戏...</div>
      </div>
    </div>

    <div id="root"></div>

    <script>
      // 隐藏加载动画
      window.addEventListener('load', function() {
        setTimeout(function() {
          const loading = document.getElementById('loading');
          if (loading) {
            loading.style.display = 'none';
          }
        }, 1000);
      });
    </script>
  </body>
</html>