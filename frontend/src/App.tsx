import React, { useState, useEffect } from 'react';
import styled, { ThemeProvider, createGlobalStyle } from 'styled-components';
import { theme } from './styles/theme';
import { GameState, GamePhase } from './types';
import GameBoard from './components/GameBoard';
import PlayerList from './components/PlayerList';
import ChatPanel from './components/ChatPanel';
import Header from './components/Header';
import { useGameState } from './hooks/useGameState';
import { useWebSocket } from './hooks/useWebSocket';

// 全局样式
const GlobalStyle = createGlobalStyle`
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  body {
    font-family: ${props => props.theme.fonts.primary};
    background-color: ${props => props.theme.colors.background};
    color: ${props => props.theme.colors.text};
    line-height: ${props => props.theme.lineHeights.normal};
  }

  button {
    font-family: inherit;
    cursor: pointer;
    border: none;
    outline: none;
    transition: ${props => props.theme.transitions.fast};
  }

  input, textarea {
    font-family: inherit;
    outline: none;
  }
`;

// 样式组件
const AppContainer = styled.div`
  min-height: 100vh;
  display: flex;
  flex-direction: column;
`;

const MainContent = styled.main`
  flex: 1;
  display: grid;
  grid-template-columns: 250px 1fr 300px;
  grid-template-rows: 1fr;
  gap: ${props => props.theme.spacing.md};
  padding: ${props => props.theme.spacing.md};
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;

  @media (max-width: ${props => props.theme.breakpoints.lg}) {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto 1fr;
  }
`;

const LeftPanel = styled.aside`
  background: ${props => props.theme.colors.backgroundLight};
  border-radius: ${props => props.theme.borderRadius.lg};
  box-shadow: ${props => props.theme.shadows.md};
  padding: ${props => props.theme.spacing.lg};
  overflow-y: auto;

  @media (max-width: ${props => props.theme.breakpoints.lg}) {
    order: 2;
  }
`;

const CenterPanel = styled.section`
  background: ${props => props.theme.colors.backgroundLight};
  border-radius: ${props => props.theme.borderRadius.lg};
  box-shadow: ${props => props.theme.shadows.md};
  padding: ${props => props.theme.spacing.lg};
  overflow-y: auto;

  @media (max-width: ${props => props.theme.breakpoints.lg}) {
    order: 1;
  }
`;

const RightPanel = styled.aside`
  background: ${props => props.theme.colors.backgroundLight};
  border-radius: ${props => props.theme.borderRadius.lg};
  box-shadow: ${props => props.theme.shadows.md};
  padding: ${props => props.theme.spacing.lg};
  overflow-y: auto;

  @media (max-width: ${props => props.theme.breakpoints.lg}) {
    order: 3;
  }
`;

const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  font-size: ${props => props.theme.fontSizes.xl};
  color: ${props => props.theme.colors.textLight};
`;

const ErrorContainer = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  padding: ${props => props.theme.spacing.xl};
  text-align: center;
`;

const ErrorTitle = styled.h1`
  color: ${props => props.theme.colors.danger};
  font-size: ${props => props.theme.fontSizes['2xl']};
  margin-bottom: ${props => props.theme.spacing.md};
`;

const ErrorMessage = styled.p`
  color: ${props => props.theme.colors.textLight};
  font-size: ${props => props.theme.fontSizes.lg};
  margin-bottom: ${props => props.theme.spacing.lg};
`;

const RetryButton = styled.button`
  background: ${props => props.theme.colors.primary};
  color: ${props => props.theme.colors.textWhite};
  padding: ${props => props.theme.spacing.md} ${props => props.theme.spacing.lg};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: ${props => props.theme.fontSizes.md};
  font-weight: ${props => props.theme.fontWeights.medium};

  &:hover {
    background: ${props => props.theme.colors.primaryLight};
  }
`;

function App() {
  const { gameState, loading, error, createGame, joinGame } = useGameState();
  const { connected, sendMessage } = useWebSocket();

  // 处理游戏创建
  const handleCreateGame = async () => {
    try {
      await createGame({
        total_players: 6,
        role_distribution: {
          VILLAGER: 2,
          WEREWOLF: 2,
          SEER: 1,
          WITCH: 1
        }
      });
    } catch (err) {
      console.error('Failed to create game:', err);
    }
  };

  // 处理重试
  const handleRetry = () => {
    window.location.reload();
  };

  if (loading) {
    return (
      <ThemeProvider theme={theme}>
        <GlobalStyle />
        <LoadingContainer>
          正在加载游戏...
        </LoadingContainer>
      </ThemeProvider>
    );
  }

  if (error) {
    return (
      <ThemeProvider theme={theme}>
        <GlobalStyle />
        <ErrorContainer>
          <ErrorTitle>连接失败</ErrorTitle>
          <ErrorMessage>{error}</ErrorMessage>
          <RetryButton onClick={handleRetry}>
            重试
          </RetryButton>
        </ErrorContainer>
      </ThemeProvider>
    );
  }

  return (
    <ThemeProvider theme={theme}>
      <GlobalStyle />
      <AppContainer>
        <Header
          gameState={gameState}
          onCreateGame={handleCreateGame}
          connected={connected}
        />

        <MainContent>
          <LeftPanel>
            <PlayerList
              players={gameState?.players || {}}
              currentPhase={gameState?.current_phase || GamePhase.SETUP}
            />
          </LeftPanel>

          <CenterPanel>
            <GameBoard
              gameState={gameState}
              onAction={sendMessage}
            />
          </CenterPanel>

          <RightPanel>
            <ChatPanel
              onSendMessage={sendMessage}
            />
          </RightPanel>
        </MainContent>
      </AppContainer>
    </ThemeProvider>
  );
}

export default App;