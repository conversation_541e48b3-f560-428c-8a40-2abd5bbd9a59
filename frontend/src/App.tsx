import React from 'react';
import styled, { ThemeProvider, createGlobalStyle } from 'styled-components';
import { theme } from './styles/theme';
import { GamePhase } from './types';
import GameBoard from './components/GameBoard';
import PlayerList from './components/PlayerList';
import ChatPanel from './components/ChatPanel';
import Header from './components/Header';
import { GameProvider, useGameContext } from './contexts/GameContext';

// 全局样式
const GlobalStyle = createGlobalStyle`
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  body {
    font-family: ${props => props.theme.fonts.primary};
    background-color: ${props => props.theme.colors.background};
    color: ${props => props.theme.colors.text};
    line-height: ${props => props.theme.lineHeights.normal};
  }

  button {
    font-family: inherit;
    cursor: pointer;
    border: none;
    outline: none;
    transition: ${props => props.theme.transitions.fast};
  }

  input, textarea {
    font-family: inherit;
    outline: none;
  }
`;

// 样式组件
const AppContainer = styled.div`
  min-height: 100vh;
  display: flex;
  flex-direction: column;
`;

const MainContent = styled.main`
  flex: 1;
  display: grid;
  grid-template-columns: 250px 1fr 300px;
  grid-template-rows: 1fr;
  gap: ${props => props.theme.spacing.md};
  padding: ${props => props.theme.spacing.md};
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;

  @media (max-width: ${props => props.theme.breakpoints.lg}) {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto 1fr;
  }
`;

const LeftPanel = styled.aside`
  background: ${props => props.theme.colors.backgroundLight};
  border-radius: ${props => props.theme.borderRadius.lg};
  box-shadow: ${props => props.theme.shadows.md};
  padding: ${props => props.theme.spacing.lg};
  overflow-y: auto;

  @media (max-width: ${props => props.theme.breakpoints.lg}) {
    order: 2;
  }
`;

const CenterPanel = styled.section`
  background: ${props => props.theme.colors.backgroundLight};
  border-radius: ${props => props.theme.borderRadius.lg};
  box-shadow: ${props => props.theme.shadows.md};
  padding: ${props => props.theme.spacing.lg};
  overflow-y: auto;

  @media (max-width: ${props => props.theme.breakpoints.lg}) {
    order: 1;
  }
`;

const RightPanel = styled.aside`
  background: ${props => props.theme.colors.backgroundLight};
  border-radius: ${props => props.theme.borderRadius.lg};
  box-shadow: ${props => props.theme.shadows.md};
  padding: ${props => props.theme.spacing.lg};
  overflow-y: auto;

  @media (max-width: ${props => props.theme.breakpoints.lg}) {
    order: 3;
  }
`;

const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  font-size: ${props => props.theme.fontSizes.xl};
  color: ${props => props.theme.colors.textLight};
`;

const ErrorContainer = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  padding: ${props => props.theme.spacing.xl};
  text-align: center;
`;

const ErrorTitle = styled.h1`
  color: ${props => props.theme.colors.danger};
  font-size: ${props => props.theme.fontSizes['2xl']};
  margin-bottom: ${props => props.theme.spacing.md};
`;

const ErrorMessage = styled.p`
  color: ${props => props.theme.colors.textLight};
  font-size: ${props => props.theme.fontSizes.lg};
  margin-bottom: ${props => props.theme.spacing.lg};
`;

const RetryButton = styled.button`
  background: ${props => props.theme.colors.primary};
  color: ${props => props.theme.colors.textWhite};
  padding: ${props => props.theme.spacing.md} ${props => props.theme.spacing.lg};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: ${props => props.theme.fontSizes.md};
  font-weight: ${props => props.theme.fontWeights.medium};

  &:hover {
    background: ${props => props.theme.colors.primaryLight};
  }
`;

// 主应用组件
const AppContent: React.FC = () => {
  const {
    gameState,
    loading,
    error,
    connected,
    messages,
    selectedPlayerId,
    createGame,
    selectPlayer,
    sendChatMessage,
    resetError
  } = useGameContext();

  // 处理游戏创建
  const handleCreateGame = async () => {
    try {
      await createGame({
        total_players: 6,
        role_distribution: {
          VILLAGER: 2,
          WEREWOLF: 2,
          SEER: 1,
          WITCH: 1
        }
      });
    } catch (err) {
      console.error('Failed to create game:', err);
    }
  };

  // 处理重试
  const handleRetry = () => {
    resetError();
    window.location.reload();
  };

  // 处理游戏操作
  const handleGameAction = (action: any) => {
    console.log('Game action:', action);
    // 这里可以根据action类型调用不同的方法
  };

  if (loading) {
    return (
      <LoadingContainer>
        正在加载游戏...
      </LoadingContainer>
    );
  }

  if (error) {
    return (
      <ErrorContainer>
        <ErrorTitle>连接失败</ErrorTitle>
        <ErrorMessage>{error}</ErrorMessage>
        <RetryButton onClick={handleRetry}>
          重试
        </RetryButton>
      </ErrorContainer>
    );
  }

  return (
    <AppContainer>
      <Header
        gameState={gameState}
        onCreateGame={handleCreateGame}
        connected={connected}
      />

      <MainContent>
        <LeftPanel>
          <PlayerList
            players={gameState?.players || {}}
            currentPhase={gameState?.current_phase || GamePhase.SETUP}
            onPlayerSelect={selectPlayer}
          />
        </LeftPanel>

        <CenterPanel>
          <GameBoard
            gameState={gameState}
            onAction={handleGameAction}
          />
        </CenterPanel>

        <RightPanel>
          <ChatPanel
            messages={messages}
            onSendMessage={sendChatMessage}
          />
        </RightPanel>
      </MainContent>
    </AppContainer>
  );
}

// 简单的测试组件
function TestApp() {
  return (
    <ThemeProvider theme={theme}>
      <GlobalStyle />
      <div style={{
        padding: '20px',
        fontFamily: 'Arial, sans-serif',
        backgroundColor: '#f0f0f0',
        minHeight: '100vh'
      }}>
        <h1 style={{ color: '#333', textAlign: 'center' }}>🐺 狼人杀AI游戏</h1>
        <div style={{
          maxWidth: '800px',
          margin: '0 auto',
          backgroundColor: 'white',
          padding: '20px',
          borderRadius: '8px',
          boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
        }}>
          <h2>游戏状态</h2>
          <p>✅ 前端服务器运行正常</p>
          <p>🔌 正在连接后端服务器...</p>

          <button
            style={{
              backgroundColor: '#007bff',
              color: 'white',
              border: 'none',
              padding: '10px 20px',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '16px',
              marginTop: '20px'
            }}
            onClick={() => {
              fetch('http://localhost:8000/api/health')
                .then(res => res.json())
                .then(data => {
                  alert('后端连接成功！\n' + JSON.stringify(data, null, 2));
                })
                .catch(err => {
                  alert('后端连接失败：' + err.message);
                });
            }}
          >
            测试后端连接
          </button>

          <button
            style={{
              backgroundColor: '#28a745',
              color: 'white',
              border: 'none',
              padding: '10px 20px',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '16px',
              marginTop: '20px',
              marginLeft: '10px'
            }}
            onClick={() => {
              fetch('http://localhost:8000/api/games', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                  total_players: 6,
                  role_distribution: {
                    VILLAGER: 2,
                    WEREWOLF: 2,
                    SEER: 1,
                    WITCH: 1
                  }
                })
              })
                .then(res => res.json())
                .then(data => {
                  alert('游戏创建成功！\n游戏ID: ' + data.data.game_id);
                })
                .catch(err => {
                  alert('创建游戏失败：' + err.message);
                });
            }}
          >
            创建测试游戏
          </button>
        </div>
      </div>
    </ThemeProvider>
  );
}

// 主App组件，包装Provider
function App() {
  // 暂时使用简单的测试组件
  return <TestApp />;

  // 完整版本（暂时注释）
  /*
  return (
    <GameProvider>
      <ThemeProvider theme={theme}>
        <GlobalStyle />
        <AppContent />
      </ThemeProvider>
    </GameProvider>
  );
  */
}

export default App;