import React, { useState, useRef, useEffect } from 'react';
import styled from 'styled-components';
import { ChatMessage } from '../types';

interface ChatPanelProps {
  onSendMessage: (message: any) => void;
}

const PanelContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
`;

const PanelHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${props => props.theme.spacing.lg};
`;

const PanelTitle = styled.h2`
  font-size: ${props => props.theme.fontSizes.xl};
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text};
  margin: 0;
`;

const MessageCount = styled.span`
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.theme.colors.textLight};
  background: ${props => props.theme.colors.background};
  padding: ${props => props.theme.spacing.xs} ${props => props.theme.spacing.sm};
  border-radius: ${props => props.theme.borderRadius.md};
`;

const MessagesContainer = styled.div`
  flex: 1;
  background: ${props => props.theme.colors.background};
  border-radius: ${props => props.theme.borderRadius.lg};
  border: 1px solid ${props => props.theme.colors.border};
  overflow: hidden;
  display: flex;
  flex-direction: column;
`;

const MessagesList = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: ${props => props.theme.spacing.md};
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.sm};
`;

const MessageItem = styled.div<{ type: 'system' | 'player' | 'announcement' }>`
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: ${props => props.theme.fontSizes.sm};
  line-height: ${props => props.theme.lineHeights.relaxed};

  ${props => {
    switch (props.type) {
      case 'system':
        return `
          background: ${props.theme.colors.info}20;
          border-left: 3px solid ${props.theme.colors.info};
          color: ${props.theme.colors.infoDark};
        `;
      case 'announcement':
        return `
          background: ${props.theme.colors.warning}20;
          border-left: 3px solid ${props.theme.colors.warning};
          color: ${props.theme.colors.warningDark};
          font-weight: ${props.theme.fontWeights.medium};
        `;
      default:
        return `
          background: ${props.theme.colors.backgroundLight};
          border: 1px solid ${props.theme.colors.borderLight};
          color: ${props.theme.colors.text};
        `;
    }
  }}
`;

const MessageHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${props => props.theme.spacing.xs};
`;

const MessageSender = styled.span`
  font-weight: ${props => props.theme.fontWeights.medium};
  color: ${props => props.theme.colors.primary};
`;

const MessageTime = styled.span`
  font-size: ${props => props.theme.fontSizes.xs};
  color: ${props => props.theme.colors.textLight};
`;

const MessageContent = styled.div`
  word-wrap: break-word;
`;

const InputContainer = styled.div`
  margin-top: ${props => props.theme.spacing.lg};
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.sm};
`;

const InputArea = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.sm};
`;

const MessageInput = styled.textarea`
  flex: 1;
  padding: ${props => props.theme.spacing.md};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: ${props => props.theme.fontSizes.md};
  font-family: ${props => props.theme.fonts.primary};
  resize: none;
  min-height: 60px;
  max-height: 120px;
  transition: ${props => props.theme.transitions.fast};

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
    box-shadow: 0 0 0 2px ${props => props.theme.colors.primary}20;
  }

  &::placeholder {
    color: ${props => props.theme.colors.textLight};
  }
`;

const SendButton = styled.button`
  background: ${props => props.theme.colors.primary};
  color: ${props => props.theme.colors.textWhite};
  border: none;
  border-radius: ${props => props.theme.borderRadius.md};
  padding: ${props => props.theme.spacing.md} ${props => props.theme.spacing.lg};
  font-size: ${props => props.theme.fontSizes.md};
  font-weight: ${props => props.theme.fontWeights.medium};
  cursor: pointer;
  transition: ${props => props.theme.transitions.fast};
  align-self: flex-end;

  &:hover:not(:disabled) {
    background: ${props => props.theme.colors.primaryLight};
  }

  &:disabled {
    background: ${props => props.theme.colors.backgroundDark};
    color: ${props => props.theme.colors.textLight};
    cursor: not-allowed;
  }
`;

const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: ${props => props.theme.colors.textLight};
  text-align: center;
`;

const EmptyIcon = styled.div`
  font-size: 3rem;
  margin-bottom: ${props => props.theme.spacing.md};
`;

const EmptyText = styled.p`
  font-size: ${props => props.theme.fontSizes.md};
`;

const formatTime = (timestamp: string): string => {
  const date = new Date(timestamp);
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  });
};

const ChatPanel: React.FC<ChatPanelProps> = ({ onSendMessage }) => {
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: '1',
      sender: '系统',
      message: '欢迎来到狼人杀游戏！',
      timestamp: new Date().toISOString(),
      type: 'system'
    }
  ]);
  const [inputValue, setInputValue] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // 自动滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = () => {
    const trimmedMessage = inputValue.trim();
    if (!trimmedMessage) return;

    const newMessage: ChatMessage = {
      id: Date.now().toString(),
      sender: '玩家',
      message: trimmedMessage,
      timestamp: new Date().toISOString(),
      type: 'player'
    };

    setMessages(prev => [...prev, newMessage]);
    setInputValue('');

    // 发送消息到服务器
    onSendMessage({
      type: 'chat_message',
      data: {
        message: trimmedMessage
      }
    });
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const addSystemMessage = (message: string) => {
    const systemMessage: ChatMessage = {
      id: Date.now().toString(),
      sender: '系统',
      message,
      timestamp: new Date().toISOString(),
      type: 'system'
    };
    setMessages(prev => [...prev, systemMessage]);
  };

  return (
    <PanelContainer>
      <PanelHeader>
        <PanelTitle>发言记录</PanelTitle>
        <MessageCount>{messages.length}</MessageCount>
      </PanelHeader>

      <MessagesContainer>
        <MessagesList>
          {messages.length === 0 ? (
            <EmptyState>
              <EmptyIcon>💬</EmptyIcon>
              <EmptyText>暂无消息</EmptyText>
            </EmptyState>
          ) : (
            messages.map(message => (
              <MessageItem key={message.id} type={message.type}>
                <MessageHeader>
                  <MessageSender>{message.sender}</MessageSender>
                  <MessageTime>{formatTime(message.timestamp)}</MessageTime>
                </MessageHeader>
                <MessageContent>{message.message}</MessageContent>
              </MessageItem>
            ))
          )}
          <div ref={messagesEndRef} />
        </MessagesList>
      </MessagesContainer>

      <InputContainer>
        <InputArea>
          <MessageInput
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="输入发言内容... (Enter发送，Shift+Enter换行)"
            maxLength={500}
          />
        </InputArea>
        <SendButton
          onClick={handleSendMessage}
          disabled={!inputValue.trim()}
        >
          发送
        </SendButton>
      </InputContainer>
    </PanelContainer>
  );
};

export default ChatPanel;