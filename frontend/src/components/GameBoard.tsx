import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { GameState, GamePhase, GameResult, VoteType, Role } from '../types';
import { getPhaseColor } from '../styles/theme';
import VoteDialog from './dialogs/VoteDialog';
import ActionDialog from './dialogs/ActionDialog';
import { useGameContext } from '../contexts/GameContext';
import { ScaleIn, Pulse } from './animations/AnimatedComponents';
import { VoteAnimation, SkillAnimation, PhaseTransition } from './animations/GameAnimations';

interface GameBoardProps {
  gameState: GameState | null;
  onAction: (action: any) => void;
}

const BoardContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: ${props => props.theme.spacing.lg};
`;

const StatusSection = styled.section`
  background: ${props => props.theme.colors.background};
  border-radius: ${props => props.theme.borderRadius.lg};
  padding: ${props => props.theme.spacing.lg};
  border: 1px solid ${props => props.theme.colors.border};
`;

const StatusTitle = styled.h2`
  font-size: ${props => props.theme.fontSizes.xl};
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text};
  margin-bottom: ${props => props.theme.spacing.md};
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: ${props => props.theme.spacing.md};
  margin-bottom: ${props => props.theme.spacing.lg};
`;

const StatCard = styled.div`
  background: ${props => props.theme.colors.backgroundLight};
  border-radius: ${props => props.theme.borderRadius.md};
  padding: ${props => props.theme.spacing.md};
  text-align: center;
  border: 1px solid ${props => props.theme.colors.borderLight};
`;

const StatValue = styled.div`
  font-size: ${props => props.theme.fontSizes['2xl']};
  font-weight: ${props => props.theme.fontWeights.bold};
  color: ${props => props.theme.colors.primary};
  margin-bottom: ${props => props.theme.spacing.xs};
`;

const StatLabel = styled.div`
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.theme.colors.textLight};
`;

const PhaseSection = styled.section`
  background: ${props => props.theme.colors.background};
  border-radius: ${props => props.theme.borderRadius.lg};
  padding: ${props => props.theme.spacing.lg};
  border: 1px solid ${props => props.theme.colors.border};
  text-align: center;
`;

const PhaseTitle = styled.h3<{ phase?: GamePhase }>`
  font-size: ${props => props.theme.fontSizes['2xl']};
  font-weight: ${props => props.theme.fontWeights.bold};
  color: ${props => props.phase ? getPhaseColor(props.phase) : props.theme.colors.text};
  margin-bottom: ${props => props.theme.spacing.sm};
`;

const PhaseDescription = styled.p`
  font-size: ${props => props.theme.fontSizes.md};
  color: ${props => props.theme.colors.textLight};
  line-height: ${props => props.theme.lineHeights.relaxed};
  margin-bottom: ${props => props.theme.spacing.lg};
`;

const ActionSection = styled.section`
  background: ${props => props.theme.colors.background};
  border-radius: ${props => props.theme.borderRadius.lg};
  padding: ${props => props.theme.spacing.lg};
  border: 1px solid ${props => props.theme.colors.border};
  flex: 1;
`;

const ActionTitle = styled.h3`
  font-size: ${props => props.theme.fontSizes.lg};
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text};
  margin-bottom: ${props => props.theme.spacing.md};
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.md};
  margin-bottom: ${props => props.theme.spacing.lg};
  flex-wrap: wrap;
`;

const ActionButton = styled.button<{ variant?: 'primary' | 'secondary' | 'danger' | 'warning' }>`
  padding: ${props => props.theme.spacing.md} ${props => props.theme.spacing.lg};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: ${props => props.theme.fontSizes.md};
  font-weight: ${props => props.theme.fontWeights.medium};
  transition: ${props => props.theme.transitions.fast};
  min-width: 120px;

  ${props => {
    switch (props.variant) {
      case 'secondary':
        return `
          background: ${props.theme.colors.secondary};
          color: ${props.theme.colors.textWhite};
          &:hover:not(:disabled) { background: ${props.theme.colors.secondaryLight}; }
        `;
      case 'danger':
        return `
          background: ${props.theme.colors.danger};
          color: ${props.theme.colors.textWhite};
          &:hover:not(:disabled) { background: ${props.theme.colors.dangerLight}; }
        `;
      case 'warning':
        return `
          background: ${props.theme.colors.warning};
          color: ${props.theme.colors.textWhite};
          &:hover:not(:disabled) { background: ${props.theme.colors.warningLight}; }
        `;
      default:
        return `
          background: ${props.theme.colors.primary};
          color: ${props.theme.colors.textWhite};
          &:hover:not(:disabled) { background: ${props.theme.colors.primaryLight}; }
        `;
    }
  }}

  &:disabled {
    background: ${props => props.theme.colors.backgroundDark};
    color: ${props => props.theme.colors.textLight};
    cursor: not-allowed;
  }
`;

const HintArea = styled.div`
  background: ${props => props.theme.colors.backgroundLight};
  border-radius: ${props => props.theme.borderRadius.md};
  padding: ${props => props.theme.spacing.md};
  border-left: 4px solid ${props => props.theme.colors.info};
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.theme.colors.textLight};
  line-height: ${props => props.theme.lineHeights.relaxed};
`;

const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: ${props => props.theme.colors.textLight};
  text-align: center;
`;

const EmptyIcon = styled.div`
  font-size: 4rem;
  margin-bottom: ${props => props.theme.spacing.md};
`;

const EmptyText = styled.p`
  font-size: ${props => props.theme.fontSizes.lg};
  margin-bottom: ${props => props.theme.spacing.sm};
`;

const EmptySubtext = styled.p`
  font-size: ${props => props.theme.fontSizes.md};
  color: ${props => props.theme.colors.textLight};
`;

const getPhaseText = (phase: GamePhase): string => {
  const phaseTexts = {
    [GamePhase.SETUP]: '游戏设置',
    [GamePhase.NIGHT]: '夜晚阶段',
    [GamePhase.DAY_DISCUSSION]: '白天讨论',
    [GamePhase.DAY_VOTING]: '白天投票',
    [GamePhase.GAME_OVER]: '游戏结束'
  };
  return phaseTexts[phase] || '未知阶段';
};

const getPhaseDescription = (phase: GamePhase): string => {
  const descriptions = {
    [GamePhase.SETUP]: '游戏准备中，等待所有玩家加入...',
    [GamePhase.NIGHT]: '夜晚降临，狼人开始行动，特殊角色使用技能',
    [GamePhase.DAY_DISCUSSION]: '白天到来，所有玩家开始发言讨论昨夜发生的事情',
    [GamePhase.DAY_VOTING]: '投票阶段，选择要淘汰的可疑玩家',
    [GamePhase.GAME_OVER]: '游戏结束，查看最终结果'
  };
  return descriptions[phase] || '';
};

const getActionHints = (phase: GamePhase): string => {
  const hints = {
    [GamePhase.SETUP]: '等待游戏开始...',
    [GamePhase.NIGHT]: '夜晚阶段：狼人选择杀害目标，预言家查验身份，女巫使用药剂，守卫保护玩家',
    [GamePhase.DAY_DISCUSSION]: '白天讨论：所有玩家发言，分析昨夜情况，讨论可疑对象',
    [GamePhase.DAY_VOTING]: '投票阶段：选择要淘汰的玩家，点击投票按钮确认，也可以选择弃权',
    [GamePhase.GAME_OVER]: '游戏已结束，查看最终结果'
  };
  return hints[phase] || '';
};

const getResultText = (result: GameResult): string => {
  const resultTexts = {
    [GameResult.VILLAGERS_WIN]: '村民阵营胜利！',
    [GameResult.WEREWOLVES_WIN]: '狼人阵营胜利！',
    [GameResult.DRAW]: '平局！',
    [GameResult.ONGOING]: '游戏进行中'
  };
  return resultTexts[result] || '未知结果';
};

const GameBoard: React.FC<GameBoardProps> = ({ gameState, onAction }) => {
  const [selectedAction, setSelectedAction] = useState<string | null>(null);
  const [showVoteDialog, setShowVoteDialog] = useState(false);
  const [showActionDialog, setShowActionDialog] = useState(false);
  const [isPhaseTransitioning, setIsPhaseTransitioning] = useState(false);
  const [previousPhase, setPreviousPhase] = useState<GamePhase | null>(null);

  const { submitVote, submitAction } = useGameContext();

  // 监听阶段变化
  useEffect(() => {
    if (gameState && previousPhase && previousPhase !== gameState.current_phase) {
      setIsPhaseTransitioning(true);
      const timer = setTimeout(() => {
        setIsPhaseTransitioning(false);
      }, 1000);
      return () => clearTimeout(timer);
    }
    if (gameState) {
      setPreviousPhase(gameState.current_phase);
    }
  }, [gameState?.current_phase, previousPhase]);

  if (!gameState) {
    return (
      <BoardContainer>
        <EmptyState>
          <EmptyIcon>🎮</EmptyIcon>
          <EmptyText>暂无游戏</EmptyText>
          <EmptySubtext>点击右上角"创建游戏"开始新的狼人杀游戏</EmptySubtext>
        </EmptyState>
      </BoardContainer>
    );
  }

  const totalPlayers = Object.keys(gameState.players).length;
  const alivePlayers = gameState.alive_players.length;
  const deadPlayers = gameState.dead_players.length;

  // 计算阵营统计
  const villagerCount = gameState.alive_players.filter(id => {
    const player = gameState.players[id];
    return player && player.faction === 'VILLAGERS';
  }).length;

  const werewolfCount = gameState.alive_players.filter(id => {
    const player = gameState.players[id];
    return player && player.faction === 'WEREWOLVES';
  }).length;

  const handleVote = () => {
    setShowVoteDialog(true);
  };

  const handleSkill = () => {
    setShowActionDialog(true);
  };

  const handleAbstain = () => {
    submitVote(null, '选择弃权');
  };

  const handleVoteConfirm = async (targetId: number | null, reason?: string) => {
    try {
      await submitVote(targetId, reason);
      setShowVoteDialog(false);
    } catch (error) {
      console.error('Vote failed:', error);
    }
  };

  const handleActionConfirm = async (actionType: string, targetId?: number) => {
    try {
      await submitAction(actionType, targetId);
      setShowActionDialog(false);
    } catch (error) {
      console.error('Action failed:', error);
    }
  };

  const canVote = gameState.current_phase === GamePhase.DAY_VOTING;
  const canUseSkill = gameState.current_phase === GamePhase.NIGHT;
  const isGameOver = gameState.current_phase === GamePhase.GAME_OVER;

  return (
    <BoardContainer>
      {/* 游戏状态统计 */}
      <StatusSection>
        <StatusTitle>游戏状态</StatusTitle>
        <StatsGrid>
          <StatCard>
            <StatValue>{totalPlayers}</StatValue>
            <StatLabel>总玩家</StatLabel>
          </StatCard>
          <StatCard>
            <StatValue>{alivePlayers}</StatValue>
            <StatLabel>存活</StatLabel>
          </StatCard>
          <StatCard>
            <StatValue>{deadPlayers}</StatValue>
            <StatLabel>死亡</StatLabel>
          </StatCard>
          <StatCard>
            <StatValue>{villagerCount}</StatValue>
            <StatLabel>村民</StatLabel>
          </StatCard>
          <StatCard>
            <StatValue>{werewolfCount}</StatValue>
            <StatLabel>狼人</StatLabel>
          </StatCard>
        </StatsGrid>
      </StatusSection>

      {/* 当前阶段 */}
      <PhaseSection>
        <PhaseTransition isTransitioning={isPhaseTransitioning}>
          <Pulse isActive={!isGameOver}>
            <PhaseTitle phase={gameState.current_phase}>
              {getPhaseText(gameState.current_phase)}
            </PhaseTitle>
          </Pulse>
          <PhaseDescription>
            {getPhaseDescription(gameState.current_phase)}
          </PhaseDescription>

          {isGameOver && (
            <ScaleIn>
              <PhaseTitle phase={gameState.current_phase}>
                {getResultText(gameState.game_result)}
              </PhaseTitle>
            </ScaleIn>
          )}
        </PhaseTransition>
      </PhaseSection>

      {/* 操作区域 */}
      <ActionSection>
        <ActionTitle>可用操作</ActionTitle>

        <ButtonGroup>
          <VoteAnimation isVoting={showVoteDialog}>
            <ActionButton
              onClick={handleVote}
              disabled={!canVote}
              variant="primary"
            >
              投票
            </ActionButton>
          </VoteAnimation>

          <SkillAnimation isActive={showActionDialog} skillColor="#E67E22">
            <ActionButton
              onClick={handleSkill}
              disabled={!canUseSkill}
              variant="secondary"
            >
              使用技能
            </ActionButton>
          </SkillAnimation>

          <ActionButton
            onClick={handleAbstain}
            disabled={!canVote}
            variant="warning"
          >
            弃权
          </ActionButton>
        </ButtonGroup>

        <HintArea>
          {getActionHints(gameState.current_phase)}
        </HintArea>
      </ActionSection>

      {/* 投票对话框 */}
      <VoteDialog
        isOpen={showVoteDialog}
        players={gameState.players}
        voteType={VoteType.ELIMINATION}
        onClose={() => setShowVoteDialog(false)}
        onConfirm={handleVoteConfirm}
      />

      {/* 行动对话框 */}
      <ActionDialog
        isOpen={showActionDialog}
        playerRole={Role.SEER} // TODO: 获取当前玩家角色
        players={gameState.players}
        availableActions={['seer_check']} // TODO: 根据角色获取可用行动
        onClose={() => setShowActionDialog(false)}
        onConfirm={handleActionConfirm}
      />
    </BoardContainer>
  );
};

export default GameBoard;