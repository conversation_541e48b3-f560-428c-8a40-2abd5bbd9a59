import React from 'react';
import styled from 'styled-components';
import { GameState, GamePhase } from '../types';
import { getPhaseColor } from '../styles/theme';

interface HeaderProps {
  gameState: GameState | null;
  onCreateGame: () => void;
  connected: boolean;
}

const HeaderContainer = styled.header`
  background: ${props => props.theme.colors.backgroundLight};
  box-shadow: ${props => props.theme.shadows.md};
  padding: ${props => props.theme.spacing.md} ${props => props.theme.spacing.lg};
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid ${props => props.theme.colors.border};
`;

const LeftSection = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.lg};
`;

const Logo = styled.h1`
  font-size: ${props => props.theme.fontSizes['2xl']};
  font-weight: ${props => props.theme.fontWeights.bold};
  color: ${props => props.theme.colors.primary};
  margin: 0;
`;

const GameInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.xs};
`;

const GameId = styled.span`
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.theme.colors.textLight};
`;

const PhaseInfo = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
`;

const PhaseIndicator = styled.div<{ phase: GamePhase }>`
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: ${props => getPhaseColor(props.phase)};
`;

const PhaseText = styled.span<{ phase: GamePhase }>`
  font-size: ${props => props.theme.fontSizes.md};
  font-weight: ${props => props.theme.fontWeights.medium};
  color: ${props => getPhaseColor(props.phase)};
`;

const RoundInfo = styled.span`
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.theme.colors.textLight};
`;

const RightSection = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.md};
`;

const ConnectionStatus = styled.div<{ connected: boolean }>`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.xs};
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.connected ? props.theme.colors.success : props.theme.colors.danger};
`;

const StatusDot = styled.div<{ connected: boolean }>`
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: ${props => props.connected ? props.theme.colors.success : props.theme.colors.danger};
`;

const ActionButton = styled.button`
  background: ${props => props.theme.colors.primary};
  color: ${props => props.theme.colors.textWhite};
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: ${props => props.theme.fontSizes.sm};
  font-weight: ${props => props.theme.fontWeights.medium};
  transition: ${props => props.theme.transitions.fast};

  &:hover {
    background: ${props => props.theme.colors.primaryLight};
  }

  &:disabled {
    background: ${props => props.theme.colors.backgroundDark};
    color: ${props => props.theme.colors.textLight};
    cursor: not-allowed;
  }
`;

const getPhaseText = (phase: GamePhase): string => {
  const phaseTexts = {
    [GamePhase.SETUP]: '游戏设置',
    [GamePhase.NIGHT]: '夜晚阶段',
    [GamePhase.DAY_DISCUSSION]: '白天讨论',
    [GamePhase.DAY_VOTING]: '白天投票',
    [GamePhase.GAME_OVER]: '游戏结束'
  };
  return phaseTexts[phase] || '未知阶段';
};

const Header: React.FC<HeaderProps> = ({ gameState, onCreateGame, connected }) => {
  const showCreateButton = !gameState || gameState.current_phase === GamePhase.GAME_OVER;

  return (
    <HeaderContainer>
      <LeftSection>
        <Logo>🐺 狼人杀</Logo>

        {gameState && (
          <GameInfo>
            <GameId>游戏ID: {gameState.game_id}</GameId>
            <PhaseInfo>
              <PhaseIndicator phase={gameState.current_phase} />
              <PhaseText phase={gameState.current_phase}>
                {getPhaseText(gameState.current_phase)}
              </PhaseText>
              <RoundInfo>第 {gameState.current_round} 回合</RoundInfo>
            </PhaseInfo>
          </GameInfo>
        )}
      </LeftSection>

      <RightSection>
        <ConnectionStatus connected={connected}>
          <StatusDot connected={connected} />
          {connected ? '已连接' : '连接断开'}
        </ConnectionStatus>

        {showCreateButton && (
          <ActionButton onClick={onCreateGame} disabled={!connected}>
            {gameState ? '开始新游戏' : '创建游戏'}
          </ActionButton>
        )}
      </RightSection>
    </HeaderContainer>
  );
};

export default Header;