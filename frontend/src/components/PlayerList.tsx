import React, { useState } from 'react';
import styled from 'styled-components';
import { Player, GamePhase, Role } from '../types';
import { getRoleColor } from '../styles/theme';
import { StaggeredList, FadeIn } from './animations/AnimatedComponents';
import { DeathAnimation, PlayerCardFlip } from './animations/GameAnimations';

interface PlayerListProps {
  players: { [key: number]: Player };
  currentPhase: GamePhase;
  onPlayerSelect?: (playerId: number) => void;
}

const ListContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
`;

const ListHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${props => props.theme.spacing.lg};
`;

const ListTitle = styled.h2`
  font-size: ${props => props.theme.fontSizes.xl};
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text};
  margin: 0;
`;

const PlayerCount = styled.span`
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.theme.colors.textLight};
  background: ${props => props.theme.colors.background};
  padding: ${props => props.theme.spacing.xs} ${props => props.theme.spacing.sm};
  border-radius: ${props => props.theme.borderRadius.md};
`;

const PlayersGrid = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.sm};
  flex: 1;
  overflow-y: auto;
`;

const PlayerCard = styled.div<{ isAlive: boolean; isSelected: boolean }>`
  background: ${props => props.isAlive ? props.theme.colors.backgroundLight : props.theme.colors.backgroundDark};
  border: 2px solid ${props => props.isSelected ? props.theme.colors.primary : props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.lg};
  padding: ${props => props.theme.spacing.md};
  cursor: pointer;
  transition: ${props => props.theme.transitions.fast};
  opacity: ${props => props.isAlive ? 1 : 0.6};

  &:hover {
    border-color: ${props => props.isSelected ? props.theme.colors.primary : props.theme.colors.primaryLight};
    box-shadow: ${props => props.theme.shadows.md};
  }
`;

const PlayerHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${props => props.theme.spacing.sm};
`;

const PlayerName = styled.h3`
  font-size: ${props => props.theme.fontSizes.md};
  font-weight: ${props => props.theme.fontWeights.medium};
  color: ${props => props.theme.colors.text};
  margin: 0;
`;

const StatusIndicator = styled.div<{ isAlive: boolean }>`
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: ${props => props.isAlive ? props.theme.colors.success : props.theme.colors.danger};
`;

const PlayerInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.xs};
`;

const RoleInfo = styled.div<{ role: Role; showRole: boolean }>`
  display: ${props => props.showRole ? 'flex' : 'none'};
  align-items: center;
  gap: ${props => props.theme.spacing.xs};
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => getRoleColor(props.role)};
  font-weight: ${props => props.theme.fontWeights.medium};
`;

const RoleIcon = styled.span`
  font-size: ${props => props.theme.fontSizes.md};
`;

const StatusText = styled.span<{ isAlive: boolean }>`
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.isAlive ? props.theme.colors.success : props.theme.colors.danger};
  font-weight: ${props => props.theme.fontWeights.medium};
`;

const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: ${props => props.theme.colors.textLight};
  text-align: center;
`;

const EmptyIcon = styled.div`
  font-size: 3rem;
  margin-bottom: ${props => props.theme.spacing.md};
`;

const EmptyText = styled.p`
  font-size: ${props => props.theme.fontSizes.md};
`;

const getRoleIcon = (role: Role): string => {
  const roleIcons = {
    [Role.VILLAGER]: '👨‍🌾',
    [Role.WEREWOLF]: '🐺',
    [Role.SEER]: '🔮',
    [Role.WITCH]: '🧙‍♀️',
    [Role.GUARD]: '🛡️',
    [Role.HUNTER]: '🏹'
  };
  return roleIcons[role] || '❓';
};

const getRoleName = (role: Role): string => {
  const roleNames = {
    [Role.VILLAGER]: '村民',
    [Role.WEREWOLF]: '狼人',
    [Role.SEER]: '预言家',
    [Role.WITCH]: '女巫',
    [Role.GUARD]: '守卫',
    [Role.HUNTER]: '猎人'
  };
  return roleNames[role] || '未知角色';
};

const PlayerList: React.FC<PlayerListProps> = ({
  players,
  currentPhase,
  onPlayerSelect
}) => {
  const [selectedPlayerId, setSelectedPlayerId] = useState<number | null>(null);

  const playerList = Object.values(players);
  const aliveCount = playerList.filter(p => p.status === 'ALIVE').length;
  const totalCount = playerList.length;

  // 根据游戏阶段决定是否显示角色
  const showRoles = currentPhase === GamePhase.GAME_OVER;

  const handlePlayerClick = (playerId: number) => {
    setSelectedPlayerId(playerId);
    onPlayerSelect?.(playerId);
  };

  if (playerList.length === 0) {
    return (
      <ListContainer>
        <ListHeader>
          <ListTitle>玩家列表</ListTitle>
          <PlayerCount>0/0</PlayerCount>
        </ListHeader>
        <EmptyState>
          <EmptyIcon>👥</EmptyIcon>
          <EmptyText>暂无玩家</EmptyText>
        </EmptyState>
      </ListContainer>
    );
  }

  // 按状态排序：存活玩家在前
  const sortedPlayers = [...playerList].sort((a, b) => {
    if (a.status === 'ALIVE' && b.status !== 'ALIVE') return -1;
    if (a.status !== 'ALIVE' && b.status === 'ALIVE') return 1;
    return a.player_id - b.player_id;
  });

  return (
    <ListContainer>
      <ListHeader>
        <ListTitle>玩家列表</ListTitle>
        <PlayerCount>{aliveCount}/{totalCount}</PlayerCount>
      </ListHeader>

      <PlayersGrid>
        <StaggeredList staggerDelay={0.1}>
          {sortedPlayers.map(player => (
            <DeathAnimation
              key={player.player_id}
              isDead={player.status !== 'ALIVE'}
            >
              <PlayerCardFlip
                isRevealed={showRoles}
                backContent={
                  <PlayerCard
                    isAlive={player.status === 'ALIVE'}
                    isSelected={selectedPlayerId === player.player_id}
                    onClick={() => handlePlayerClick(player.player_id)}
                  >
                    <PlayerHeader>
                      <PlayerName>{player.name}</PlayerName>
                      <StatusIndicator isAlive={player.status === 'ALIVE'} />
                    </PlayerHeader>
                    <PlayerInfo>
                      <RoleInfo role={player.role} showRole={true}>
                        <RoleIcon>{getRoleIcon(player.role)}</RoleIcon>
                        {getRoleName(player.role)}
                      </RoleInfo>
                      <StatusText isAlive={player.status === 'ALIVE'}>
                        {player.status === 'ALIVE' ? '存活' : '死亡'}
                      </StatusText>
                    </PlayerInfo>
                  </PlayerCard>
                }
              >
                <PlayerCard
                  isAlive={player.status === 'ALIVE'}
                  isSelected={selectedPlayerId === player.player_id}
                  onClick={() => handlePlayerClick(player.player_id)}
                >
                  <PlayerHeader>
                    <PlayerName>{player.name}</PlayerName>
                    <StatusIndicator isAlive={player.status === 'ALIVE'} />
                  </PlayerHeader>

                  <PlayerInfo>
                    <RoleInfo role={player.role} showRole={showRoles && !showRoles}>
                      <RoleIcon>{getRoleIcon(player.role)}</RoleIcon>
                      {getRoleName(player.role)}
                    </RoleInfo>

                    <StatusText isAlive={player.status === 'ALIVE'}>
                      {player.status === 'ALIVE' ? '存活' : '死亡'}
                    </StatusText>
                  </PlayerInfo>
                </PlayerCard>
              </PlayerCardFlip>
            </DeathAnimation>
          ))}
        </StaggeredList>
      </PlayersGrid>
    </ListContainer>
  );
};

export default PlayerList;