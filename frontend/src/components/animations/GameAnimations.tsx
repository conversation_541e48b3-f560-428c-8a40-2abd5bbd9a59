import React, { ReactNode, useState, useEffect } from 'react';
import styled, { css, keyframes } from 'styled-components';
import { animations } from '../../styles/animations';

// 玩家卡片翻转动画
const cardFlipAnimation = keyframes`
  0% {
    transform: rotateY(0);
  }
  50% {
    transform: rotateY(90deg);
  }
  100% {
    transform: rotateY(0);
  }
`;

const PlayerCardContainer = styled.div<{ isFlipping?: boolean; isRevealed?: boolean }>`
  perspective: 1000px;

  ${props => props.isFlipping && css`
    animation: ${cardFlipAnimation} 0.6s ease-in-out;
  `}

  ${props => props.isRevealed && css`
    .card-back {
      transform: rotateY(180deg);
    }
    .card-front {
      transform: rotateY(0);
    }
  `}
`;

const CardSide = styled.div`
  backface-visibility: hidden;
  transition: transform 0.6s;

  &.card-front {
    transform: rotateY(0);
  }

  &.card-back {
    transform: rotateY(180deg);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
`;

interface PlayerCardFlipProps {
  children: ReactNode;
  backContent?: ReactNode;
  isRevealed?: boolean;
  onFlipComplete?: () => void;
}

export const PlayerCardFlip: React.FC<PlayerCardFlipProps> = ({
  children,
  backContent,
  isRevealed = false,
  onFlipComplete
}) => {
  const [isFlipping, setIsFlipping] = useState(false);

  useEffect(() => {
    if (isRevealed) {
      setIsFlipping(true);
      const timer = setTimeout(() => {
        setIsFlipping(false);
        onFlipComplete?.();
      }, 600);
      return () => clearTimeout(timer);
    }
  }, [isRevealed, onFlipComplete]);

  return (
    <PlayerCardContainer isFlipping={isFlipping} isRevealed={isRevealed}>
      <CardSide className="card-front">
        {children}
      </CardSide>
      {backContent && (
        <CardSide className="card-back">
          {backContent}
        </CardSide>
      )}
    </PlayerCardContainer>
  );
};

// 投票动画
const voteAnimation = keyframes`
  0% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
  50% {
    transform: scale(1.2) rotate(5deg);
    opacity: 0.8;
  }
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
`;

const VoteContainer = styled.div<{ isVoting?: boolean }>`
  ${props => props.isVoting && css`
    animation: ${voteAnimation} 0.5s ease-in-out;
  `}
`;

interface VoteAnimationProps {
  children: ReactNode;
  isVoting?: boolean;
  className?: string;
}

export const VoteAnimation: React.FC<VoteAnimationProps> = ({
  children,
  isVoting = false,
  className
}) => (
  <VoteContainer isVoting={isVoting} className={className}>
    {children}
  </VoteContainer>
);

// 死亡动画
const deathAnimation = keyframes`
  0% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
    filter: grayscale(0%);
  }
  50% {
    transform: scale(0.9) rotate(-2deg);
    opacity: 0.7;
    filter: grayscale(50%);
  }
  100% {
    transform: scale(0.95) rotate(0deg);
    opacity: 0.5;
    filter: grayscale(100%);
  }
`;

const DeathContainer = styled.div<{ isDead?: boolean }>`
  transition: all 0.5s ease;

  ${props => props.isDead && css`
    animation: ${deathAnimation} 1s ease-in-out forwards;
    pointer-events: none;
  `}
`;

interface DeathAnimationProps {
  children: ReactNode;
  isDead?: boolean;
  className?: string;
}

export const DeathAnimation: React.FC<DeathAnimationProps> = ({
  children,
  isDead = false,
  className
}) => (
  <DeathContainer isDead={isDead} className={className}>
    {children}
  </DeathContainer>
);

// 技能使用动画
const skillAnimation = keyframes`
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(52, 152, 219, 0.7);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(52, 152, 219, 0.3);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 20px rgba(52, 152, 219, 0);
  }
`;

const SkillContainer = styled.div<{ isActive?: boolean; skillColor?: string }>`
  ${props => props.isActive && css`
    animation: ${skillAnimation} 0.8s ease-out;
    ${props.skillColor && css`
      box-shadow: 0 0 0 0 ${props.skillColor}70;
    `}
  `}
`;

interface SkillAnimationProps {
  children: ReactNode;
  isActive?: boolean;
  skillColor?: string;
  className?: string;
}

export const SkillAnimation: React.FC<SkillAnimationProps> = ({
  children,
  isActive = false,
  skillColor,
  className
}) => (
  <SkillContainer isActive={isActive} skillColor={skillColor} className={className}>
    {children}
  </SkillContainer>
);

// 阶段切换动画
const phaseTransition = keyframes`
  0% {
    opacity: 1;
    transform: translateY(0);
  }
  50% {
    opacity: 0;
    transform: translateY(-20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
`;

const PhaseContainer = styled.div<{ isTransitioning?: boolean }>`
  ${props => props.isTransitioning && css`
    animation: ${phaseTransition} 1s ease-in-out;
  `}
`;

interface PhaseTransitionProps {
  children: ReactNode;
  isTransitioning?: boolean;
  className?: string;
}

export const PhaseTransition: React.FC<PhaseTransitionProps> = ({
  children,
  isTransitioning = false,
  className
}) => (
  <PhaseContainer isTransitioning={isTransitioning} className={className}>
    {children}
  </PhaseContainer>
);

// 消息气泡动画
const bubbleAnimation = keyframes`
  0% {
    transform: scale(0) translateY(20px);
    opacity: 0;
  }
  50% {
    transform: scale(1.1) translateY(-5px);
    opacity: 0.8;
  }
  100% {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
`;

const BubbleContainer = styled.div<{ isAppearing?: boolean }>`
  ${props => props.isAppearing && css`
    animation: ${bubbleAnimation} 0.4s ease-out;
  `}
`;

interface MessageBubbleProps {
  children: ReactNode;
  isAppearing?: boolean;
  className?: string;
}

export const MessageBubble: React.FC<MessageBubbleProps> = ({
  children,
  isAppearing = false,
  className
}) => (
  <BubbleContainer isAppearing={isAppearing} className={className}>
    {children}
  </BubbleContainer>
);

// 计时器动画
const timerAnimation = keyframes`
  0% {
    stroke-dasharray: 0 100;
  }
  100% {
    stroke-dasharray: 100 100;
  }
`;

const TimerContainer = styled.div`
  position: relative;
  display: inline-block;
`;

const TimerSvg = styled.svg<{ duration?: number; isActive?: boolean }>`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;

  circle {
    fill: none;
    stroke: ${props => props.theme.colors.primary};
    stroke-width: 2;
    stroke-linecap: round;
    transform: rotate(-90deg);
    transform-origin: 50% 50%;

    ${props => props.isActive && css`
      animation: ${timerAnimation} ${props.duration || 30}s linear forwards;
    `}
  }
`;

interface TimerAnimationProps {
  children: ReactNode;
  duration?: number; // 秒
  isActive?: boolean;
  className?: string;
}

export const TimerAnimation: React.FC<TimerAnimationProps> = ({
  children,
  duration = 30,
  isActive = false,
  className
}) => (
  <TimerContainer className={className}>
    {children}
    <TimerSvg duration={duration} isActive={isActive} viewBox="0 0 40 40">
      <circle cx="20" cy="20" r="18" strokeDasharray="0 100" />
    </TimerSvg>
  </TimerContainer>
);