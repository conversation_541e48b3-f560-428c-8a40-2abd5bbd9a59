import React, { useState } from 'react';
import styled from 'styled-components';
import { Player, Role } from '../../types';
import { getRoleColor } from '../../styles/theme';

interface ActionDialogProps {
  isOpen: boolean;
  playerRole: Role;
  players: { [key: number]: Player };
  availableActions: string[];
  onClose: () => void;
  onConfirm: (actionType: string, targetId?: number) => void;
}

const Overlay = styled.div<{ isOpen: boolean }>`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: ${props => props.isOpen ? 'flex' : 'none'};
  justify-content: center;
  align-items: center;
  z-index: ${props => props.theme.zIndex.modal};
`;

const DialogContainer = styled.div`
  background: ${props => props.theme.colors.backgroundLight};
  border-radius: ${props => props.theme.borderRadius.xl};
  box-shadow: ${props => props.theme.shadows.xl};
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
`;

const DialogHeader = styled.div<{ roleColor: string }>`
  padding: ${props => props.theme.spacing.lg};
  border-bottom: 1px solid ${props => props.theme.colors.border};
  background: linear-gradient(135deg, ${props => props.roleColor}20, ${props => props.theme.colors.background});
`;

const RoleIcon = styled.div`
  font-size: 2rem;
  margin-bottom: ${props => props.theme.spacing.sm};
`;

const DialogTitle = styled.h2<{ roleColor: string }>`
  font-size: ${props => props.theme.fontSizes.xl};
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.roleColor};
  margin: 0 0 ${props => props.theme.spacing.sm} 0;
`;

const DialogDescription = styled.p`
  font-size: ${props => props.theme.fontSizes.md};
  color: ${props => props.theme.colors.textLight};
  margin: 0;
  line-height: ${props => props.theme.lineHeights.relaxed};
`;

const DialogBody = styled.div`
  padding: ${props => props.theme.spacing.lg};
  flex: 1;
  overflow-y: auto;
`;

const ActionSection = styled.div`
  margin-bottom: ${props => props.theme.spacing.xl};
`;

const SectionTitle = styled.h3`
  font-size: ${props => props.theme.fontSizes.lg};
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text};
  margin: 0 0 ${props => props.theme.spacing.md} 0;
`;

const ActionGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: ${props => props.theme.spacing.sm};
`;

const ActionOption = styled.div<{ isSelected: boolean; isAvailable: boolean }>`
  display: flex;
  align-items: center;
  padding: ${props => props.theme.spacing.md};
  border: 2px solid ${props => props.isSelected ? props.theme.colors.primary : props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.lg};
  background: ${props => props.isSelected ? props.theme.colors.primary + '10' : props.theme.colors.backgroundLight};
  cursor: ${props => props.isAvailable ? 'pointer' : 'not-allowed'};
  opacity: ${props => props.isAvailable ? 1 : 0.5};
  transition: ${props => props.theme.transitions.fast};

  &:hover {
    border-color: ${props => props.isAvailable ? props.theme.colors.primary : props.theme.colors.border};
    background: ${props => props.isAvailable ? props.theme.colors.primary + '20' : props.theme.colors.backgroundLight};
  }
`;

const ActionInfo = styled.div`
  flex: 1;
`;

const ActionName = styled.div`
  font-size: ${props => props.theme.fontSizes.md};
  font-weight: ${props => props.theme.fontWeights.medium};
  color: ${props => props.theme.colors.text};
  margin-bottom: ${props => props.theme.spacing.xs};
`;

const ActionDescription = styled.div`
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.theme.colors.textLight};
  line-height: ${props => props.theme.lineHeights.relaxed};
`;

const TargetSection = styled.div<{ show: boolean }>`
  display: ${props => props.show ? 'block' : 'none'};
  margin-bottom: ${props => props.theme.spacing.lg};
`;

const PlayerGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: ${props => props.theme.spacing.sm};
`;

const PlayerOption = styled.div<{ isSelected: boolean; isTargetable: boolean }>`
  display: flex;
  align-items: center;
  padding: ${props => props.theme.spacing.md};
  border: 2px solid ${props => props.isSelected ? props.theme.colors.secondary : props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.lg};
  background: ${props => props.isSelected ? props.theme.colors.secondary + '10' : props.theme.colors.backgroundLight};
  cursor: ${props => props.isTargetable ? 'pointer' : 'not-allowed'};
  opacity: ${props => props.isTargetable ? 1 : 0.5};
  transition: ${props => props.theme.transitions.fast};

  &:hover {
    border-color: ${props => props.isTargetable ? props.theme.colors.secondary : props.theme.colors.border};
    background: ${props => props.isTargetable ? props.theme.colors.secondary + '20' : props.theme.colors.backgroundLight};
  }
`;

const PlayerInfo = styled.div`
  flex: 1;
`;

const PlayerName = styled.div`
  font-size: ${props => props.theme.fontSizes.md};
  font-weight: ${props => props.theme.fontWeights.medium};
  color: ${props => props.theme.colors.text};
  margin-bottom: ${props => props.theme.spacing.xs};
`;

const PlayerStatus = styled.div<{ isAlive: boolean }>`
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.isAlive ? props.theme.colors.success : props.theme.colors.danger};
`;

const StatusIndicator = styled.div<{ isAlive: boolean }>`
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: ${props => props.isAlive ? props.theme.colors.success : props.theme.colors.danger};
  margin-left: ${props => props.theme.spacing.md};
`;

const DialogFooter = styled.div`
  padding: ${props => props.theme.spacing.lg};
  border-top: 1px solid ${props => props.theme.colors.border};
  display: flex;
  gap: ${props => props.theme.spacing.md};
  justify-content: flex-end;
`;

const Button = styled.button<{ variant?: 'primary' | 'secondary' | 'warning' }>`
  padding: ${props => props.theme.spacing.md} ${props => props.theme.spacing.lg};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: ${props => props.theme.fontSizes.md};
  font-weight: ${props => props.theme.fontWeights.medium};
  border: none;
  cursor: pointer;
  transition: ${props => props.theme.transitions.fast};
  min-width: 100px;

  ${props => {
    switch (props.variant) {
      case 'warning':
        return `
          background: ${props.theme.colors.warning};
          color: ${props.theme.colors.textWhite};
          &:hover:not(:disabled) { background: ${props.theme.colors.warningLight}; }
        `;
      case 'secondary':
        return `
          background: ${props.theme.colors.backgroundDark};
          color: ${props.theme.colors.text};
          &:hover:not(:disabled) { background: ${props.theme.colors.border}; }
        `;
      default:
        return `
          background: ${props.theme.colors.primary};
          color: ${props.theme.colors.textWhite};
          &:hover:not(:disabled) { background: ${props.theme.colors.primaryLight}; }
        `;
    }
  }}

  &:disabled {
    background: ${props => props.theme.colors.backgroundDark};
    color: ${props => props.theme.colors.textLight};
    cursor: not-allowed;
  }
`;

const getRoleIcon = (role: Role): string => {
  const roleIcons = {
    [Role.VILLAGER]: '👨‍🌾',
    [Role.WEREWOLF]: '🐺',
    [Role.SEER]: '🔮',
    [Role.WITCH]: '🧙‍♀️',
    [Role.GUARD]: '🛡️',
    [Role.HUNTER]: '🏹'
  };
  return roleIcons[role] || '❓';
};

const getRoleName = (role: Role): string => {
  const roleNames = {
    [Role.VILLAGER]: '村民',
    [Role.WEREWOLF]: '狼人',
    [Role.SEER]: '预言家',
    [Role.WITCH]: '女巫',
    [Role.GUARD]: '守卫',
    [Role.HUNTER]: '猎人'
  };
  return roleNames[role] || '未知角色';
};

const getRoleDescription = (role: Role): string => {
  const descriptions = {
    [Role.VILLAGER]: '普通村民，白天参与讨论和投票',
    [Role.WEREWOLF]: '夜晚杀人，白天伪装成村民',
    [Role.SEER]: '每晚可以查验一个人的身份',
    [Role.WITCH]: '拥有一瓶解药和一瓶毒药，各用一次',
    [Role.GUARD]: '每晚可以保护一个人不被狼人杀死',
    [Role.HUNTER]: '被淘汰时可以开枪带走一个人'
  };
  return descriptions[role] || '';
};

const getActionInfo = (actionType: string) => {
  const actionMap: { [key: string]: { name: string; description: string } } = {
    'seer_check': {
      name: '查验身份',
      description: '查看目标玩家的真实身份（村民阵营或狼人阵营）'
    },
    'witch_save': {
      name: '使用解药',
      description: '救治被狼人杀害的玩家，每局游戏只能使用一次'
    },
    'witch_poison': {
      name: '使用毒药',
      description: '毒杀一个玩家，每局游戏只能使用一次'
    },
    'guard_protect': {
      name: '保护玩家',
      description: '保护目标玩家免受狼人攻击，不能连续两晚保护同一人'
    },
    'werewolf_kill': {
      name: '杀害玩家',
      description: '选择要杀害的目标，需要狼人阵营达成一致'
    },
    'hunter_shoot': {
      name: '猎人开枪',
      description: '被淘汰时可以开枪带走一个玩家'
    },
    'skip': {
      name: '跳过行动',
      description: '本回合不执行任何特殊行动'
    }
  };

  return actionMap[actionType] || { name: actionType, description: '未知行动' };
};

const canTargetPlayer = (player: Player, actionType: string, playerRole: Role): boolean => {
  switch (actionType) {
    case 'seer_check':
    case 'guard_protect':
      return player.status === 'ALIVE';
    case 'werewolf_kill':
      return player.status === 'ALIVE' && player.role !== Role.WEREWOLF;
    case 'witch_poison':
    case 'hunter_shoot':
      return player.status === 'ALIVE';
    case 'witch_save':
      // 女巫救人的逻辑可能需要特殊处理
      return true;
    default:
      return player.status === 'ALIVE';
  }
};

const ActionDialog: React.FC<ActionDialogProps> = ({
  isOpen,
  playerRole,
  players,
  availableActions,
  onClose,
  onConfirm
}) => {
  const [selectedAction, setSelectedAction] = useState<string>('skip');
  const [selectedTargetId, setSelectedTargetId] = useState<number | null>(null);

  const roleColor = getRoleColor(playerRole.toString());
  const playerList = Object.values(players);
  const needsTarget = selectedAction !== 'skip';

  const handleActionSelect = (actionType: string) => {
    setSelectedAction(actionType);
    setSelectedTargetId(null);
  };

  const handleTargetSelect = (playerId: number) => {
    const player = players[playerId];
    if (player && canTargetPlayer(player, selectedAction, playerRole)) {
      setSelectedTargetId(playerId);
    }
  };

  const handleConfirm = () => {
    if (selectedAction === 'skip') {
      onConfirm('skip');
    } else if (needsTarget && selectedTargetId) {
      onConfirm(selectedAction, selectedTargetId);
    } else if (!needsTarget) {
      onConfirm(selectedAction);
    }
    handleClose();
  };

  const handleSkip = () => {
    onConfirm('skip');
    handleClose();
  };

  const handleClose = () => {
    setSelectedAction('skip');
    setSelectedTargetId(null);
    onClose();
  };

  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      handleClose();
    }
  };

  const canConfirm = selectedAction === 'skip' || !needsTarget || selectedTargetId !== null;

  if (!isOpen) return null;

  return (
    <Overlay isOpen={isOpen} onClick={handleOverlayClick}>
      <DialogContainer>
        <DialogHeader roleColor={roleColor}>
          <RoleIcon>{getRoleIcon(playerRole)}</RoleIcon>
          <DialogTitle roleColor={roleColor}>
            {getRoleName(playerRole)} - 夜晚行动
          </DialogTitle>
          <DialogDescription>
            {getRoleDescription(playerRole)}
          </DialogDescription>
        </DialogHeader>

        <DialogBody>
          <ActionSection>
            <SectionTitle>选择行动</SectionTitle>
            <ActionGrid>
              {availableActions.map(actionType => {
                const actionInfo = getActionInfo(actionType);
                return (
                  <ActionOption
                    key={actionType}
                    isSelected={selectedAction === actionType}
                    isAvailable={true}
                    onClick={() => handleActionSelect(actionType)}
                  >
                    <ActionInfo>
                      <ActionName>{actionInfo.name}</ActionName>
                      <ActionDescription>{actionInfo.description}</ActionDescription>
                    </ActionInfo>
                  </ActionOption>
                );
              })}

              <ActionOption
                isSelected={selectedAction === 'skip'}
                isAvailable={true}
                onClick={() => handleActionSelect('skip')}
              >
                <ActionInfo>
                  <ActionName>跳过行动</ActionName>
                  <ActionDescription>本回合不执行任何特殊行动</ActionDescription>
                </ActionInfo>
              </ActionOption>
            </ActionGrid>
          </ActionSection>

          <TargetSection show={needsTarget}>
            <SectionTitle>选择目标</SectionTitle>
            <PlayerGrid>
              {playerList
                .filter(player => canTargetPlayer(player, selectedAction, playerRole))
                .map(player => (
                  <PlayerOption
                    key={player.player_id}
                    isSelected={selectedTargetId === player.player_id}
                    isTargetable={canTargetPlayer(player, selectedAction, playerRole)}
                    onClick={() => handleTargetSelect(player.player_id)}
                  >
                    <PlayerInfo>
                      <PlayerName>{player.name}</PlayerName>
                      <PlayerStatus isAlive={player.status === 'ALIVE'}>
                        {player.status === 'ALIVE' ? '存活' : '死亡'}
                      </PlayerStatus>
                    </PlayerInfo>
                    <StatusIndicator isAlive={player.status === 'ALIVE'} />
                  </PlayerOption>
                ))}
            </PlayerGrid>
          </TargetSection>
        </DialogBody>

        <DialogFooter>
          <Button variant="secondary" onClick={handleClose}>
            取消
          </Button>

          <Button variant="warning" onClick={handleSkip}>
            跳过
          </Button>

          <Button
            variant="primary"
            onClick={handleConfirm}
            disabled={!canConfirm}
          >
            确认行动
          </Button>
        </DialogFooter>
      </DialogContainer>
    </Overlay>
  );
};

export default ActionDialog;