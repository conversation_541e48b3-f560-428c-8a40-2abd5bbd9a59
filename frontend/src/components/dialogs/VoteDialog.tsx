import React, { useState } from 'react';
import styled from 'styled-components';
import { Player, VoteType } from '../../types';
import { getRoleColor } from '../../styles/theme';

interface VoteDialogProps {
  isOpen: boolean;
  players: { [key: number]: Player };
  voteType: VoteType;
  onClose: () => void;
  onConfirm: (targetId: number | null, reason?: string) => void;
}

const Overlay = styled.div<{ isOpen: boolean }>`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: ${props => props.isOpen ? 'flex' : 'none'};
  justify-content: center;
  align-items: center;
  z-index: ${props => props.theme.zIndex.modal};
`;

const DialogContainer = styled.div`
  background: ${props => props.theme.colors.backgroundLight};
  border-radius: ${props => props.theme.borderRadius.xl};
  box-shadow: ${props => props.theme.shadows.xl};
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
`;

const DialogHeader = styled.div`
  padding: ${props => props.theme.spacing.lg};
  border-bottom: 1px solid ${props => props.theme.colors.border};
  background: ${props => props.theme.colors.background};
`;

const DialogTitle = styled.h2`
  font-size: ${props => props.theme.fontSizes.xl};
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text};
  margin: 0 0 ${props => props.theme.spacing.sm} 0;
`;

const DialogDescription = styled.p`
  font-size: ${props => props.theme.fontSizes.md};
  color: ${props => props.theme.colors.textLight};
  margin: 0;
  line-height: ${props => props.theme.lineHeights.relaxed};
`;

const DialogBody = styled.div`
  padding: ${props => props.theme.spacing.lg};
  flex: 1;
  overflow-y: auto;
`;

const PlayerGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: ${props => props.theme.spacing.sm};
  margin-bottom: ${props => props.theme.spacing.lg};
`;

const PlayerOption = styled.div<{ isSelected: boolean; isAlive: boolean }>`
  display: flex;
  align-items: center;
  padding: ${props => props.theme.spacing.md};
  border: 2px solid ${props => props.isSelected ? props.theme.colors.primary : props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.lg};
  background: ${props => props.isSelected ? props.theme.colors.primary + '10' : props.theme.colors.backgroundLight};
  cursor: ${props => props.isAlive ? 'pointer' : 'not-allowed'};
  opacity: ${props => props.isAlive ? 1 : 0.5};
  transition: ${props => props.theme.transitions.fast};

  &:hover {
    border-color: ${props => props.isAlive ? props.theme.colors.primary : props.theme.colors.border};
    background: ${props => props.isAlive ? props.theme.colors.primary + '20' : props.theme.colors.backgroundLight};
  }
`;

const PlayerInfo = styled.div`
  flex: 1;
`;

const PlayerName = styled.div`
  font-size: ${props => props.theme.fontSizes.md};
  font-weight: ${props => props.theme.fontWeights.medium};
  color: ${props => props.theme.colors.text};
  margin-bottom: ${props => props.theme.spacing.xs};
`;

const PlayerStatus = styled.div<{ isAlive: boolean }>`
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.isAlive ? props.theme.colors.success : props.theme.colors.danger};
`;

const StatusIndicator = styled.div<{ isAlive: boolean }>`
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: ${props => props.isAlive ? props.theme.colors.success : props.theme.colors.danger};
  margin-left: ${props => props.theme.spacing.md};
`;

const ReasonSection = styled.div`
  margin-bottom: ${props => props.theme.spacing.lg};
`;

const ReasonLabel = styled.label`
  display: block;
  font-size: ${props => props.theme.fontSizes.md};
  font-weight: ${props => props.theme.fontWeights.medium};
  color: ${props => props.theme.colors.text};
  margin-bottom: ${props => props.theme.spacing.sm};
`;

const ReasonInput = styled.textarea`
  width: 100%;
  padding: ${props => props.theme.spacing.md};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: ${props => props.theme.fontSizes.md};
  font-family: ${props => props.theme.fonts.primary};
  resize: vertical;
  min-height: 80px;
  transition: ${props => props.theme.transitions.fast};

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
    box-shadow: 0 0 0 2px ${props => props.theme.colors.primary}20;
  }

  &::placeholder {
    color: ${props => props.theme.colors.textLight};
  }
`;

const DialogFooter = styled.div`
  padding: ${props => props.theme.spacing.lg};
  border-top: 1px solid ${props => props.theme.colors.border};
  display: flex;
  gap: ${props => props.theme.spacing.md};
  justify-content: flex-end;
`;

const Button = styled.button<{ variant?: 'primary' | 'secondary' | 'danger' }>`
  padding: ${props => props.theme.spacing.md} ${props => props.theme.spacing.lg};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: ${props => props.theme.fontSizes.md};
  font-weight: ${props => props.theme.fontWeights.medium};
  border: none;
  cursor: pointer;
  transition: ${props => props.theme.transitions.fast};
  min-width: 100px;

  ${props => {
    switch (props.variant) {
      case 'danger':
        return `
          background: ${props.theme.colors.danger};
          color: ${props.theme.colors.textWhite};
          &:hover:not(:disabled) { background: ${props.theme.colors.dangerLight}; }
        `;
      case 'secondary':
        return `
          background: ${props.theme.colors.backgroundDark};
          color: ${props.theme.colors.text};
          &:hover:not(:disabled) { background: ${props.theme.colors.border}; }
        `;
      default:
        return `
          background: ${props.theme.colors.primary};
          color: ${props.theme.colors.textWhite};
          &:hover:not(:disabled) { background: ${props.theme.colors.primaryLight}; }
        `;
    }
  }}

  &:disabled {
    background: ${props => props.theme.colors.backgroundDark};
    color: ${props => props.theme.colors.textLight};
    cursor: not-allowed;
  }
`;

const getVoteTitle = (voteType: VoteType): string => {
  const titles = {
    [VoteType.ELIMINATION]: '投票淘汰',
    [VoteType.WEREWOLF_KILL]: '狼人杀人',
    [VoteType.SEER_CHECK]: '预言家查验',
    [VoteType.WITCH_SAVE]: '女巫救人',
    [VoteType.WITCH_POISON]: '女巫毒人',
    [VoteType.GUARD_PROTECT]: '守卫保护'
  };
  return titles[voteType] || '选择目标';
};

const getVoteDescription = (voteType: VoteType): string => {
  const descriptions = {
    [VoteType.ELIMINATION]: '选择要投票淘汰的玩家，或选择弃权',
    [VoteType.WEREWOLF_KILL]: '选择要杀害的玩家',
    [VoteType.SEER_CHECK]: '选择要查验身份的玩家',
    [VoteType.WITCH_SAVE]: '选择要救治的玩家',
    [VoteType.WITCH_POISON]: '选择要毒杀的玩家',
    [VoteType.GUARD_PROTECT]: '选择要保护的玩家'
  };
  return descriptions[voteType] || '请选择一个目标';
};

const VoteDialog: React.FC<VoteDialogProps> = ({
  isOpen,
  players,
  voteType,
  onClose,
  onConfirm
}) => {
  const [selectedPlayerId, setSelectedPlayerId] = useState<number | null>(null);
  const [reason, setReason] = useState('');

  const playerList = Object.values(players);
  const alivePlayers = playerList.filter(p => p.status === 'ALIVE');
  const showReasonInput = voteType === VoteType.ELIMINATION;

  const handlePlayerSelect = (playerId: number) => {
    const player = players[playerId];
    if (player && player.status === 'ALIVE') {
      setSelectedPlayerId(playerId);
    }
  };

  const handleConfirm = () => {
    onConfirm(selectedPlayerId, reason.trim() || undefined);
    handleClose();
  };

  const handleAbstain = () => {
    onConfirm(null, '选择弃权');
    handleClose();
  };

  const handleClose = () => {
    setSelectedPlayerId(null);
    setReason('');
    onClose();
  };

  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      handleClose();
    }
  };

  if (!isOpen) return null;

  return (
    <Overlay isOpen={isOpen} onClick={handleOverlayClick}>
      <DialogContainer>
        <DialogHeader>
          <DialogTitle>{getVoteTitle(voteType)}</DialogTitle>
          <DialogDescription>
            {getVoteDescription(voteType)}
          </DialogDescription>
        </DialogHeader>

        <DialogBody>
          <PlayerGrid>
            {alivePlayers.map(player => (
              <PlayerOption
                key={player.player_id}
                isSelected={selectedPlayerId === player.player_id}
                isAlive={player.status === 'ALIVE'}
                onClick={() => handlePlayerSelect(player.player_id)}
              >
                <PlayerInfo>
                  <PlayerName>{player.name}</PlayerName>
                  <PlayerStatus isAlive={player.status === 'ALIVE'}>
                    {player.status === 'ALIVE' ? '存活' : '死亡'}
                  </PlayerStatus>
                </PlayerInfo>
                <StatusIndicator isAlive={player.status === 'ALIVE'} />
              </PlayerOption>
            ))}
          </PlayerGrid>

          {showReasonInput && (
            <ReasonSection>
              <ReasonLabel htmlFor="vote-reason">
                投票理由（可选）
              </ReasonLabel>
              <ReasonInput
                id="vote-reason"
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                placeholder="请输入投票理由..."
                maxLength={200}
              />
            </ReasonSection>
          )}
        </DialogBody>

        <DialogFooter>
          <Button variant="secondary" onClick={handleClose}>
            取消
          </Button>

          {voteType === VoteType.ELIMINATION && (
            <Button variant="danger" onClick={handleAbstain}>
              弃权
            </Button>
          )}

          <Button
            variant="primary"
            onClick={handleConfirm}
            disabled={!selectedPlayerId}
          >
            确认
          </Button>
        </DialogFooter>
      </DialogContainer>
    </Overlay>
  );
};

export default VoteDialog;