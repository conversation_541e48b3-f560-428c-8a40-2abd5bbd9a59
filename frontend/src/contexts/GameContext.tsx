import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { GameState, GameConfig, ChatMessage, WebSocketMessage, VoteType } from '../types';
import { useWebSocket } from '../hooks/useWebSocket';
import { gameApi } from '../services/api';

// 游戏状态类型
interface GameContextState {
  gameState: GameState | null;
  loading: boolean;
  error: string | null;
  connected: boolean;
  messages: ChatMessage[];
  selectedPlayerId: number | null;
}

// 动作类型
type GameAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_GAME_STATE'; payload: GameState | null }
  | { type: 'SET_CONNECTED'; payload: boolean }
  | { type: 'ADD_MESSAGE'; payload: ChatMessage }
  | { type: 'SET_MESSAGES'; payload: ChatMessage[] }
  | { type: 'SET_SELECTED_PLAYER'; payload: number | null }
  | { type: 'RESET_STATE' };

// Context类型
interface GameContextType extends GameContextState {
  // 游戏操作
  createGame: (config: GameConfig) => Promise<void>;
  joinGame: (gameId: string) => Promise<void>;
  startGame: () => Promise<void>;

  // 玩家操作
  selectPlayer: (playerId: number | null) => void;
  submitVote: (targetId: number | null, reason?: string) => Promise<void>;
  submitAction: (actionType: string, targetId?: number) => Promise<void>;

  // 聊天操作
  sendChatMessage: (message: string) => Promise<void>;
  addSystemMessage: (message: string) => void;

  // 工具方法
  resetError: () => void;
  resetGame: () => void;
}

// 初始状态
const initialState: GameContextState = {
  gameState: null,
  loading: false,
  error: null,
  connected: false,
  messages: [],
  selectedPlayerId: null
};

// Reducer
const gameReducer = (state: GameContextState, action: GameAction): GameContextState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };

    case 'SET_ERROR':
      return { ...state, error: action.payload, loading: false };

    case 'SET_GAME_STATE':
      return { ...state, gameState: action.payload };

    case 'SET_CONNECTED':
      return { ...state, connected: action.payload };

    case 'ADD_MESSAGE':
      return {
        ...state,
        messages: [...state.messages, action.payload]
      };

    case 'SET_MESSAGES':
      return { ...state, messages: action.payload };

    case 'SET_SELECTED_PLAYER':
      return { ...state, selectedPlayerId: action.payload };

    case 'RESET_STATE':
      return { ...initialState, connected: state.connected };

    default:
      return state;
  }
};

// 创建Context
const GameContext = createContext<GameContextType | undefined>(undefined);

// Provider组件
interface GameProviderProps {
  children: ReactNode;
}

export const GameProvider: React.FC<GameProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(gameReducer, initialState);
  const { connected, sendMessage, lastMessage } = useWebSocket();

  // 更新连接状态
  useEffect(() => {
    dispatch({ type: 'SET_CONNECTED', payload: connected });
  }, [connected]);

  // 处理WebSocket消息
  useEffect(() => {
    if (!lastMessage) return;

    switch (lastMessage.type) {
      case 'game_state_update':
        dispatch({ type: 'SET_GAME_STATE', payload: lastMessage.data });
        break;

      case 'chat_message':
        const chatMessage: ChatMessage = {
          id: Date.now().toString(),
          sender: lastMessage.data.sender || '系统',
          message: lastMessage.data.message,
          timestamp: lastMessage.timestamp,
          type: lastMessage.data.type || 'system'
        };
        dispatch({ type: 'ADD_MESSAGE', payload: chatMessage });
        break;

      case 'phase_change':
        addSystemMessage(`游戏阶段变更: ${lastMessage.data.phase}`);
        break;

      case 'game_over':
        addSystemMessage(`游戏结束: ${lastMessage.data.result}`);
        break;

      default:
        console.log('Unhandled WebSocket message:', lastMessage);
    }
  }, [lastMessage]);

  // 创建游戏
  const createGame = async (config: GameConfig) => {
    dispatch({ type: 'SET_LOADING', payload: true });
    dispatch({ type: 'SET_ERROR', payload: null });

    try {
      const response = await gameApi.createGame(config);
      if (response.success && response.data) {
        dispatch({ type: 'SET_GAME_STATE', payload: response.data });
        addSystemMessage('游戏创建成功！');
      } else {
        throw new Error(response.error || '创建游戏失败');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '创建游戏失败';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  // 加入游戏
  const joinGame = async (gameId: string) => {
    dispatch({ type: 'SET_LOADING', payload: true });
    dispatch({ type: 'SET_ERROR', payload: null });

    try {
      const response = await gameApi.joinGame(gameId);
      if (response.success && response.data) {
        dispatch({ type: 'SET_GAME_STATE', payload: response.data });
        addSystemMessage('成功加入游戏！');
      } else {
        throw new Error(response.error || '加入游戏失败');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '加入游戏失败';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  // 开始游戏
  const startGame = async () => {
    if (!state.gameState) return;

    try {
      const response = await gameApi.startGame(state.gameState.game_id);
      if (response.success && response.data) {
        dispatch({ type: 'SET_GAME_STATE', payload: response.data });
        addSystemMessage('游戏开始！');
      } else {
        throw new Error(response.error || '开始游戏失败');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '开始游戏失败';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
    }
  };

  // 选择玩家
  const selectPlayer = (playerId: number | null) => {
    dispatch({ type: 'SET_SELECTED_PLAYER', payload: playerId });
  };

  // 提交投票
  const submitVote = async (targetId: number | null, reason?: string) => {
    if (!state.gameState) return;

    try {
      const vote = {
        voter_id: 1, // TODO: 获取当前玩家ID
        target_id: targetId,
        vote_type: VoteType.ELIMINATION,
        reason: reason || ''
      };

      const response = await gameApi.submitVote(state.gameState.game_id, vote);
      if (response.success) {
        addSystemMessage(targetId ? `投票给玩家 ${targetId}` : '选择弃权');

        // 通过WebSocket通知其他玩家
        sendMessage({
          type: 'vote',
          data: vote
        });
      } else {
        throw new Error(response.error || '投票失败');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '投票失败';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
    }
  };

  // 提交行动
  const submitAction = async (actionType: string, targetId?: number) => {
    if (!state.gameState) return;

    try {
      const action = {
        player_id: 1, // TODO: 获取当前玩家ID
        action_type: actionType,
        target_id: targetId,
        timestamp: new Date().toISOString()
      };

      const response = await gameApi.submitAction(state.gameState.game_id, action);
      if (response.success) {
        addSystemMessage(`执行行动: ${actionType}`);

        // 通过WebSocket通知其他玩家
        sendMessage({
          type: 'action',
          data: action
        });
      } else {
        throw new Error(response.error || '行动执行失败');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '行动执行失败';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
    }
  };

  // 发送聊天消息
  const sendChatMessage = async (message: string) => {
    if (!state.gameState) return;

    try {
      const response = await gameApi.sendChatMessage(state.gameState.game_id, message);
      if (response.success) {
        const chatMessage: ChatMessage = {
          id: Date.now().toString(),
          sender: '我', // TODO: 获取当前玩家名称
          message,
          timestamp: new Date().toISOString(),
          type: 'player'
        };

        dispatch({ type: 'ADD_MESSAGE', payload: chatMessage });

        // 通过WebSocket发送给其他玩家
        sendMessage({
          type: 'chat_message',
          data: {
            sender: '我',
            message,
            type: 'player'
          }
        });
      } else {
        throw new Error(response.error || '发送消息失败');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '发送消息失败';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
    }
  };

  // 添加系统消息
  const addSystemMessage = (message: string) => {
    const systemMessage: ChatMessage = {
      id: Date.now().toString(),
      sender: '系统',
      message,
      timestamp: new Date().toISOString(),
      type: 'system'
    };
    dispatch({ type: 'ADD_MESSAGE', payload: systemMessage });
  };

  // 重置错误
  const resetError = () => {
    dispatch({ type: 'SET_ERROR', payload: null });
  };

  // 重置游戏
  const resetGame = () => {
    dispatch({ type: 'RESET_STATE' });
    localStorage.removeItem('wolfkill_game_id');
  };

  // 初始化时尝试恢复游戏状态
  useEffect(() => {
    const savedGameId = localStorage.getItem('wolfkill_game_id');
    if (savedGameId && connected) {
      gameApi.getGameState(savedGameId).then(response => {
        if (response.success && response.data) {
          dispatch({ type: 'SET_GAME_STATE', payload: response.data });
        }
      });
    }
  }, [connected]);

  // 保存游戏ID到本地存储
  useEffect(() => {
    if (state.gameState?.game_id) {
      localStorage.setItem('wolfkill_game_id', state.gameState.game_id);
    }
  }, [state.gameState?.game_id]);

  const contextValue: GameContextType = {
    ...state,
    createGame,
    joinGame,
    startGame,
    selectPlayer,
    submitVote,
    submitAction,
    sendChatMessage,
    addSystemMessage,
    resetError,
    resetGame
  };

  return (
    <GameContext.Provider value={contextValue}>
      {children}
    </GameContext.Provider>
  );
};

// Hook for using the game context
export const useGameContext = (): GameContextType => {
  const context = useContext(GameContext);
  if (context === undefined) {
    throw new Error('useGameContext must be used within a GameProvider');
  }
  return context;
};

export default GameContext;