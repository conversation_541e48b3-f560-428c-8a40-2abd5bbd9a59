import { useState, useEffect, useCallback } from 'react';
import { GameState, GameConfig, ApiResponse } from '../types';
import { gameApi } from '../services/api';

interface UseGameStateReturn {
  gameState: GameState | null;
  loading: boolean;
  error: string | null;
  createGame: (config: GameConfig) => Promise<void>;
  joinGame: (gameId: string) => Promise<void>;
  updateGameState: (newState: GameState) => void;
  resetError: () => void;
}

export const useGameState = (): UseGameStateReturn => {
  const [gameState, setGameState] = useState<GameState | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // 创建游戏
  const createGame = useCallback(async (config: GameConfig) => {
    setLoading(true);
    setError(null);

    try {
      const response = await gameApi.createGame(config);
      if (response.success && response.data) {
        setGameState(response.data);
      } else {
        throw new Error(response.error || '创建游戏失败');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '创建游戏失败';
      setError(errorMessage);
      console.error('Create game error:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // 加入游戏
  const joinGame = useCallback(async (gameId: string) => {
    setLoading(true);
    setError(null);

    try {
      const response = await gameApi.joinGame(gameId);
      if (response.success && response.data) {
        setGameState(response.data);
      } else {
        throw new Error(response.error || '加入游戏失败');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '加入游戏失败';
      setError(errorMessage);
      console.error('Join game error:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // 更新游戏状态
  const updateGameState = useCallback((newState: GameState) => {
    setGameState(newState);
  }, []);

  // 重置错误
  const resetError = useCallback(() => {
    setError(null);
  }, []);

  // 获取当前游戏状态
  const fetchGameState = useCallback(async (gameId: string) => {
    try {
      const response = await gameApi.getGameState(gameId);
      if (response.success && response.data) {
        setGameState(response.data);
      }
    } catch (err) {
      console.error('Fetch game state error:', err);
    }
  }, []);

  // 初始化时尝试恢复游戏状态
  useEffect(() => {
    const savedGameId = localStorage.getItem('wolfkill_game_id');
    if (savedGameId) {
      fetchGameState(savedGameId);
    }
  }, [fetchGameState]);

  // 保存游戏ID到本地存储
  useEffect(() => {
    if (gameState?.game_id) {
      localStorage.setItem('wolfkill_game_id', gameState.game_id);
    }
  }, [gameState?.game_id]);

  return {
    gameState,
    loading,
    error,
    createGame,
    joinGame,
    updateGameState,
    resetError
  };
};