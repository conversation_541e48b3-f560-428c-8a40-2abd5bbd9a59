import { keyframes } from 'styled-components';

// 基础动画
export const fadeIn = keyframes`
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
`;

export const fadeOut = keyframes`
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
`;

export const slideInUp = keyframes`
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
`;

export const slideInDown = keyframes`
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
`;

export const slideInLeft = keyframes`
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
`;

export const slideInRight = keyframes`
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
`;

export const scaleIn = keyframes`
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
`;

export const scaleOut = keyframes`
  from {
    transform: scale(1);
    opacity: 1;
  }
  to {
    transform: scale(0.8);
    opacity: 0;
  }
`;

// 旋转动画
export const spin = keyframes`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`;

export const pulse = keyframes`
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
`;

export const bounce = keyframes`
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
`;

export const shake = keyframes`
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-2px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(2px);
  }
`;

// 游戏特定动画
export const cardFlip = keyframes`
  0% {
    transform: rotateY(0);
  }
  50% {
    transform: rotateY(90deg);
  }
  100% {
    transform: rotateY(0);
  }
`;

export const glow = keyframes`
  0%, 100% {
    box-shadow: 0 0 5px rgba(52, 152, 219, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(52, 152, 219, 0.8);
  }
`;

export const heartbeat = keyframes`
  0%, 100% {
    transform: scale(1);
  }
  14% {
    transform: scale(1.1);
  }
  28% {
    transform: scale(1);
  }
  42% {
    transform: scale(1.1);
  }
  70% {
    transform: scale(1);
  }
`;

export const typewriter = keyframes`
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
`;

export const blink = keyframes`
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
`;

// 粒子效果动画
export const float = keyframes`
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
`;

export const sparkle = keyframes`
  0%, 100% {
    opacity: 0;
    transform: scale(0);
  }
  50% {
    opacity: 1;
    transform: scale(1);
  }
`;

// 动画组合工具
export const animations = {
  // 入场动画
  entrance: {
    fadeIn: `${fadeIn} 0.3s ease-out`,
    slideInUp: `${slideInUp} 0.4s ease-out`,
    slideInDown: `${slideInDown} 0.4s ease-out`,
    slideInLeft: `${slideInLeft} 0.4s ease-out`,
    slideInRight: `${slideInRight} 0.4s ease-out`,
    scaleIn: `${scaleIn} 0.3s ease-out`,
  },

  // 退场动画
  exit: {
    fadeOut: `${fadeOut} 0.3s ease-in`,
    scaleOut: `${scaleOut} 0.3s ease-in`,
  },

  // 循环动画
  loop: {
    spin: `${spin} 1s linear infinite`,
    pulse: `${pulse} 2s ease-in-out infinite`,
    bounce: `${bounce} 1s ease infinite`,
    float: `${float} 3s ease-in-out infinite`,
    glow: `${glow} 2s ease-in-out infinite`,
    heartbeat: `${heartbeat} 1.5s ease-in-out infinite`,
  },

  // 交互动画
  interaction: {
    shake: `${shake} 0.5s ease-in-out`,
    cardFlip: `${cardFlip} 0.6s ease-in-out`,
  },

  // 文字动画
  text: {
    typewriter: `${typewriter} 2s steps(40, end)`,
    blink: `${blink} 1s step-end infinite`,
  }
};

// 动画延迟工具
export const getStaggerDelay = (index: number, baseDelay: number = 0.1): string => {
  return `${baseDelay * index}s`;
};

// 缓动函数
export const easings = {
  easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
  easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
  easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
  bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
  elastic: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)',
};

// 动画持续时间
export const durations = {
  fast: '150ms',
  normal: '300ms',
  slow: '500ms',
  slower: '800ms',
};