// 主题配置
export const theme = {
  colors: {
    // 主色调
    primary: '#2C3E50',
    primaryLight: '#34495E',
    primaryDark: '#1A252F',

    // 辅助色
    secondary: '#E67E22',
    secondaryLight: '#F39C12',
    secondaryDark: '#D35400',

    // 背景色
    background: '#ECF0F1',
    backgroundDark: '#BDC3C7',
    backgroundLight: '#FFFFFF',

    // 文字色
    text: '#2C3E50',
    textLight: '#7F8C8D',
    textWhite: '#FFFFFF',

    // 状态色
    success: '#27AE60',
    successLight: '#2ECC71',
    successDark: '#1E8449',

    warning: '#F39C12',
    warningLight: '#F1C40F',
    warningDark: '#E67E22',

    danger: '#E74C3C',
    dangerLight: '#EC7063',
    dangerDark: '#C0392B',

    info: '#3498DB',
    infoLight: '#5DADE2',
    infoDark: '#2980B9',

    // 角色颜色
    villager: '#27AE60',
    werewolf: '#E74C3C',
    seer: '#3498DB',
    witch: '#9B59B6',
    guard: '#F39C12',
    hunter: '#8B4513',

    // 边框色
    border: '#BDC3C7',
    borderLight: '#D5DBDB',
    borderDark: '#85929E',

    // 阴影色
    shadow: 'rgba(52, 73, 94, 0.1)',
    shadowDark: 'rgba(52, 73, 94, 0.2)'
  },

  fonts: {
    primary: '"Microsoft YaHei", "PingFang SC", "Helvetica Neue", Arial, sans-serif',
    mono: '"Fira Code", "Monaco", "Consolas", monospace'
  },

  fontSizes: {
    xs: '0.75rem',    // 12px
    sm: '0.875rem',   // 14px
    md: '1rem',       // 16px
    lg: '1.125rem',   // 18px
    xl: '1.25rem',    // 20px
    '2xl': '1.5rem',  // 24px
    '3xl': '1.875rem', // 30px
    '4xl': '2.25rem'  // 36px
  },

  fontWeights: {
    normal: 400,
    medium: 500,
    semibold: 600,
    bold: 700
  },

  lineHeights: {
    tight: 1.25,
    normal: 1.5,
    relaxed: 1.75
  },

  spacing: {
    xs: '0.25rem',   // 4px
    sm: '0.5rem',    // 8px
    md: '1rem',      // 16px
    lg: '1.5rem',    // 24px
    xl: '2rem',      // 32px
    '2xl': '3rem',   // 48px
    '3xl': '4rem',   // 64px
    '4xl': '6rem'    // 96px
  },

  borderRadius: {
    none: '0',
    sm: '0.125rem',   // 2px
    md: '0.375rem',   // 6px
    lg: '0.5rem',     // 8px
    xl: '0.75rem',    // 12px
    '2xl': '1rem',    // 16px
    full: '9999px'
  },

  shadows: {
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
  },

  breakpoints: {
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1536px'
  },

  transitions: {
    fast: '150ms ease-in-out',
    normal: '300ms ease-in-out',
    slow: '500ms ease-in-out'
  },

  zIndex: {
    dropdown: 1000,
    sticky: 1020,
    fixed: 1030,
    modal: 1040,
    popover: 1050,
    tooltip: 1060
  }
};

// 角色颜色映射
export const getRoleColor = (role: string): string => {
  const roleColors: { [key: string]: string } = {
    VILLAGER: theme.colors.villager,
    WEREWOLF: theme.colors.werewolf,
    SEER: theme.colors.seer,
    WITCH: theme.colors.witch,
    GUARD: theme.colors.guard,
    HUNTER: theme.colors.hunter
  };
  return roleColors[role.toUpperCase()] || theme.colors.text;
};

// 阶段颜色映射
export const getPhaseColor = (phase: string): string => {
  const phaseColors: { [key: string]: string } = {
    SETUP: theme.colors.info,
    NIGHT: theme.colors.primaryDark,
    DAY_DISCUSSION: theme.colors.warning,
    DAY_VOTING: theme.colors.danger,
    GAME_OVER: theme.colors.success
  };
  return phaseColors[phase] || theme.colors.text;
};

export type Theme = typeof theme;