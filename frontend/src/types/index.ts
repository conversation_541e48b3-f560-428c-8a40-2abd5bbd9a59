// 游戏类型定义
export enum Role {
  VILLAGER = 'VILLAGER',
  WEREWOLF = 'WEREWOLF',
  SEER = 'SEER',
  WITCH = 'WITCH',
  GUARD = 'GUARD',
  HUNTER = 'HUNTER'
}

export enum GamePhase {
  SETUP = 'SETUP',
  NIGHT = 'NIGHT',
  DAY_DISCUSSION = 'DAY_DISCUSSION',
  DAY_VOTING = 'DAY_VOTING',
  GAME_OVER = 'GAME_OVER'
}

export enum GameResult {
  ONGOING = 'ONGOING',
  VILLAGERS_WIN = 'VILLAGERS_WIN',
  WEREWOLVES_WIN = 'WEREWOLVES_WIN',
  DRAW = 'DRAW'
}

export enum PlayerStatus {
  ALIVE = 'ALIVE',
  DEAD = 'DEAD'
}

export enum VoteType {
  ELIMINATION = 'ELIMINATION',
  WEREWOLF_KILL = 'WEREWOLF_KILL',
  SEER_CHECK = 'SEER_CHECK',
  WITCH_SAVE = 'WITCH_SAVE',
  WITCH_POISON = 'WITCH_POISON',
  GUARD_PROTECT = 'GUARD_PROTECT'
}

// 玩家接口
export interface Player {
  player_id: number;
  name: string;
  role: Role;
  status: PlayerStatus;
  faction: string;
}

// 游戏状态接口
export interface GameState {
  game_id: string;
  current_phase: GamePhase;
  current_round: number;
  game_result: GameResult;
  players: { [key: number]: Player };
  alive_players: number[];
  dead_players: number[];
}

// 投票接口
export interface Vote {
  voter_id: number;
  target_id: number | null;
  vote_type: VoteType;
  reason?: string;
}

// 行动接口
export interface Action {
  player_id: number;
  action_type: string;
  target_id?: number;
  timestamp: string;
}

// 聊天消息接口
export interface ChatMessage {
  id: string;
  sender: string;
  message: string;
  timestamp: string;
  type: 'system' | 'player' | 'announcement';
}

// 游戏配置接口
export interface GameConfig {
  total_players: number;
  role_distribution: { [key in Role]?: number };
}

// API响应接口
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// WebSocket消息接口
export interface WebSocketMessage {
  type: string;
  data: any;
  timestamp: string;
}