#!/usr/bin/env python3
"""
狼人杀AI游戏GUI版本主程序
Phase 4 实现：图形用户界面
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.ui.gui.main_window import MainWindow
from src.models.game_state import GameConfig
from src.models.enums import Role
from src.engine.game_engine import GameEngine


class WolfkillGUI:
    """狼人杀GUI应用程序"""

    def __init__(self):
        self.main_window = MainWindow()
        self.game_engine = None

        # 设置回调函数
        self.main_window.on_new_game = self.start_new_game
        self.main_window.on_settings = self.show_settings
        self.main_window.on_help = self.show_help
        self.main_window.on_exit = self.exit_application

        # 初始化状态
        self.main_window.update_status("欢迎使用狼人杀AI游戏")

    def start_new_game(self):
        """开始新游戏"""
        try:
            # 创建游戏配置（简化版，6人局）
            config = GameConfig(
                total_players=6,
                role_distribution={
                    Role.VILLAGER: 2,
                    Role.WEREWOLF: 2,
                    Role.SEER: 1,
                    Role.WITCH: 1
                }
            )

            # 创建游戏引擎
            self.game_engine = GameEngine(config)
            self.main_window.set_game_engine(self.game_engine)

            # 创建游戏
            player_names = [f"AI玩家{i+1}" for i in range(6)]
            game_state = self.game_engine.create_game("gui_game", player_names)

            # 更新UI
            self.main_window.update_game_state(game_state)
            self.main_window.update_status("游戏已创建，点击开始游戏")
            self.main_window.add_chat_message("系统", "游戏已创建，准备开始...")

            # 显示玩家信息
            self._display_player_info(game_state)

        except Exception as e:
            self.main_window.update_status(f"创建游戏失败: {e}")
            print(f"创建游戏失败: {e}")

    def _display_player_info(self, game_state):
        """显示玩家信息"""
        self.main_window.add_chat_message("系统", "=== 玩家信息 ===")
        for player_id, player in game_state.players.items():
            role_name = self._get_role_name(player.role)
            self.main_window.add_chat_message(
                "系统",
                f"{player.name} - {role_name}"
            )

    def _get_role_name(self, role):
        """获取角色中文名"""
        role_names = {
            Role.VILLAGER: "村民",
            Role.WEREWOLF: "狼人",
            Role.SEER: "预言家",
            Role.WITCH: "女巫",
            Role.GUARD: "守卫",
            Role.HUNTER: "猎人"
        }
        return role_names.get(role, "未知角色")

    def show_settings(self):
        """显示设置"""
        self.main_window.update_status("设置功能正在开发中...")

    def show_help(self):
        """显示帮助"""
        self.main_window.update_status("帮助功能已显示")

    def exit_application(self):
        """退出应用程序"""
        self.main_window.destroy()

    def run(self):
        """运行应用程序"""
        try:
            self.main_window.show()
        except KeyboardInterrupt:
            print("\n应用程序被用户中断")
        except Exception as e:
            print(f"应用程序运行出错: {e}")
            import traceback
            traceback.print_exc()


def main():
    """主函数"""
    print("启动狼人杀AI游戏GUI版本...")

    try:
        app = WolfkillGUI()
        app.run()
    except Exception as e:
        print(f"程序启动失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()