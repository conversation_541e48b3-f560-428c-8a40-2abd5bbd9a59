"""
AI策略管理器
统一管理所有AI组件，提供完整的AI策略系统
"""
from typing import Dict, List, Optional, Any, Type
from ..models.enums import Role, GamePhase, VoteType
from ..models.game_state import GameState
from ..players.ai_player import AIPlayer

from .ai_personality import AIPersonalityManager, AIConfiguration, DifficultyLevel, PersonalityType
from .villager_ai import VillagerAI
from .werewolf_ai import WerewolfAI
from .seer_ai import SeerAI
from .special_roles_ai import WitchAI, GuardAI, HunterAI, SpecialRoleAIManager
from .llm_integration import LLMManager, create_llm_manager


class EnhancedAIPlayer(AIPlayer):
    """增强的AI玩家"""
    
    def __init__(self, player_id: int, name: str,
                 ai_config: Optional[AIConfiguration] = None,
                 use_llm: bool = True, llm_config: str = "qwen3_30b"):
        """初始化增强AI玩家"""
        super().__init__(player_id, name)
        
        # AI配置
        self.ai_config = ai_config or AIPersonalityManager().create_ai_configuration()
        self.use_llm = use_llm
        self.llm_config = llm_config
        
        # 角色专用AI
        self.role_ai = None
        
        # 大模型管理器
        if use_llm:
            self.llm_manager = create_llm_manager(llm_config)
        else:
            self.llm_manager = None
    
    def set_role(self, role: Role):
        """设置角色并初始化对应的AI策略"""
        super().set_role(role)
        self._initialize_role_ai()
    
    def _initialize_role_ai(self):
        """初始化角色专用AI"""
        if not self.role:
            return
        
        difficulty = self.ai_config.difficulty.value
        
        if self.role == Role.VILLAGER:
            self.role_ai = VillagerAI(self, difficulty, self.use_llm, self.llm_config)
        elif self.role == Role.WEREWOLF:
            self.role_ai = WerewolfAI(self, difficulty, self.use_llm, self.llm_config)
        elif self.role == Role.SEER:
            self.role_ai = SeerAI(self, difficulty, self.use_llm, self.llm_config)
        elif self.role == Role.WITCH:
            self.role_ai = WitchAI(self, difficulty, self.use_llm, self.llm_config)
        elif self.role == Role.GUARD:
            self.role_ai = GuardAI(self, difficulty, self.use_llm, self.llm_config)
        elif self.role == Role.HUNTER:
            self.role_ai = HunterAI(self, difficulty, self.use_llm, self.llm_config)
    
    def make_vote_decision(self, game_state: GameState, vote_type: VoteType) -> Optional[int]:
        """做出投票决策"""
        if self.role_ai:
            # 使用角色专用AI
            if hasattr(self.role_ai, 'make_vote_decision'):
                decision = self.role_ai.make_vote_decision(game_state, vote_type)
            elif hasattr(self.role_ai, 'make_kill_decision') and vote_type == VoteType.WEREWOLF_KILL:
                decision = self.role_ai.make_kill_decision(game_state)
            else:
                decision = super().make_vote_decision(game_state, vote_type)
        else:
            decision = super().make_vote_decision(game_state, vote_type)
        
        # 应用个性化调整
        return self._apply_personality_to_decision(decision, game_state, vote_type)
    
    def make_special_action_decision(self, game_state: GameState, action_type: str) -> Optional[int]:
        """做出特殊行动决策"""
        if self.role_ai:
            if self.role == Role.SEER and action_type == "seer_check":
                if hasattr(self.role_ai, 'make_check_decision'):
                    return self.role_ai.make_check_decision(game_state)
            elif self.role == Role.GUARD and action_type == "guard_protect":
                if hasattr(self.role_ai, 'make_protection_decision'):
                    return self.role_ai.make_protection_decision(game_state)
            elif self.role == Role.WITCH and action_type in ["witch_save", "witch_poison"]:
                if hasattr(self.role_ai, 'make_witch_decision'):
                    save_target, poison_target = self.role_ai.make_witch_decision(game_state, None)
                    return save_target if action_type == "witch_save" else poison_target
            elif self.role == Role.HUNTER and action_type == "hunter_shoot":
                if hasattr(self.role_ai, 'make_revenge_decision'):
                    return self.role_ai.make_revenge_decision(game_state)
        
        return super().make_special_action_decision(game_state, action_type)
    
    def generate_speech(self, game_state: GameState, phase: GamePhase) -> str:
        """生成发言"""
        # 根据个性决定是否发言
        if not self._should_speak(game_state, phase):
            return ""
        
        if self.role_ai and hasattr(self.role_ai, 'generate_speech'):
            speech = self.role_ai.generate_speech(game_state, phase)
        else:
            speech = super().generate_speech(game_state, phase)
        
        # 应用个性化调整
        return self._apply_personality_to_speech(speech, game_state, phase)
    
    def _should_speak(self, game_state: GameState, phase: GamePhase) -> bool:
        """根据个性决定是否发言"""
        if phase != GamePhase.DAY_DISCUSSION:
            return False
        
        # 基于发言频率和个性特征
        speech_probability = self.ai_config.speech_frequency
        
        # 根据个性特征调整
        traits = self.ai_config.traits
        if traits.verbosity > 0.7:
            speech_probability += 0.2
        if traits.social_skills > 0.7:
            speech_probability += 0.1
        if traits.confidence > 0.7:
            speech_probability += 0.1
        
        # 根据游戏情况调整
        if self._is_in_danger(game_state):
            speech_probability += 0.3  # 危险时更可能发言
        
        import random
        return random.random() < min(0.9, speech_probability)
    
    def _is_in_danger(self, game_state: GameState) -> bool:
        """判断是否处于危险中"""
        # 简化实现：检查是否被投票
        if game_state.current_votes:
            votes_against_me = sum(1 for target_id in game_state.current_votes.values() 
                                 if target_id == self.player_id)
            return votes_against_me > 0
        return False
    
    def _apply_personality_to_decision(self, decision: Optional[int], 
                                     game_state: GameState, vote_type: VoteType) -> Optional[int]:
        """应用个性化调整到决策"""
        if decision is None:
            return None
        
        traits = self.ai_config.traits
        
        # 根据不可预测性添加随机因素
        if traits.unpredictability > 0.5:
            import random
            if random.random() < traits.unpredictability * 0.3:
                # 有一定概率改变决策
                alive_players = [p.player_id for p in game_state.get_alive_players() 
                               if p.player_id != self.player_id]
                if alive_players:
                    return random.choice(alive_players)
        
        return decision
    
    def _apply_personality_to_speech(self, speech: str, 
                                   game_state: GameState, phase: GamePhase) -> str:
        """应用个性化调整到发言"""
        if not speech:
            return speech
        
        traits = self.ai_config.traits
        
        # 根据情绪稳定性调整语气
        if traits.emotional_stability < 0.4:
            # 情绪不稳定，可能添加感叹号或强调
            if "。" in speech:
                speech = speech.replace("。", "！")
        
        # 根据自信心调整表达
        if traits.confidence > 0.7:
            # 高自信，使用更肯定的表达
            speech = speech.replace("我觉得", "我确信")
            speech = speech.replace("可能", "一定")
        elif traits.confidence < 0.4:
            # 低自信，使用更谨慎的表达
            speech = speech.replace("是", "可能是")
            speech = "我觉得" + speech if not speech.startswith("我") else speech
        
        return speech
    
    def process_game_event(self, event_type: str, event_data: Dict[str, Any]):
        """处理游戏事件"""
        # 先让角色AI处理
        if self.role_ai and hasattr(self.role_ai, 'process_game_event'):
            self.role_ai.process_game_event(event_type, event_data)
        
        # 基础处理
        super().process_game_event(event_type, event_data)
        
        # 个性化学习和适应
        self._adapt_to_event(event_type, event_data)
    
    def _adapt_to_event(self, event_type: str, event_data: Dict[str, Any]):
        """根据事件进行适应性学习"""
        if not self.ai_config.learning_enabled:
            return
        
        adaptation_rate = self.ai_config.adaptation_rate
        
        # 根据事件类型调整策略
        if event_type == "player_eliminated" and event_data.get("player_id") == self.player_id:
            # 如果自己被淘汰，增加谨慎性
            self.ai_config.traits.cautiousness = min(1.0, 
                self.ai_config.traits.cautiousness + adaptation_rate)
        
        elif event_type == "game_ended":
            result = event_data.get("result")
            if self._won_game(result):
                # 胜利时增加自信
                self.ai_config.traits.confidence = min(1.0, 
                    self.ai_config.traits.confidence + adaptation_rate * 0.5)
            else:
                # 失败时增加分析能力
                self.ai_config.traits.analytical_thinking = min(1.0, 
                    self.ai_config.traits.analytical_thinking + adaptation_rate * 0.3)
    
    def _won_game(self, result) -> bool:
        """判断是否获胜"""
        from ..models.enums import GameResult
        if self.role and self.role.name in ["WEREWOLF"]:
            return result == GameResult.WEREWOLVES_WIN
        else:
            return result == GameResult.VILLAGERS_WIN
    
    def get_ai_status(self) -> Dict[str, Any]:
        """获取AI状态信息"""
        status = {
            "player_id": self.player_id,
            "name": self.name,
            "role": self.role.name if self.role else None,
            "difficulty": self.ai_config.difficulty.value,
            "personality_type": self.ai_config.personality_type.value,
            "traits": {
                "aggressiveness": self.ai_config.traits.aggressiveness,
                "cautiousness": self.ai_config.traits.cautiousness,
                "analytical_thinking": self.ai_config.traits.analytical_thinking,
                "social_skills": self.ai_config.traits.social_skills,
                "confidence": self.ai_config.traits.confidence
            },
            "behavior_params": {
                "speech_frequency": self.ai_config.speech_frequency,
                "vote_confidence": self.ai_config.vote_confidence,
                "information_sharing": self.ai_config.information_sharing
            }
        }
        
        # 添加角色专用信息
        if self.role_ai:
            if hasattr(self.role_ai, 'get_information_summary'):
                status["role_specific_info"] = self.role_ai.get_information_summary()
            elif hasattr(self.role_ai, 'memory'):
                status["role_specific_info"] = {"memory_items": len(getattr(self.role_ai.memory, '__dict__', {}))}
        
        return status


class AIStrategyManager:
    """AI策略管理器"""
    
    def __init__(self):
        self.personality_manager = AIPersonalityManager()
        self.special_role_manager = SpecialRoleAIManager()
        self.ai_players = {}
    
    def create_ai_player(self, player_id: int, name: str,
                        difficulty: DifficultyLevel = DifficultyLevel.NORMAL,
                        personality_type: Optional[PersonalityType] = None,
                        use_llm: bool = True, llm_config: str = "qwen3_30b") -> EnhancedAIPlayer:
        """创建AI玩家"""
        
        # 创建AI配置
        ai_config = self.personality_manager.create_ai_configuration(
            difficulty=difficulty,
            personality_type=personality_type
        )
        
        # 创建增强AI玩家
        ai_player = EnhancedAIPlayer(
            player_id=player_id,
            name=name,
            ai_config=ai_config,
            use_llm=use_llm,
            llm_config=llm_config
        )
        
        # 注册配置
        self.personality_manager.register_configuration(player_id, ai_config)
        self.ai_players[player_id] = ai_player
        
        return ai_player
    
    def create_balanced_team(self, player_names: List[str],
                           difficulty: DifficultyLevel = DifficultyLevel.NORMAL,
                           use_llm: bool = True) -> List[EnhancedAIPlayer]:
        """创建平衡的AI团队"""
        
        # 创建平衡配置
        configs = self.personality_manager.create_balanced_team(len(player_names), difficulty)
        
        ai_players = []
        for i, (name, config) in enumerate(zip(player_names, configs)):
            ai_player = EnhancedAIPlayer(
                player_id=i + 1,
                name=name,
                ai_config=config,
                use_llm=use_llm
            )
            
            self.personality_manager.register_configuration(i + 1, config)
            self.ai_players[i + 1] = ai_player
            ai_players.append(ai_player)
        
        return ai_players
    
    def get_ai_player(self, player_id: int) -> Optional[EnhancedAIPlayer]:
        """获取AI玩家"""
        return self.ai_players.get(player_id)
    
    def get_team_analysis(self) -> Dict[str, Any]:
        """获取团队分析"""
        configs = [ai.ai_config for ai in self.ai_players.values()]
        return self.personality_manager.analyze_team_composition(configs)
    
    def get_all_ai_status(self) -> List[Dict[str, Any]]:
        """获取所有AI状态"""
        return [ai.get_ai_status() for ai in self.ai_players.values()]
