"""
大模型集成组件
支持多种大模型服务，用于生成AI发言内容和策略分析
"""
import json
import random
from typing import Dict, List, Optional, Any
from abc import ABC, abstractmethod
from dataclasses import dataclass
import openai
from copy import copy

from ..models.enums import Role, GamePhase, Faction
from ..models.game_state import GameState


@dataclass
class LLMConfig:
    """大模型配置"""
    provider: str  # "openai", "anthropic", "local", "mock"
    model_name: str
    api_key: Optional[str] = None
    base_url: Optional[str] = None
    max_tokens: int = 150
    temperature: float = 0.7
    timeout: int = 30


class BaseLLMProvider(ABC):
    """大模型提供者基类"""

    def __init__(self, config: LLMConfig):
        self.config = config

    @abstractmethod
    async def generate_text(self, prompt: str, history: List[Dict[str, str]] = None,
                          system: str = "You are a helpful assistant", **kwargs) -> str:
        """生成文本"""
        pass

    @abstractmethod
    def generate_text_sync(self, prompt: str, history: List[Dict[str, str]] = None,
                         system: str = "You are a helpful assistant", **kwargs) -> str:
        """同步生成文本"""
        pass


class MockLLMProvider(BaseLLMProvider):
    """模拟大模型提供者（用于测试和演示）"""
    
    def __init__(self, config: LLMConfig):
        super().__init__(config)
        self.response_templates = self._load_response_templates()
    
    def _load_response_templates(self) -> Dict[str, List[str]]:
        """加载响应模板"""
        return {
            "villager_speech": [
                "我觉得需要仔细分析一下昨晚的情况。",
                "从投票情况来看，有些人的行为很可疑。",
                "我们需要找出狼人，不能让他们继续隐藏。",
                "大家要冷静分析，不要被情绪影响判断。",
                "我相信通过逻辑推理能找出真相。"
            ],
            "werewolf_speech": [
                "我同意大家的分析，确实需要小心。",
                "昨晚的情况确实很奇怪，我们要谨慎。",
                "我觉得某些人的发言有问题。",
                "我们应该相信预言家的判断。",
                "让我们一起找出真正的狼人。"
            ],
            "seer_speech": [
                "我有一些重要信息要分享。",
                "根据我的观察，情况可能不是表面看起来的那样。",
                "我建议大家听听我的分析。",
                "我可以确定某些人的身份。",
                "相信我，我知道谁是好人。"
            ],
            "witch_speech": [
                "昨晚发生的事情我有所了解。",
                "我的药剂使用需要谨慎考虑。",
                "让我来分析一下局势。",
                "我会在关键时刻发挥作用。",
                "大家要相信我的判断。"
            ],
            "guard_speech": [
                "我会保护重要的人。",
                "昨晚我做了我认为正确的选择。",
                "保护策略需要仔细考虑。",
                "我会尽力保护好人。",
                "让我们团结起来对抗狼人。"
            ],
            "hunter_speech": [
                "如果我被淘汰，我不会白白死去。",
                "我的枪会瞄准最可疑的人。",
                "大家要小心，不要误伤好人。",
                "我会为村民阵营战斗到最后。",
                "我的选择会很谨慎。"
            ]
        }
    
    async def generate_text(self, prompt: str, history: List[Dict[str, str]] = None,
                          system: str = "You are a helpful assistant", **kwargs) -> str:
        """异步生成文本"""
        return self.generate_text_sync(prompt, history=history, system=system, **kwargs)

    def generate_text_sync(self, prompt: str, history: List[Dict[str, str]] = None,
                         system: str = "You are a helpful assistant", **kwargs) -> str:
        """同步生成文本"""
        # 设置默认值
        if history is None:
            history = []

        # 解析提示词中的角色和场景信息
        role_type = self._extract_role_from_prompt(prompt)
        speech_type = f"{role_type}_speech"

        if speech_type in self.response_templates:
            responses = self.response_templates[speech_type]
            response = random.choice(responses)
        else:
            # 默认响应
            responses = [
                "我需要仔细考虑当前的情况。",
                "让我分析一下现在的局势。",
                "我会做出最合适的选择。",
                "这个决定需要谨慎考虑。"
            ]
            response = random.choice(responses)

        # 添加一些随机性和个性化
        if random.random() < 0.3:  # 30%概率添加个性化内容
            personality_additions = [
                "（思考中...）",
                "我的直觉告诉我...",
                "根据我的经验...",
                "让我想想...",
                "这很重要..."
            ]
            addition = random.choice(personality_additions)
            response = f"{addition} {response}"

        # 如果有历史对话，可以基于历史调整响应
        if history and len(history) > 0:
            # 简单的历史感知：如果历史中有相似的问题，稍微改变回答
            if random.random() < 0.2:  # 20%概率基于历史调整
                response = f"如我之前提到的，{response}"

        return response
    
    def _extract_role_from_prompt(self, prompt: str) -> str:
        """从提示词中提取角色类型"""
        prompt_lower = prompt.lower()
        
        if "werewolf" in prompt_lower or "狼人" in prompt_lower:
            return "werewolf"
        elif "seer" in prompt_lower or "预言家" in prompt_lower:
            return "seer"
        elif "witch" in prompt_lower or "女巫" in prompt_lower:
            return "witch"
        elif "guard" in prompt_lower or "守卫" in prompt_lower:
            return "guard"
        elif "hunter" in prompt_lower or "猎人" in prompt_lower:
            return "hunter"
        else:
            return "villager"


class OpenAIProvider(BaseLLMProvider):
    def __init__(self, config: LLMConfig):
        super().__init__(config)
        self.client = openai.OpenAI(api_key=config.api_key, base_url=config.base_url)
        self.async_client = openai.AsyncOpenAI(api_key=config.api_key, base_url=config.base_url)
        
    def _build_messages(self, prompt, history=[], system="", system_as_query=False, *args, **kwargs):
        msgs = copy(history)

        if system != "" and system_as_query:
            prompt = f"{system}\n\n{prompt}"
            system = ""

        if system != "":
            if len(msgs) > 0 and msgs[0]['role'] == 'system':
                msgs[0]['content'] = system
            else:
                msgs.insert(0, {
                    "role": "system",
                    "content": system
                })

        msgs.append({
            "role": "user",
            "content": prompt
        })

        return msgs

    def _call_llm(self, messages, thinking, **gen_kwargs):
        """处理非流式同步调用的工具调用逻辑"""
        # HARDCODE: only for qwen3 series
        if 'qwen3' in self.config.model_name.lower() and not thinking:
            messages[-1]['content'] += "\\no_think"

        response = self.client.chat.completions.create(
            messages=messages,
            model=self.config.model_name,
            stream=False,
            **gen_kwargs
        )
        content = response.choices[0].message.content
        if content:
            messages.append({
                "role": "assistant",
                "content": content
            })
        return content, messages

    async def _call_llm_async(self, messages, thinking, **gen_kwargs):
        """处理异步非流式调用的工具调用逻辑"""
        # HARDCODE: only for qwen3 series
        if 'qwen3' in self.config.model_name.lower() and not thinking:
            messages[-1]['content'] += "\\no_think"

        response = await self.async_client.chat.completions.create(
            messages=messages,
            model=self.config.model_name,
            stream=False,
            **gen_kwargs
        )
        content = response.choices[0].message.content
        if content:
            messages.append({
                "role": "assistant",
                "content": content
            })
        return content, messages

    def generate_text_sync(self, prompt: str, history: List[Dict[str, str]] = None,
                         system: str = "You are a helpful assistant", **kwargs) -> str:
        # 设置默认值
        if history is None:
            history = []

        try:
            msgs = self._build_messages(
                prompt=prompt,
                history=history,
                system=system,
            )
            response, msgs = self._call_llm(msgs, thinking=False, **kwargs)
        except Exception as e:
            raise RuntimeError(f"LLMClient.chat error: {e}") from e

        return response

    async def generate_text(self, prompt: str, history: List[Dict[str, str]] = None,
                          system: str = "You are a helpful assistant", **kwargs) -> str:
        # 设置默认值
        if history is None:
            history = []

        try:
            msgs = self._build_messages(
                prompt=prompt,
                history=history,
                system=system,
            )
            response, msgs = await self._call_llm_async(msgs, thinking=False, **kwargs)
        except Exception as e:
            raise RuntimeError(f"LLMClient.chat error: {e}") from e

        return response


class LLMManager:
    """大模型管理器"""
    
    def __init__(self, config: LLMConfig):
        self.config = config
        self.provider = self._create_provider()
    
    def _create_provider(self) -> BaseLLMProvider:
        """创建大模型提供者"""
        if self.config.provider == "openai":
            return OpenAIProvider(self.config)
        elif self.config.provider == "mock":
            return MockLLMProvider(self.config)
        else:
            # 默认使用模拟提供者
            return MockLLMProvider(self.config)
    
    def generate_speech(self, role: Role, game_state: GameState,
                       context: Dict[str, Any], history: List[Dict[str, str]] = None) -> str:
        """生成角色发言"""
        prompt = self._build_speech_prompt(role, game_state, context)
        system_prompt = self._build_role_system_prompt(role)

        return self.provider.generate_text_sync(
            prompt=prompt,
            history=history or [],
            system=system_prompt
        )
    
    def generate_strategy_analysis(self, role: Role, game_state: GameState,
                                 possible_actions: List[str],
                                 history: List[Dict[str, str]] = None) -> Dict[str, Any]:
        """生成策略分析"""
        prompt = self._build_strategy_prompt(role, game_state, possible_actions)
        system_prompt = self._build_strategy_system_prompt(role)

        response = self.provider.generate_text_sync(
            prompt=prompt,
            history=history or [],
            system=system_prompt
        )

        # 解析响应为结构化数据
        try:
            analysis = json.loads(response)
        except:
            # 如果解析失败，返回默认分析
            analysis = {
                "recommended_action": possible_actions[0] if possible_actions else "wait",
                "confidence": 0.5,
                "reasoning": response
            }

        return analysis

    def _build_role_system_prompt(self, role: Role) -> str:
        """构建角色专用的系统提示词"""
        base_prompt = "你是狼人杀游戏中的AI玩家，需要根据你的角色身份进行游戏。"

        role_specific_prompts = {
            Role.VILLAGER: f"{base_prompt} 你是村民，目标是找出并投票淘汰所有狼人。你需要通过观察、推理和讨论来识别狼人。",
            Role.WEREWOLF: f"{base_prompt} 你是狼人，目标是消灭所有村民而不被发现。你需要伪装成村民，误导其他玩家，保护队友。",
            Role.SEER: f"{base_prompt} 你是预言家，拥有查验他人身份的能力。你需要合理使用你的能力，并巧妙地引导村民找出狼人。",
            Role.WITCH: f"{base_prompt} 你是女巫，拥有解药和毒药。你需要在关键时刻使用药剂，帮助村民阵营获胜。",
            Role.GUARD: f"{base_prompt} 你是守卫，可以保护他人免受狼人攻击。你需要分析局势，保护重要的村民。",
            Role.HUNTER: f"{base_prompt} 你是猎人，死亡时可以开枪带走一人。你需要谨慎行动，确保你的枪不会误伤好人。"
        }

        return role_specific_prompts.get(role, base_prompt)

    def _build_strategy_system_prompt(self, role: Role) -> str:
        """构建策略分析专用的系统提示词"""
        return f"""你是狼人杀游戏中的{role.name}角色的策略分析AI。
请基于当前游戏状态和可选行动，分析最佳策略并以JSON格式返回结果。

返回格式要求：
{{
    "recommended_action": "推荐的行动",
    "confidence": 0.8,
    "reasoning": "详细的推理过程"
}}

分析时请考虑：
1. 当前角色的目标和能力
2. 游戏局势和存活玩家
3. 风险和收益评估
4. 长期策略规划"""

    def _build_speech_prompt(self, role: Role, game_state: GameState,
                           context: Dict[str, Any]) -> str:
        """构建发言提示词"""
        # 获取游戏基本信息
        alive_count = len(game_state.get_alive_players())
        phase = game_state.current_phase
        round_num = game_state.current_round
        
        # 获取角色特定信息
        role_info = self._get_role_context(role, game_state, context)
        
        prompt = f"""你是狼人杀游戏中的{role.name}角色。

游戏状态:
- 当前阶段: {phase.name}
- 回合数: {round_num}
- 存活玩家数: {alive_count}

角色信息:
{role_info}

请生成一段符合你角色身份的发言，要求:
1. 符合角色特点和立场
2. 长度控制在50字以内
3. 自然流畅，不要暴露真实身份（如果你是狼人）
4. 体现策略思考

发言内容:"""
        
        return prompt
    
    def _build_strategy_prompt(self, role: Role, game_state: GameState,
                             possible_actions: List[str]) -> str:
        """构建策略分析提示词"""
        prompt = f"""作为狼人杀游戏中的{role.name}，分析当前局势并选择最佳行动。

当前状态:
- 阶段: {game_state.current_phase.name}
- 存活玩家: {len(game_state.get_alive_players())}人
- 可选行动: {', '.join(possible_actions)}

请以JSON格式返回分析结果:
{{
    "recommended_action": "推荐的行动",
    "confidence": 0.8,
    "reasoning": "选择理由"
}}"""
        
        return prompt
    
    def _get_role_context(self, role: Role, game_state: GameState, 
                         context: Dict[str, Any]) -> str:
        """获取角色上下文信息"""
        if role == Role.WEREWOLF:
            werewolves = game_state.get_werewolves()
            teammates = [w.name for w in werewolves if w.player_id != context.get('player_id')]
            return f"你是狼人，队友: {', '.join(teammates) if teammates else '无'}"
        
        elif role == Role.SEER:
            return "你是预言家，拥有查验他人身份的能力"
        
        elif role == Role.WITCH:
            antidote = "已用" if game_state.witch_used_antidote else "可用"
            poison = "已用" if game_state.witch_used_poison else "可用"
            return f"你是女巫，解药: {antidote}，毒药: {poison}"
        
        elif role == Role.GUARD:
            return "你是守卫，可以保护他人免受狼人攻击"
        
        elif role == Role.HUNTER:
            return "你是猎人，死亡时可以开枪带走一人"
        
        else:
            return "你是村民，需要通过推理找出狼人"


# 预设配置
DEFAULT_CONFIGS = {
    "mock": LLMConfig(
        provider="mock",
        model_name="mock-model",
        max_tokens=100,
        temperature=0.7
    ),
    "openai_gpt35": LLMConfig(
        provider="openai",
        model_name="gpt-3.5-turbo",
        max_tokens=150,
        temperature=0.7
    ),
    "openai_gpt4": LLMConfig(
        provider="openai",
        model_name="gpt-4",
        max_tokens=200,
        temperature=0.6
    ),
    "qwen3_30b": LLMConfig(
        provider="openai",
        model_name="ckpt/Qwen/Qwen3-30B-A3B",
        api_key="EMPTY",
        base_url="http://localhost:8005/v1",
        max_tokens=200,
        temperature=0.7
    )
}


def create_llm_manager(config_name: str = "qwen3_30b", api_key: Optional[str] = None) -> LLMManager:
    """创建大模型管理器"""
    # 优先使用配置管理器
    try:
        from ..config.llm_config import get_llm_config
        config = get_llm_config(config_name)
    except ImportError:
        # 如果配置管理器不可用，使用默认配置
        if config_name not in DEFAULT_CONFIGS:
            config_name = "qwen3_30b"
        config = DEFAULT_CONFIGS[config_name]

    # 如果提供了API密钥，覆盖配置中的密钥
    if api_key:
        config.api_key = api_key

    return LLMManager(config)
