"""
预言家AI策略
实现预言家的查验策略、信息披露和领导能力
"""
import random
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field

from ..models.enums import Role, GamePhase, VoteType
from ..models.game_state import GameState
from ..models.player import PlayerInfo
from ..players.ai_player import AIPlayer
from .llm_integration import LLMManager, create_llm_manager


@dataclass
class CheckResult:
    """查验结果"""
    target_id: int
    target_name: str
    is_werewolf: bool
    round_checked: int
    revealed: bool = False  # 是否已公开


@dataclass
class SeerKnowledge:
    """预言家知识"""
    check_results: List[CheckResult] = field(default_factory=list)
    confirmed_werewolves: List[int] = field(default_factory=list)
    confirmed_villagers: List[int] = field(default_factory=list)
    suspicious_players: Dict[int, float] = field(default_factory=dict)
    trusted_players: List[int] = field(default_factory=list)
    revelation_strategy: str = "gradual"  # "immediate", "gradual", "strategic"


class SeerAI:
    """预言家AI策略"""
    
    def __init__(self, ai_player: AIPlayer, difficulty: str = "normal",
                use_llm: bool = True, llm_config: str = "qwen3_30b"):
        """初始化预言家AI"""
        self.ai_player = ai_player
        self.difficulty = difficulty
        self.knowledge = SeerKnowledge()
        self.use_llm = use_llm
        
        # 初始化大模型
        if use_llm:
            self.llm_manager = create_llm_manager(llm_config)
        else:
            self.llm_manager = None
        
        # 策略参数
        self.params = self._init_strategy_params()
    
    def _init_strategy_params(self) -> Dict[str, float]:
        """初始化策略参数"""
        params = {
            "revelation_threshold": 0.7,    # 公开信息的阈值
            "leadership_tendency": 0.8,     # 领导倾向
            "risk_tolerance": 0.6,          # 风险容忍度
            "trust_building": 0.7,          # 建立信任的能力
            "strategic_patience": 0.6,      # 战略耐心
            "information_value": 0.8,       # 信息价值评估
            "survival_priority": 0.7,       # 生存优先级
        }
        
        # 根据难度调整参数
        if self.difficulty == "easy":
            params["revelation_threshold"] = 0.5
            params["strategic_patience"] = 0.3
            params["risk_tolerance"] = 0.8
        elif self.difficulty == "hard":
            params["revelation_threshold"] = 0.9
            params["strategic_patience"] = 0.9
            params["risk_tolerance"] = 0.4
            params["leadership_tendency"] = 0.9
        
        return params
    
    def make_check_decision(self, game_state: GameState) -> Optional[int]:
        """做出查验决策"""
        # 获取可查验的目标
        targets = [p for p in game_state.get_alive_players() 
                  if p.player_id != self.ai_player.player_id]
        
        if not targets:
            return None
        
        # 过滤已查验的玩家
        unchecked_targets = [p for p in targets 
                           if not self._already_checked(p.player_id)]
        
        if not unchecked_targets:
            # 如果都查验过了，选择最可疑的重新查验
            unchecked_targets = targets
        
        # 计算查验优先级
        check_scores = self._calculate_check_priority(game_state, unchecked_targets)
        
        # 选择优先级最高的目标
        best_target = max(check_scores.items(), key=lambda x: x[1])[0]
        
        return best_target
    
    def _already_checked(self, player_id: int) -> bool:
        """检查是否已经查验过该玩家"""
        return any(result.target_id == player_id for result in self.knowledge.check_results)
    
    def _calculate_check_priority(self, game_state: GameState, 
                                targets: List[PlayerInfo]) -> Dict[int, float]:
        """计算查验优先级"""
        priority_scores = {}
        
        for target in targets:
            score = 0.5  # 基础分数
            
            # 根据怀疑度调整
            suspicion = self.knowledge.suspicious_players.get(target.player_id, 0.5)
            score += suspicion * 0.4
            
            # 优先查验活跃的玩家
            activity_level = self._assess_activity_level(game_state, target)
            score += activity_level * 0.2
            
            # 优先查验可能的领导者
            leadership_level = self._assess_leadership_level(game_state, target)
            score += leadership_level * 0.2
            
            # 避免查验明显的村民
            if target.player_id in self.knowledge.trusted_players:
                score *= 0.5
            
            # 随机因素
            score += random.uniform(-0.1, 0.1)
            
            priority_scores[target.player_id] = max(0.1, min(1.0, score))
        
        return priority_scores
    
    def _assess_activity_level(self, game_state: GameState, target: PlayerInfo) -> float:
        """评估玩家活跃度"""
        # 简化实现：基于投票参与度
        vote_count = 0
        total_votes = 0
        
        for round_info in game_state.rounds_history:
            total_votes += 1
            for vote in round_info.votes:
                if vote.voter_id == target.player_id:
                    vote_count += 1
                    break
        
        if total_votes == 0:
            return 0.5
        
        return vote_count / total_votes
    
    def _assess_leadership_level(self, game_state: GameState, target: PlayerInfo) -> float:
        """评估玩家领导力"""
        # 简化实现：基于发言频率和影响力
        return random.uniform(0.3, 0.7)
    
    def process_check_result(self, target_id: int, is_werewolf: bool, game_state: GameState):
        """处理查验结果"""
        target = game_state.players[target_id]
        
        result = CheckResult(
            target_id=target_id,
            target_name=target.name,
            is_werewolf=is_werewolf,
            round_checked=game_state.current_round
        )
        
        self.knowledge.check_results.append(result)
        
        # 更新知识库
        if is_werewolf:
            if target_id not in self.knowledge.confirmed_werewolves:
                self.knowledge.confirmed_werewolves.append(target_id)
        else:
            if target_id not in self.knowledge.confirmed_villagers:
                self.knowledge.confirmed_villagers.append(target_id)
    
    def make_vote_decision(self, game_state: GameState, vote_type: VoteType) -> Optional[int]:
        """做出投票决策"""
        if vote_type != VoteType.ELIMINATION:
            return None
        
        # 优先投票给确认的狼人
        alive_werewolves = [wid for wid in self.knowledge.confirmed_werewolves 
                          if game_state.players[wid].is_alive()]
        
        if alive_werewolves:
            return alive_werewolves[0]
        
        # 如果没有确认的狼人，投票给最可疑的
        targets = [p for p in game_state.get_alive_players() 
                  if p.player_id != self.ai_player.player_id]
        
        if not targets:
            return None
        
        # 计算投票分数
        vote_scores = self._calculate_vote_scores(game_state, targets)
        
        # 选择得分最高的目标
        best_target = max(vote_scores.items(), key=lambda x: x[1])[0]
        
        return best_target
    
    def _calculate_vote_scores(self, game_state: GameState, 
                             targets: List[PlayerInfo]) -> Dict[int, float]:
        """计算投票分数"""
        vote_scores = {}
        
        for target in targets:
            score = 0.3  # 基础分数较低（预言家比较谨慎）
            
            # 如果是确认的狼人，最高分
            if target.player_id in self.knowledge.confirmed_werewolves:
                score = 1.0
            
            # 如果是确认的村民，最低分
            elif target.player_id in self.knowledge.confirmed_villagers:
                score = 0.1
            
            # 根据怀疑度调整
            else:
                suspicion = self.knowledge.suspicious_players.get(target.player_id, 0.5)
                score += suspicion * 0.6
            
            vote_scores[target.player_id] = score
        
        return vote_scores
    
    def generate_speech(self, game_state: GameState, phase: GamePhase) -> str:
        """生成发言"""
        if phase != GamePhase.DAY_DISCUSSION:
            return "我没有什么要说的。"
        
        # 使用大模型生成发言
        if self.use_llm and self.llm_manager:
            context = {
                "player_id": self.ai_player.player_id,
                "check_results": [
                    {
                        "target_name": result.target_name,
                        "is_werewolf": result.is_werewolf,
                        "revealed": result.revealed
                    }
                    for result in self.knowledge.check_results
                ],
                "confirmed_werewolves": len(self.knowledge.confirmed_werewolves),
                "confirmed_villagers": len(self.knowledge.confirmed_villagers)
            }
            
            # 构建历史对话（包括查验结果信息）
            history = self._build_conversation_history(game_state)

            speech = self.llm_manager.generate_speech(
                role=Role.SEER,
                game_state=game_state,
                context=context,
                history=history
            )
            
            return speech
        
        # 基于规则生成发言
        return self._rule_based_speech_generation(game_state)

    def _build_conversation_history(self, game_state: GameState) -> List[Dict[str, str]]:
        """构建对话历史，包括查验结果信息"""
        history = []

        # 添加查验结果作为背景信息
        if self.knowledge.check_results:
            for result in self.knowledge.check_results[-3:]:  # 最近3次查验
                status = "狼人" if result.is_werewolf else "好人"
                revealed_status = "已公开" if result.revealed else "未公开"

                history.append({
                    "role": "system",
                    "content": f"查验结果: {result.target_name}是{status} ({revealed_status})"
                })

        # 添加确认的狼人信息
        if self.knowledge.confirmed_werewolves:
            werewolf_names = []
            for werewolf_id in self.knowledge.confirmed_werewolves:
                for player in game_state.players:
                    if player.player_id == werewolf_id:
                        werewolf_names.append(player.name)
                        break

            if werewolf_names:
                history.append({
                    "role": "system",
                    "content": f"已确认的狼人: {', '.join(werewolf_names)}"
                })

        # 添加确认的村民信息
        if self.knowledge.confirmed_villagers:
            villager_names = []
            for villager_id in self.knowledge.confirmed_villagers:
                for player in game_state.players:
                    if player.player_id == villager_id:
                        villager_names.append(player.name)
                        break

            if villager_names:
                history.append({
                    "role": "system",
                    "content": f"已确认的好人: {', '.join(villager_names)}"
                })

        return history
    
    def _rule_based_speech_generation(self, game_state: GameState) -> str:
        """基于规则生成发言"""
        # 决定是否公开信息
        should_reveal = self._should_reveal_information(game_state)
        
        if should_reveal:
            return self._generate_revelation_speech(game_state)
        else:
            return self._generate_guidance_speech(game_state)
    
    def _should_reveal_information(self, game_state: GameState) -> bool:
        """判断是否应该公开信息"""
        # 如果有确认的狼人且还未公开
        unrevealed_werewolves = [
            result for result in self.knowledge.check_results
            if result.is_werewolf and not result.revealed
        ]
        
        if unrevealed_werewolves:
            # 根据策略和风险评估决定
            if self.knowledge.revelation_strategy == "immediate":
                return True
            elif self.knowledge.revelation_strategy == "gradual":
                return random.random() < self.params["revelation_threshold"]
            else:  # strategic
                return self._strategic_revelation_decision(game_state)
        
        return False
    
    def _strategic_revelation_decision(self, game_state: GameState) -> bool:
        """战略性公开决策"""
        # 考虑当前局势
        alive_count = len(game_state.get_alive_players())
        werewolf_count = len([p for p in game_state.get_alive_players() if p.is_werewolf()])
        
        # 如果狼人数量接近村民，需要立即公开
        if werewolf_count >= alive_count // 2:
            return True
        
        # 如果自己处于危险中，考虑公开
        danger_level = self._assess_personal_danger(game_state)
        if danger_level > 0.7:
            return True
        
        return False
    
    def _assess_personal_danger(self, game_state: GameState) -> float:
        """评估个人危险程度"""
        # 简化实现：基于投票情况
        votes_against_me = 0
        total_votes = 0
        
        if game_state.current_round_info:
            for vote in game_state.current_round_info.votes:
                total_votes += 1
                if vote.target_id == self.ai_player.player_id:
                    votes_against_me += 1
        
        if total_votes == 0:
            return 0.3
        
        return votes_against_me / total_votes
    
    def _generate_revelation_speech(self, game_state: GameState) -> str:
        """生成公开信息的发言"""
        # 选择一个未公开的狼人结果
        unrevealed_werewolves = [
            result for result in self.knowledge.check_results
            if result.is_werewolf and not result.revealed
        ]
        
        if unrevealed_werewolves:
            result = unrevealed_werewolves[0]
            result.revealed = True
            
            speeches = [
                f"我是预言家，昨晚查验了{result.target_name}，他是狼人！",
                f"大家听我说，{result.target_name}是狼人，我查验过了！",
                f"我必须告诉大家，{result.target_name}的身份是狼人！"
            ]
            
            return random.choice(speeches)
        
        # 如果没有狼人要公开，公开好人信息
        unrevealed_villagers = [
            result for result in self.knowledge.check_results
            if not result.is_werewolf and not result.revealed
        ]
        
        if unrevealed_villagers:
            result = unrevealed_villagers[0]
            result.revealed = True
            
            return f"我查验过{result.target_name}，他是好人。"
        
        return "我是预言家，会在合适的时候公开信息。"
    
    def _generate_guidance_speech(self, game_state: GameState) -> str:
        """生成指导性发言"""
        speeches = [
            "我们需要仔细分析每个人的行为模式。",
            "从投票情况来看，有些人的动机值得怀疑。",
            "大家要冷静，不要被表象迷惑。",
            "我建议我们关注那些试图误导大家的人。",
            "让我们通过逻辑推理找出真相。"
        ]
        
        # 如果有可疑的人，针对性发言
        if self.knowledge.suspicious_players:
            most_suspicious = max(self.knowledge.suspicious_players.items(), 
                                key=lambda x: x[1])
            if most_suspicious[1] > 0.7:
                suspicious_player = game_state.players[most_suspicious[0]]
                speeches.append(f"我觉得{suspicious_player.name}的行为需要大家注意。")
        
        return random.choice(speeches)
    
    def process_game_event(self, event_type: str, event_data: Dict[str, Any]):
        """处理游戏事件"""
        if event_type == "seer_check_result":
            self._process_seer_result(event_data)
        elif event_type == "vote_cast":
            self._process_vote_event(event_data)
        elif event_type in ["player_eliminated", "player_killed"]:
            self._process_death_event(event_data)
    
    def _process_seer_result(self, event_data: Dict[str, Any]):
        """处理预言家查验结果"""
        if event_data.get("seer_id") == self.ai_player.player_id:
            target_id = event_data.get("target_id")
            is_werewolf = event_data.get("result", False)
            
            # 这里的result是角色信息，需要判断是否为狼人
            if hasattr(event_data.get("result"), "name"):
                is_werewolf = event_data.get("result") == Role.WEREWOLF
            
            self.process_check_result(target_id, is_werewolf, None)
    
    def _process_vote_event(self, event_data: Dict[str, Any]):
        """处理投票事件"""
        voter_id = event_data.get("voter_id")
        target_id = event_data.get("target_id")
        
        # 分析投票模式，更新怀疑度
        if voter_id and target_id:
            # 如果投票给确认的好人，增加怀疑度
            if target_id in self.knowledge.confirmed_villagers:
                current_suspicion = self.knowledge.suspicious_players.get(voter_id, 0.5)
                self.knowledge.suspicious_players[voter_id] = min(1.0, current_suspicion + 0.2)
            
            # 如果投票给确认的狼人，降低怀疑度
            elif target_id in self.knowledge.confirmed_werewolves:
                current_suspicion = self.knowledge.suspicious_players.get(voter_id, 0.5)
                self.knowledge.suspicious_players[voter_id] = max(0.1, current_suspicion - 0.2)
    
    def _process_death_event(self, event_data: Dict[str, Any]):
        """处理死亡事件"""
        player_id = event_data.get("player_id")
        role = event_data.get("role")
        
        if player_id and role:
            # 更新知识库
            if role == Role.WEREWOLF:
                if player_id not in self.knowledge.confirmed_werewolves:
                    self.knowledge.confirmed_werewolves.append(player_id)
            elif role in [Role.VILLAGER, Role.SEER, Role.WITCH, Role.GUARD, Role.HUNTER]:
                if player_id not in self.knowledge.confirmed_villagers:
                    self.knowledge.confirmed_villagers.append(player_id)
    
    def get_information_summary(self) -> Dict[str, Any]:
        """获取信息总结"""
        return {
            "total_checks": len(self.knowledge.check_results),
            "confirmed_werewolves": len(self.knowledge.confirmed_werewolves),
            "confirmed_villagers": len(self.knowledge.confirmed_villagers),
            "unrevealed_werewolves": len([r for r in self.knowledge.check_results 
                                        if r.is_werewolf and not r.revealed]),
            "revelation_strategy": self.knowledge.revelation_strategy
        }
