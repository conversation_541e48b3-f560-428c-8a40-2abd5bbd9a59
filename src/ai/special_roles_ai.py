"""
特殊角色AI策略
实现女巫、猎人、守卫等特殊角色的AI策略
"""
import random
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field

from ..models.enums import Role, GamePhase, VoteType
from ..models.game_state import GameState
from ..models.player import PlayerInfo
from ..players.ai_player import AIPlayer
from .llm_integration import LLMManager, create_llm_manager


@dataclass
class WitchMemory:
    """女巫记忆"""
    antidote_used: bool = False
    poison_used: bool = False
    saved_players: List[int] = field(default_factory=list)
    poisoned_players: List[int] = field(default_factory=list)
    known_werewolf_targets: List[int] = field(default_factory=list)
    suspected_werewolves: Dict[int, float] = field(default_factory=dict)


@dataclass
class GuardMemory:
    """守卫记忆"""
    protected_history: List[Tuple[int, int]] = field(default_factory=list)  # (round, player_id)
    last_protected: Optional[int] = None
    protection_strategy: str = "rotate"  # "rotate", "focus", "random"
    high_value_targets: List[int] = field(default_factory=list)


@dataclass
class HunterMemory:
    """猎人记忆"""
    revenge_target: Optional[int] = None
    threat_assessment: Dict[int, float] = field(default_factory=dict)
    strategic_value: Dict[int, float] = field(default_factory=dict)


class WitchAI:
    """女巫AI策略"""
    
    def __init__(self, ai_player: AIPlayer, difficulty: str = "normal",
                use_llm: bool = True, llm_config: str = "qwen3_30b"):
        self.ai_player = ai_player
        self.difficulty = difficulty
        self.memory = WitchMemory()
        self.use_llm = use_llm
        
        if use_llm:
            self.llm_manager = create_llm_manager(llm_config)
        else:
            self.llm_manager = None
        
        self.params = self._init_strategy_params()
    
    def _init_strategy_params(self) -> Dict[str, float]:
        """初始化策略参数"""
        params = {
            "save_threshold": 0.7,          # 救人阈值
            "poison_threshold": 0.8,        # 毒人阈值
            "self_preservation": 0.6,       # 自保意识
            "strategic_patience": 0.7,      # 战略耐心
            "information_value": 0.8,       # 信息价值评估
        }
        
        if self.difficulty == "easy":
            params["save_threshold"] = 0.5
            params["poison_threshold"] = 0.6
            params["strategic_patience"] = 0.4
        elif self.difficulty == "hard":
            params["save_threshold"] = 0.9
            params["poison_threshold"] = 0.9
            params["strategic_patience"] = 0.9
        
        return params
    
    def make_witch_decision(self, game_state: GameState, werewolf_target: Optional[int]) -> Tuple[Optional[int], Optional[int]]:
        """做出女巫决策：(救人目标, 毒人目标)"""
        save_target = None
        poison_target = None
        
        # 决定是否救人
        if not self.memory.antidote_used and werewolf_target:
            save_target = self._decide_save_target(game_state, werewolf_target)
        
        # 决定是否毒人
        if not self.memory.poison_used:
            poison_target = self._decide_poison_target(game_state)
        
        return save_target, poison_target
    
    def _decide_save_target(self, game_state: GameState, werewolf_target: int) -> Optional[int]:
        """决定救人目标"""
        target_player = game_state.players.get(werewolf_target)
        if not target_player:
            return None
        
        # 不救自己（通常规则）
        if werewolf_target == self.ai_player.player_id:
            return None
        
        # 评估救人价值
        save_value = self._calculate_save_value(game_state, target_player)
        
        if save_value >= self.params["save_threshold"]:
            self.memory.antidote_used = True
            self.memory.saved_players.append(werewolf_target)
            return werewolf_target
        
        return None
    
    def _calculate_save_value(self, game_state: GameState, target: PlayerInfo) -> float:
        """计算救人价值"""
        value = 0.5
        
        # 根据角色调整价值
        if target.role == Role.SEER:
            value += 0.4  # 预言家价值很高
        elif target.role == Role.GUARD:
            value += 0.2  # 守卫价值较高
        elif target.role == Role.HUNTER:
            value += 0.1  # 猎人价值中等
        
        # 根据游戏阶段调整
        alive_count = len(game_state.get_alive_players())
        if alive_count <= 6:  # 后期更谨慎
            value += 0.2
        
        # 如果是可疑的狼人，降低价值
        if target.player_id in self.memory.suspected_werewolves:
            suspicion = self.memory.suspected_werewolves[target.player_id]
            value -= suspicion * 0.5
        
        return max(0.0, min(1.0, value))
    
    def _decide_poison_target(self, game_state: GameState) -> Optional[int]:
        """决定毒人目标"""
        # 获取可毒杀的目标
        targets = [p for p in game_state.get_alive_players() 
                  if p.player_id != self.ai_player.player_id]
        
        if not targets:
            return None
        
        # 计算毒杀价值
        poison_scores = {}
        for target in targets:
            score = self._calculate_poison_value(game_state, target)
            poison_scores[target.player_id] = score
        
        # 选择价值最高的目标
        best_target = max(poison_scores.items(), key=lambda x: x[1])
        
        if best_target[1] >= self.params["poison_threshold"]:
            self.memory.poison_used = True
            self.memory.poisoned_players.append(best_target[0])
            return best_target[0]
        
        return None
    
    def _calculate_poison_value(self, game_state: GameState, target: PlayerInfo) -> float:
        """计算毒杀价值"""
        value = 0.3  # 基础价值较低（毒药珍贵）
        
        # 如果确认是狼人，价值很高
        if target.player_id in self.memory.suspected_werewolves:
            suspicion = self.memory.suspected_werewolves[target.player_id]
            value += suspicion * 0.6
        
        # 根据威胁程度调整
        threat_level = self._assess_threat_level(game_state, target)
        value += threat_level * 0.3
        
        return max(0.0, min(1.0, value))
    
    def _assess_threat_level(self, game_state: GameState, target: PlayerInfo) -> float:
        """评估威胁等级"""
        # 简化实现
        return random.uniform(0.3, 0.7)


class GuardAI:
    """守卫AI策略"""
    
    def __init__(self, ai_player: AIPlayer, difficulty: str = "normal",
                use_llm: bool = True, llm_config: str = "qwen3_30b"):
        self.ai_player = ai_player
        self.difficulty = difficulty
        self.memory = GuardMemory()
        self.use_llm = use_llm
        
        if use_llm:
            self.llm_manager = create_llm_manager(llm_config)
        else:
            self.llm_manager = None
        
        self.params = self._init_strategy_params()
    
    def _init_strategy_params(self) -> Dict[str, float]:
        """初始化策略参数"""
        params = {
            "protection_value_threshold": 0.6,  # 保护价值阈值
            "rotation_tendency": 0.7,           # 轮换倾向
            "focus_tendency": 0.5,              # 专注倾向
            "risk_assessment": 0.8,             # 风险评估能力
        }
        
        if self.difficulty == "easy":
            params["protection_value_threshold"] = 0.4
            params["risk_assessment"] = 0.5
        elif self.difficulty == "hard":
            params["protection_value_threshold"] = 0.8
            params["risk_assessment"] = 0.9
        
        return params
    
    def make_protection_decision(self, game_state: GameState) -> Optional[int]:
        """做出保护决策"""
        # 获取可保护的目标
        targets = [p for p in game_state.get_alive_players() 
                  if p.player_id != self.ai_player.player_id]
        
        if not targets:
            return None
        
        # 过滤掉上次保护的玩家（如果有连续保护限制）
        if self.memory.last_protected:
            targets = [t for t in targets if t.player_id != self.memory.last_protected]
        
        if not targets:
            return None
        
        # 计算保护价值
        protection_scores = {}
        for target in targets:
            score = self._calculate_protection_value(game_state, target)
            protection_scores[target.player_id] = score
        
        # 选择价值最高的目标
        best_target = max(protection_scores.items(), key=lambda x: x[1])
        
        if best_target[1] >= self.params["protection_value_threshold"]:
            target_id = best_target[0]
            self.memory.last_protected = target_id
            self.memory.protected_history.append((game_state.current_round, target_id))
            return target_id
        
        return None
    
    def _calculate_protection_value(self, game_state: GameState, target: PlayerInfo) -> float:
        """计算保护价值"""
        value = 0.5
        
        # 根据角色调整价值
        if target.role == Role.SEER:
            value += 0.4  # 预言家价值很高
        elif target.role == Role.WITCH:
            value += 0.3  # 女巫价值较高
        elif target.role == Role.HUNTER:
            value += 0.1  # 猎人价值中等
        
        # 根据威胁程度调整
        threat_level = self._assess_kill_probability(game_state, target)
        value += threat_level * 0.3
        
        # 根据保护历史调整
        protection_count = sum(1 for _, pid in self.memory.protected_history 
                             if pid == target.player_id)
        if protection_count > 0:
            value -= protection_count * 0.1  # 避免过度保护同一人
        
        return max(0.0, min(1.0, value))
    
    def _assess_kill_probability(self, game_state: GameState, target: PlayerInfo) -> float:
        """评估被杀概率"""
        # 简化实现：基于角色和活跃度
        probability = 0.5
        
        if target.role in [Role.SEER, Role.WITCH]:
            probability += 0.3
        elif target.role == Role.HUNTER:
            probability -= 0.2  # 狼人可能避免杀猎人
        
        return max(0.0, min(1.0, probability))


class HunterAI:
    """猎人AI策略"""
    
    def __init__(self, ai_player: AIPlayer, difficulty: str = "normal",
                use_llm: bool = True, llm_config: str = "qwen3_30b"):
        self.ai_player = ai_player
        self.difficulty = difficulty
        self.memory = HunterMemory()
        self.use_llm = use_llm
        
        if use_llm:
            self.llm_manager = create_llm_manager(llm_config)
        else:
            self.llm_manager = None
        
        self.params = self._init_strategy_params()
    
    def _init_strategy_params(self) -> Dict[str, float]:
        """初始化策略参数"""
        params = {
            "revenge_threshold": 0.7,       # 复仇阈值
            "strategic_value": 0.8,         # 战略价值评估
            "risk_tolerance": 0.6,          # 风险容忍度
            "information_confidence": 0.7,   # 信息信心度
        }
        
        if self.difficulty == "easy":
            params["revenge_threshold"] = 0.5
            params["information_confidence"] = 0.5
        elif self.difficulty == "hard":
            params["revenge_threshold"] = 0.9
            params["information_confidence"] = 0.9
        
        return params
    
    def make_revenge_decision(self, game_state: GameState) -> Optional[int]:
        """做出复仇决策"""
        # 获取可射击的目标
        targets = [p for p in game_state.get_alive_players() 
                  if p.player_id != self.ai_player.player_id]
        
        if not targets:
            return None
        
        # 计算射击价值
        shoot_scores = {}
        for target in targets:
            score = self._calculate_shoot_value(game_state, target)
            shoot_scores[target.player_id] = score
        
        # 选择价值最高的目标
        best_target = max(shoot_scores.items(), key=lambda x: x[1])
        
        if best_target[1] >= self.params["revenge_threshold"]:
            return best_target[0]
        
        # 如果没有明确目标，随机选择一个可疑的
        suspicious_targets = [tid for tid, score in shoot_scores.items() if score > 0.5]
        if suspicious_targets:
            return random.choice(suspicious_targets)
        
        return None
    
    def _calculate_shoot_value(self, game_state: GameState, target: PlayerInfo) -> float:
        """计算射击价值"""
        value = 0.3  # 基础价值较低（射击机会珍贵）
        
        # 根据威胁评估调整
        threat = self.memory.threat_assessment.get(target.player_id, 0.5)
        value += threat * 0.4
        
        # 根据战略价值调整
        strategic = self.memory.strategic_value.get(target.player_id, 0.5)
        value += strategic * 0.3
        
        # 如果是狼人的可能性很高
        if target.is_werewolf():  # 这在实际游戏中猎人不知道
            value += 0.5
        
        return max(0.0, min(1.0, value))


class SpecialRoleAIManager:
    """特殊角色AI管理器"""
    
    def __init__(self):
        self.role_ais = {}
    
    def create_role_ai(self, role: Role, ai_player: AIPlayer,
                      difficulty: str = "normal", use_llm: bool = True,
                      llm_config: str = "qwen3_30b"):
        """创建角色AI"""
        if role == Role.WITCH:
            return WitchAI(ai_player, difficulty, use_llm, llm_config)
        elif role == Role.GUARD:
            return GuardAI(ai_player, difficulty, use_llm, llm_config)
        elif role == Role.HUNTER:
            return HunterAI(ai_player, difficulty, use_llm, llm_config)
        else:
            return None
    
    def register_role_ai(self, player_id: int, role_ai):
        """注册角色AI"""
        self.role_ais[player_id] = role_ai
    
    def get_role_ai(self, player_id: int):
        """获取角色AI"""
        return self.role_ais.get(player_id)
    
    def process_night_actions(self, game_state: GameState) -> Dict[str, Any]:
        """处理夜晚行动"""
        actions = {}
        
        for player_id, role_ai in self.role_ais.items():
            player = game_state.players.get(player_id)
            if not player or not player.is_alive():
                continue
            
            if isinstance(role_ai, WitchAI):
                # 女巫需要知道狼人目标
                werewolf_target = self._get_werewolf_target(game_state)
                save_target, poison_target = role_ai.make_witch_decision(game_state, werewolf_target)
                actions[player_id] = {
                    "role": "witch",
                    "save_target": save_target,
                    "poison_target": poison_target
                }
            
            elif isinstance(role_ai, GuardAI):
                protection_target = role_ai.make_protection_decision(game_state)
                actions[player_id] = {
                    "role": "guard",
                    "protection_target": protection_target
                }
        
        return actions
    
    def _get_werewolf_target(self, game_state: GameState) -> Optional[int]:
        """获取狼人目标（简化实现）"""
        # 在实际游戏中，这个信息需要从夜晚阶段管理器获取
        return None
