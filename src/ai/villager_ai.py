"""
村民AI策略
实现村民的投票策略、发言逻辑和推理能力
"""
import random
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field

from ..models.enums import Role, GamePhase, VoteType, Faction
from ..models.game_state import GameState
from ..models.player import PlayerInfo, PlayerAction, PlayerVote
from ..players.ai_player import AIPlayer
from .llm_integration import LLMManager, create_llm_manager


@dataclass
class SuspicionData:
    """怀疑度数据"""
    player_id: int
    suspicion_level: float = 0.0  # 0.0-1.0，越高越可疑
    evidence: List[str] = field(default_factory=list)
    last_updated_round: int = 0


@dataclass
class VillagerMemory:
    """村民记忆"""
    # 怀疑记录
    suspicions: Dict[int, SuspicionData] = field(default_factory=dict)
    # 信任的玩家
    trusted_players: List[int] = field(default_factory=list)
    # 确认的狼人
    confirmed_werewolves: List[int] = field(default_factory=list)
    # 确认的好人
    confirmed_villagers: List[int] = field(default_factory=list)
    # 投票历史
    vote_history: List[Tuple[int, int, int]] = field(default_factory=list)  # (round, target_id, reason_code)
    # 发言历史
    speech_history: List[Tuple[int, str]] = field(default_factory=list)  # (round, speech)
    # 死亡玩家分析
    death_analysis: Dict[int, Dict[str, Any]] = field(default_factory=dict)


class VillagerAI:
    """村民AI策略"""
    
    def __init__(self, ai_player: AIPlayer, difficulty: str = "normal",
                use_llm: bool = True, llm_config: str = "qwen3_30b"):
        """初始化村民AI
        
        Args:
            ai_player: AI玩家实例
            difficulty: 难度 ("easy", "normal", "hard")
            use_llm: 是否使用大模型
            llm_config: 大模型配置名称
        """
        self.ai_player = ai_player
        self.difficulty = difficulty
        self.memory = VillagerMemory()
        self.use_llm = use_llm
        
        # 初始化大模型
        if use_llm:
            self.llm_manager = create_llm_manager(llm_config)
        else:
            self.llm_manager = None
        
        # 策略参数
        self.params = self._init_strategy_params()
    
    def _init_strategy_params(self) -> Dict[str, float]:
        """初始化策略参数"""
        # 基础参数
        params = {
            "random_factor": 0.1,           # 随机因素
            "vote_pattern_weight": 0.3,     # 投票模式权重
            "speech_analysis_weight": 0.4,  # 发言分析权重
            "death_pattern_weight": 0.2,    # 死亡模式权重
            "trust_threshold": 0.7,         # 信任阈值
            "suspicion_threshold": 0.7,     # 怀疑阈值
            "memory_decay": 0.1,            # 记忆衰减
            "evidence_weight": 0.2,         # 证据权重
        }
        
        # 根据难度调整参数
        if self.difficulty == "easy":
            params["random_factor"] = 0.3
            params["vote_pattern_weight"] = 0.2
            params["speech_analysis_weight"] = 0.2
            params["suspicion_threshold"] = 0.6
        elif self.difficulty == "hard":
            params["random_factor"] = 0.05
            params["vote_pattern_weight"] = 0.4
            params["speech_analysis_weight"] = 0.5
            params["suspicion_threshold"] = 0.8
            params["memory_decay"] = 0.05
        
        return params
    
    def make_vote_decision(self, game_state: GameState, vote_type: VoteType) -> Optional[int]:
        """做出投票决策"""
        if vote_type != VoteType.ELIMINATION:
            return None  # 村民只参与淘汰投票
        
        # 获取存活玩家
        alive_players = [p for p in game_state.get_alive_players() 
                        if p.player_id != self.ai_player.player_id]
        if not alive_players:
            return None
        
        # 更新记忆和分析
        self._update_memory(game_state)
        
        # 计算每个玩家的怀疑度
        suspicion_scores = self._calculate_suspicion_scores(game_state, alive_players)
        
        # 根据难度决定是否添加随机因素
        if random.random() < self.params["random_factor"]:
            # 简单随机选择
            return random.choice(alive_players).player_id
        
        # 选择怀疑度最高的玩家
        target_id = max(suspicion_scores.items(), key=lambda x: x[1])[0]
        
        # 记录投票决策
        self.memory.vote_history.append((game_state.current_round, target_id, 1))
        
        return target_id
    
    def _calculate_suspicion_scores(self, game_state: GameState, 
                                  alive_players: List[PlayerInfo]) -> Dict[int, float]:
        """计算怀疑度分数"""
        suspicion_scores = {}
        
        for player in alive_players:
            player_id = player.player_id
            
            # 如果是确认的狼人，给予最高怀疑度
            if player_id in self.memory.confirmed_werewolves:
                suspicion_scores[player_id] = 1.0
                continue
            
            # 如果是确认的好人，给予最低怀疑度
            if player_id in self.memory.confirmed_villagers:
                suspicion_scores[player_id] = 0.1
                continue
            
            # 基础怀疑度
            base_suspicion = 0.5
            
            # 从记忆中获取怀疑数据
            suspicion_data = self.memory.suspicions.get(player_id)
            if suspicion_data:
                # 应用记忆衰减
                rounds_passed = game_state.current_round - suspicion_data.last_updated_round
                decay = self.params["memory_decay"] * rounds_passed
                adjusted_suspicion = max(0.1, suspicion_data.suspicion_level - decay)
                
                # 根据证据数量调整
                evidence_bonus = min(0.3, len(suspicion_data.evidence) * self.params["evidence_weight"])
                
                base_suspicion = adjusted_suspicion + evidence_bonus
            
            # 分析投票模式
            vote_suspicion = self._analyze_vote_patterns(game_state, player_id)
            
            # 分析发言
            speech_suspicion = self._analyze_speech_patterns(game_state, player_id)
            
            # 计算最终怀疑度
            final_suspicion = (
                base_suspicion * 0.4 +
                vote_suspicion * self.params["vote_pattern_weight"] +
                speech_suspicion * self.params["speech_analysis_weight"]
            )
            
            # 确保在0-1范围内
            suspicion_scores[player_id] = max(0.1, min(1.0, final_suspicion))
        
        return suspicion_scores
    
    def _analyze_vote_patterns(self, game_state: GameState, player_id: int) -> float:
        """分析投票模式"""
        suspicion = 0.5  # 默认中等怀疑度
        
        # 获取该玩家的投票历史
        player_votes = []
        for round_info in game_state.rounds_history:
            for vote in round_info.votes:
                if vote.voter_id == player_id:
                    player_votes.append(vote)
        
        if not player_votes:
            return suspicion
        
        # 分析投票给已确认狼人的次数
        votes_for_werewolves = sum(1 for vote in player_votes 
                                 if vote.target_id in self.memory.confirmed_werewolves)
        
        # 分析投票给已确认好人的次数
        votes_for_villagers = sum(1 for vote in player_votes 
                                if vote.target_id in self.memory.confirmed_villagers)
        
        # 如果有投票给确认的狼人，降低怀疑度
        if votes_for_werewolves > 0:
            suspicion -= 0.2 * votes_for_werewolves
        
        # 如果有投票给确认的好人，增加怀疑度
        if votes_for_villagers > 0:
            suspicion += 0.2 * votes_for_villagers
        
        # 确保在0-1范围内
        return max(0.1, min(0.9, suspicion))
    
    def _analyze_speech_patterns(self, game_state: GameState, player_id: int) -> float:
        """分析发言模式"""
        # 简化实现，实际中可以使用NLP或大模型分析
        return 0.5  # 默认中等怀疑度
    
    def _update_memory(self, game_state: GameState):
        """更新记忆"""
        # 更新回合信息
        current_round = game_state.current_round
        
        # 分析新的死亡玩家
        if game_state.current_round_info:
            # 分析夜晚死亡
            for player_id in game_state.current_round_info.night_deaths:
                if player_id not in self.memory.death_analysis:
                    player = game_state.players[player_id]
                    self.memory.death_analysis[player_id] = {
                        "round": current_round,
                        "phase": "night",
                        "role": player.role
                    }
            
            # 分析白天淘汰
            if game_state.current_round_info.eliminated_player_id:
                player_id = game_state.current_round_info.eliminated_player_id
                if player_id not in self.memory.death_analysis:
                    player = game_state.players[player_id]
                    self.memory.death_analysis[player_id] = {
                        "round": current_round,
                        "phase": "day",
                        "role": player.role
                    }
        
        # 更新怀疑度
        for player_id, player in game_state.players.items():
            if player_id == self.ai_player.player_id:
                continue
            
            if player_id not in self.memory.suspicions:
                self.memory.suspicions[player_id] = SuspicionData(
                    player_id=player_id,
                    suspicion_level=0.5,
                    last_updated_round=current_round
                )
    
    def generate_speech(self, game_state: GameState, phase: GamePhase) -> str:
        """生成发言"""
        if phase != GamePhase.DAY_DISCUSSION:
            return "我没有什么要说的。"
        
        # 使用大模型生成发言
        if self.use_llm and self.llm_manager:
            context = {
                "player_id": self.ai_player.player_id,
                "suspicions": {pid: data.suspicion_level for pid, data in self.memory.suspicions.items()},
                "confirmed_werewolves": self.memory.confirmed_werewolves,
                "confirmed_villagers": self.memory.confirmed_villagers
            }
            
            # 构建历史对话（如果有的话）
            history = self._build_conversation_history(game_state)

            speech = self.llm_manager.generate_speech(
                role=Role.VILLAGER,
                game_state=game_state,
                context=context,
                history=history
            )
            
            # 记录发言
            self.memory.speech_history.append((game_state.current_round, speech))
            return speech
        
        # 基于规则生成发言
        return self._rule_based_speech_generation(game_state)

    def _build_conversation_history(self, game_state: GameState) -> List[Dict[str, str]]:
        """构建对话历史"""
        history = []

        # 添加最近的发言历史（最多5条）
        recent_speeches = self.memory.speech_history[-5:] if self.memory.speech_history else []

        for round_num, speech in recent_speeches:
            history.append({
                "role": "assistant",
                "content": f"第{round_num}轮我说: {speech}"
            })

        # 添加重要的游戏事件
        if hasattr(game_state, 'recent_events'):
            for event in game_state.recent_events[-3:]:  # 最近3个事件
                history.append({
                    "role": "user",
                    "content": f"游戏事件: {event}"
                })

        return history
    
    def _rule_based_speech_generation(self, game_state: GameState) -> str:
        """基于规则生成发言"""
        # 获取最可疑的玩家
        alive_players = [p for p in game_state.get_alive_players() 
                        if p.player_id != self.ai_player.player_id]
        
        if not alive_players:
            return "我们需要仔细分析局势。"
        
        suspicion_scores = self._calculate_suspicion_scores(game_state, alive_players)
        most_suspicious = max(suspicion_scores.items(), key=lambda x: x[1])
        
        suspicious_player_id = most_suspicious[0]
        suspicious_player = game_state.players[suspicious_player_id]
        suspicion_level = most_suspicious[1]
        
        # 根据怀疑度生成不同的发言
        if suspicion_level > self.params["suspicion_threshold"]:
            speech = f"我认为{suspicious_player.name}很可疑，大家要注意。"
        elif suspicion_level > 0.5:
            speech = f"{suspicious_player.name}的行为有些奇怪，值得关注。"
        else:
            speech = "目前还没有明显可疑的人，需要继续观察。"
        
        # 记录发言
        self.memory.speech_history.append((game_state.current_round, speech))
        
        return speech
    
    def process_game_event(self, event_type: str, event_data: Dict[str, Any]):
        """处理游戏事件"""
        # 处理预言家查验结果
        if event_type == "seer_check_result":
            self._process_seer_result(event_data)
        
        # 处理投票事件
        elif event_type == "vote_cast":
            self._process_vote_event(event_data)
        
        # 处理玩家死亡
        elif event_type in ["player_eliminated", "player_killed"]:
            self._process_death_event(event_data)
    
    def _process_seer_result(self, event_data: Dict[str, Any]):
        """处理预言家查验结果"""
        # 村民不会直接收到预言家结果，除非预言家公开
        pass
    
    def _process_vote_event(self, event_data: Dict[str, Any]):
        """处理投票事件"""
        voter_id = event_data.get("voter_id")
        target_id = event_data.get("target_id")
        
        if not voter_id or not target_id:
            return
        
        # 更新对投票者的分析
        if voter_id in self.memory.suspicions:
            suspicion = self.memory.suspicions[voter_id]
            
            # 如果投票给确认的狼人，降低怀疑度
            if target_id in self.memory.confirmed_werewolves:
                suspicion.suspicion_level = max(0.1, suspicion.suspicion_level - 0.2)
                suspicion.evidence.append(f"投票给确认的狼人 {target_id}")
            
            # 如果投票给确认的好人，增加怀疑度
            elif target_id in self.memory.confirmed_villagers:
                suspicion.suspicion_level = min(0.9, suspicion.suspicion_level + 0.2)
                suspicion.evidence.append(f"投票给确认的好人 {target_id}")
    
    def _process_death_event(self, event_data: Dict[str, Any]):
        """处理死亡事件"""
        player_id = event_data.get("player_id")
        role = event_data.get("role")
        
        if not player_id or not role:
            return
        
        # 记录死亡角色信息
        if isinstance(role, Role):
            # 如果是狼人，添加到确认的狼人列表
            if role == Role.WEREWOLF:
                if player_id not in self.memory.confirmed_werewolves:
                    self.memory.confirmed_werewolves.append(player_id)
            
            # 如果是村民阵营，添加到确认的好人列表
            elif role in [Role.VILLAGER, Role.SEER, Role.WITCH, Role.GUARD, Role.HUNTER]:
                if player_id not in self.memory.confirmed_villagers:
                    self.memory.confirmed_villagers.append(player_id)
