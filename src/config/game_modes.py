"""
游戏模式配置
定义各种预设的游戏模式和规则变体
"""
from typing import Dict, List, Optional
from dataclasses import dataclass, field
from ..models.enums import Role
from ..models.game_state import GameConfig


@dataclass
class GameModeConfig:
    """游戏模式配置"""
    name: str
    description: str
    min_players: int
    max_players: int
    recommended_players: int
    base_config: GameConfig
    special_rules: Dict[str, any] = field(default_factory=dict)
    difficulty: str = "normal"  # easy, normal, hard
    
    def create_config_for_players(self, player_count: int) -> Optional[GameConfig]:
        """为指定玩家数创建配置"""
        if not (self.min_players <= player_count <= self.max_players):
            return None
        
        # 根据玩家数调整角色分配
        role_dist = self._calculate_role_distribution(player_count)
        
        config = GameConfig(
            total_players=player_count,
            role_distribution=role_dist,
            enable_special_roles=self.base_config.enable_special_roles,
            discussion_time_limit=self.base_config.discussion_time_limit,
            voting_time_limit=self.base_config.voting_time_limit,
            allow_tie_votes=self.base_config.allow_tie_votes
        )
        
        # 应用特殊规则
        self._apply_special_rules(config)
        
        return config
    
    def _calculate_role_distribution(self, player_count: int) -> Dict[Role, int]:
        """计算角色分配"""
        # 基础分配算法
        werewolf_count = max(1, player_count // 4)  # 约25%狼人
        
        # 特殊角色分配
        special_roles = {}
        remaining_players = player_count - werewolf_count
        
        if self.base_config.enable_special_roles:
            if remaining_players >= 3:
                special_roles[Role.SEER] = 1
                remaining_players -= 1
            
            if remaining_players >= 3:
                special_roles[Role.WITCH] = 1
                remaining_players -= 1
            
            if remaining_players >= 2 and player_count >= 8:
                special_roles[Role.GUARD] = 1
                remaining_players -= 1
            
            if remaining_players >= 2 and player_count >= 10:
                special_roles[Role.HUNTER] = 1
                remaining_players -= 1
        
        # 剩余都是村民
        villager_count = remaining_players
        
        role_dist = {Role.WEREWOLF: werewolf_count, Role.VILLAGER: villager_count}
        role_dist.update(special_roles)
        
        return role_dist
    
    def _apply_special_rules(self, config: GameConfig):
        """应用特殊规则"""
        for rule_name, rule_value in self.special_rules.items():
            if rule_name == "fast_game":
                config.discussion_time_limit = 120
                config.voting_time_limit = 30
            elif rule_name == "allow_ties":
                config.allow_tie_votes = rule_value
            elif rule_name == "no_special_roles":
                config.enable_special_roles = False


class GameModeManager:
    """游戏模式管理器"""
    
    def __init__(self):
        self.modes = self._initialize_default_modes()
    
    def _initialize_default_modes(self) -> Dict[str, GameModeConfig]:
        """初始化默认游戏模式"""
        modes = {}
        
        # 经典模式
        modes["classic"] = GameModeConfig(
            name="经典模式",
            description="标准的狼人杀游戏，包含基础角色和规则",
            min_players=6,
            max_players=12,
            recommended_players=8,
            base_config=GameConfig(
                total_players=8,
                role_distribution={Role.VILLAGER: 4, Role.WEREWOLF: 2, Role.SEER: 1, Role.WITCH: 1},
                enable_special_roles=True,
                discussion_time_limit=300,
                voting_time_limit=60,
                allow_tie_votes=False
            ),
            difficulty="normal"
        )
        
        # 简单模式
        modes["simple"] = GameModeConfig(
            name="简单模式",
            description="只有村民和狼人的简化版本，适合新手",
            min_players=4,
            max_players=8,
            recommended_players=6,
            base_config=GameConfig(
                total_players=6,
                role_distribution={Role.VILLAGER: 4, Role.WEREWOLF: 2},
                enable_special_roles=False,
                discussion_time_limit=180,
                voting_time_limit=45,
                allow_tie_votes=True
            ),
            special_rules={"no_special_roles": True, "allow_ties": True},
            difficulty="easy"
        )
        
        # 快速模式
        modes["quick"] = GameModeConfig(
            name="快速模式",
            description="时间限制更短的快节奏游戏",
            min_players=6,
            max_players=10,
            recommended_players=8,
            base_config=GameConfig(
                total_players=8,
                role_distribution={Role.VILLAGER: 4, Role.WEREWOLF: 2, Role.SEER: 1, Role.WITCH: 1},
                enable_special_roles=True,
                discussion_time_limit=120,
                voting_time_limit=30,
                allow_tie_votes=False
            ),
            special_rules={"fast_game": True},
            difficulty="normal"
        )
        
        # 复杂模式
        modes["complex"] = GameModeConfig(
            name="复杂模式",
            description="包含更多特殊角色的高级游戏",
            min_players=8,
            max_players=16,
            recommended_players=12,
            base_config=GameConfig(
                total_players=12,
                role_distribution={
                    Role.VILLAGER: 5, Role.WEREWOLF: 3, Role.SEER: 1, 
                    Role.WITCH: 1, Role.GUARD: 1, Role.HUNTER: 1
                },
                enable_special_roles=True,
                discussion_time_limit=400,
                voting_time_limit=90,
                allow_tie_votes=False
            ),
            difficulty="hard"
        )
        
        # 平衡模式
        modes["balanced"] = GameModeConfig(
            name="平衡模式",
            description="经过平衡调整的竞技版本",
            min_players=8,
            max_players=12,
            recommended_players=10,
            base_config=GameConfig(
                total_players=10,
                role_distribution={
                    Role.VILLAGER: 4, Role.WEREWOLF: 3, Role.SEER: 1, 
                    Role.WITCH: 1, Role.GUARD: 1
                },
                enable_special_roles=True,
                discussion_time_limit=300,
                voting_time_limit=60,
                allow_tie_votes=False
            ),
            difficulty="normal"
        )
        
        return modes
    
    def get_mode(self, mode_name: str) -> Optional[GameModeConfig]:
        """获取游戏模式"""
        return self.modes.get(mode_name)
    
    def get_available_modes(self) -> List[str]:
        """获取可用模式列表"""
        return list(self.modes.keys())
    
    def get_modes_for_player_count(self, player_count: int) -> List[str]:
        """获取适合指定玩家数的模式"""
        suitable_modes = []
        
        for mode_name, mode_config in self.modes.items():
            if mode_config.min_players <= player_count <= mode_config.max_players:
                suitable_modes.append(mode_name)
        
        return suitable_modes
    
    def create_custom_mode(self, name: str, description: str, player_count: int, 
                          role_distribution: Dict[Role, int], **kwargs) -> GameModeConfig:
        """创建自定义模式"""
        base_config = GameConfig(
            total_players=player_count,
            role_distribution=role_distribution,
            enable_special_roles=kwargs.get("enable_special_roles", True),
            discussion_time_limit=kwargs.get("discussion_time_limit", 300),
            voting_time_limit=kwargs.get("voting_time_limit", 60),
            allow_tie_votes=kwargs.get("allow_tie_votes", False)
        )
        
        custom_mode = GameModeConfig(
            name=name,
            description=description,
            min_players=player_count,
            max_players=player_count,
            recommended_players=player_count,
            base_config=base_config,
            special_rules=kwargs.get("special_rules", {}),
            difficulty=kwargs.get("difficulty", "normal")
        )
        
        return custom_mode
    
    def add_mode(self, mode_name: str, mode_config: GameModeConfig):
        """添加新模式"""
        self.modes[mode_name] = mode_config
    
    def get_mode_info(self, mode_name: str) -> Optional[Dict[str, any]]:
        """获取模式详细信息"""
        mode = self.get_mode(mode_name)
        if not mode:
            return None
        
        return {
            "name": mode.name,
            "description": mode.description,
            "player_range": f"{mode.min_players}-{mode.max_players}",
            "recommended_players": mode.recommended_players,
            "difficulty": mode.difficulty,
            "role_distribution": {role.name: count for role, count in mode.base_config.role_distribution.items()},
            "special_rules": mode.special_rules,
            "time_limits": {
                "discussion": mode.base_config.discussion_time_limit,
                "voting": mode.base_config.voting_time_limit
            },
            "allow_tie_votes": mode.base_config.allow_tie_votes
        }
    
    def validate_custom_config(self, player_count: int, role_distribution: Dict[Role, int]) -> Dict[str, any]:
        """验证自定义配置"""
        errors = []
        warnings = []
        
        # 检查玩家总数
        total_roles = sum(role_distribution.values())
        if total_roles != player_count:
            errors.append(f"角色总数 ({total_roles}) 与玩家数 ({player_count}) 不匹配")
        
        # 检查狼人数量
        werewolf_count = role_distribution.get(Role.WEREWOLF, 0)
        if werewolf_count == 0:
            errors.append("必须至少有1个狼人")
        elif werewolf_count >= player_count / 2:
            warnings.append("狼人数量过多，可能导致游戏不平衡")
        
        # 检查村民数量
        villager_count = role_distribution.get(Role.VILLAGER, 0)
        if villager_count == 0:
            warnings.append("没有普通村民，游戏可能过于复杂")
        
        # 检查特殊角色
        special_roles = [role for role in role_distribution.keys() if role not in [Role.VILLAGER, Role.WEREWOLF]]
        if len(special_roles) > player_count // 2:
            warnings.append("特殊角色过多，可能影响游戏平衡")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings,
            "balance_score": self._calculate_balance_score(role_distribution)
        }
    
    def _calculate_balance_score(self, role_distribution: Dict[Role, int]) -> float:
        """计算配置平衡分数 (0-1, 1为最平衡)"""
        werewolf_count = role_distribution.get(Role.WEREWOLF, 0)
        total_players = sum(role_distribution.values())
        
        if total_players == 0:
            return 0.0
        
        # 理想狼人比例约为25%
        werewolf_ratio = werewolf_count / total_players
        ideal_ratio = 0.25
        
        # 计算偏离度
        deviation = abs(werewolf_ratio - ideal_ratio)
        balance_score = max(0.0, 1.0 - deviation * 4)  # 偏离25%时分数为0
        
        return balance_score
