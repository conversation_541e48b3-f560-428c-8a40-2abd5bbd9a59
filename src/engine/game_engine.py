"""
游戏引擎
负责游戏的核心逻辑、回合管理和状态转换
"""
import random
from typing import List, Dict, Optional, Callable
from datetime import datetime

from ..models.enums import GamePhase, GameResult, Role, VoteType
from ..models.game_state import GameState, GameConfig, RoundInfo
from ..models.player import PlayerInfo, PlayerAction
from .day_phase import DayPhaseManager
from .night_phase import NightPhaseManager
from .victory_conditions import VictoryConditionChecker
from .game_history import GameHistoryManager


class GameEngine:
    """游戏引擎类"""

    def __init__(self, config: GameConfig):
        """初始化游戏引擎"""
        self.config = config
        self.game_state: Optional[GameState] = None
        self.event_handlers: Dict[str, List[Callable]] = {}

        # Phase 2 新增管理器
        self.day_phase_manager: Optional[DayPhaseManager] = None
        self.night_phase_manager: Optional[NightPhaseManager] = None
        self.victory_checker: Optional[VictoryConditionChecker] = None
        self.history_manager: Optional[GameHistoryManager] = None
        
    def create_game(self, game_id: str, player_names: List[str]) -> GameState:
        """创建新游戏"""
        if len(player_names) != self.config.total_players:
            raise ValueError(f"玩家数量不匹配：期望 {self.config.total_players}，实际 {len(player_names)}")
        
        # 分配角色
        roles = self._distribute_roles()
        
        # 创建玩家
        players = {}
        for i, (name, role) in enumerate(zip(player_names, roles)):
            player = PlayerInfo(
                player_id=i + 1,
                name=name,
                role=role
            )
            players[player.player_id] = player
        
        # 创建游戏状态
        self.game_state = GameState(
            game_id=game_id,
            config=self.config,
            players=players,
            current_phase=GamePhase.SETUP
        )

        # 初始化Phase 2管理器
        self.day_phase_manager = DayPhaseManager(self.game_state)
        self.night_phase_manager = NightPhaseManager(self.game_state)
        self.victory_checker = VictoryConditionChecker(self.game_state)
        self.history_manager = GameHistoryManager(self.game_state)

        # 开始记录游戏历史
        self.history_manager.start_recording()

        self._emit_event("game_created", self.game_state)
        return self.game_state
    
    def _distribute_roles(self) -> List[Role]:
        """分配角色"""
        roles = []
        for role, count in self.config.role_distribution.items():
            roles.extend([role] * count)
        
        # 随机打乱角色分配
        random.shuffle(roles)
        return roles
    
    def start_game(self):
        """开始游戏"""
        if not self.game_state:
            raise RuntimeError("游戏未创建")
        
        self.game_state.current_phase = GamePhase.NIGHT
        self.game_state.start_new_round()
        self._emit_event("game_started", self.game_state)
    
    def advance_phase(self):
        """推进游戏阶段"""
        if not self.game_state:
            raise RuntimeError("游戏未创建")
        
        current_phase = self.game_state.current_phase
        
        if current_phase == GamePhase.SETUP:
            self.start_game()
        elif current_phase == GamePhase.NIGHT:
            self._transition_to_day_discussion()
        elif current_phase == GamePhase.DAY_DISCUSSION:
            self._transition_to_day_voting()
        elif current_phase == GamePhase.DAY_VOTING:
            self._transition_to_night()
        
        # 检查游戏是否结束
        result = self.game_state.check_game_end()
        if result != GameResult.ONGOING:
            self.game_state.current_phase = GamePhase.GAME_OVER
            self._emit_event("game_ended", self.game_state, result)
    
    def _transition_to_day_discussion(self):
        """转换到白天讨论阶段"""
        self.game_state.current_phase = GamePhase.DAY_DISCUSSION
        self.game_state.end_current_round()
        self.game_state.start_new_round()
        self._emit_event("phase_changed", self.game_state, GamePhase.DAY_DISCUSSION)
    
    def _transition_to_day_voting(self):
        """转换到白天投票阶段"""
        self.game_state.current_phase = GamePhase.DAY_VOTING
        self.game_state.clear_votes()
        self._emit_event("phase_changed", self.game_state, GamePhase.DAY_VOTING)
    
    def _transition_to_night(self):
        """转换到夜晚阶段"""
        # 处理白天投票结果
        eliminated_player_id = self.game_state.get_vote_leader()
        if eliminated_player_id:
            self.game_state.eliminate_player(eliminated_player_id)
            self._emit_event("player_eliminated", self.game_state, eliminated_player_id)
        
        self.game_state.current_phase = GamePhase.NIGHT
        self.game_state.clear_votes()
        self.game_state.end_current_round()
        self.game_state.start_new_round()
        
        # 重置夜晚状态
        self.game_state.protected_player_id = None
        
        self._emit_event("phase_changed", self.game_state, GamePhase.NIGHT)
    
    def process_vote(self, voter_id: int, target_id: int, vote_type: VoteType = VoteType.ELIMINATION) -> bool:
        """处理投票"""
        if not self.game_state:
            return False
        
        # 验证投票者是否存活
        voter = self.game_state.players.get(voter_id)
        if not voter or not voter.is_alive():
            return False
        
        # 验证目标是否存在且存活
        target = self.game_state.players.get(target_id)
        if not target or not target.is_alive():
            return False
        
        # 验证投票阶段
        if vote_type == VoteType.ELIMINATION and self.game_state.current_phase != GamePhase.DAY_VOTING:
            return False
        
        if vote_type == VoteType.WEREWOLF_KILL and self.game_state.current_phase != GamePhase.NIGHT:
            return False
        
        # 验证投票权限
        if vote_type == VoteType.WEREWOLF_KILL and not voter.is_werewolf():
            return False
        
        self.game_state.add_vote(voter_id, target_id, vote_type)
        self._emit_event("vote_cast", self.game_state, voter_id, target_id, vote_type)
        return True
    
    def process_werewolf_kill(self) -> Optional[int]:
        """处理狼人杀人"""
        if not self.game_state or self.game_state.current_phase != GamePhase.NIGHT:
            return None
        
        # 获取狼人投票结果
        werewolf_votes = {}
        for voter_id, target_id in self.game_state.current_votes.items():
            voter = self.game_state.players[voter_id]
            if voter.is_werewolf():
                werewolf_votes[target_id] = werewolf_votes.get(target_id, 0) + 1
        
        if not werewolf_votes:
            return None
        
        # 选择得票最多的目标
        target_id = max(werewolf_votes.items(), key=lambda x: x[1])[0]
        
        # 检查是否被保护
        if target_id != self.game_state.protected_player_id:
            self.game_state.kill_player(target_id)
            self._emit_event("player_killed", self.game_state, target_id)
            return target_id
        
        return None
    
    def process_special_action(self, player_id: int, action_type: str, target_id: Optional[int] = None) -> bool:
        """处理特殊角色行动"""
        if not self.game_state:
            return False
        
        player = self.game_state.players.get(player_id)
        if not player or not player.is_alive():
            return False
        
        action = PlayerAction(
            player_id=player_id,
            action_type=action_type,
            target_id=target_id
        )
        
        success = False
        
        if action_type == "seer_check" and player.role == Role.SEER:
            success = self._process_seer_check(player_id, target_id)
        elif action_type == "guard_protect" and player.role == Role.GUARD:
            success = self._process_guard_protect(player_id, target_id)
        elif action_type == "witch_save" and player.role == Role.WITCH:
            success = self._process_witch_save(player_id, target_id)
        elif action_type == "witch_poison" and player.role == Role.WITCH:
            success = self._process_witch_poison(player_id, target_id)
        
        if success and self.game_state.current_round_info:
            self.game_state.current_round_info.add_action(action)
        
        return success
    
    def _process_seer_check(self, seer_id: int, target_id: Optional[int]) -> bool:
        """处理预言家查验"""
        if not target_id or target_id not in self.game_state.players:
            return False
        
        target = self.game_state.players[target_id]
        if not target.is_alive():
            return False
        
        # 预言家获得目标角色信息
        self._emit_event("seer_check_result", self.game_state, seer_id, target_id, target.role)
        return True
    
    def _process_guard_protect(self, guard_id: int, target_id: Optional[int]) -> bool:
        """处理守卫保护"""
        if not target_id or target_id not in self.game_state.players:
            return False
        
        target = self.game_state.players[target_id]
        if not target.is_alive():
            return False
        
        self.game_state.protected_player_id = target_id
        self._emit_event("player_protected", self.game_state, target_id)
        return True
    
    def _process_witch_save(self, witch_id: int, target_id: Optional[int]) -> bool:
        """处理女巫救人"""
        if self.game_state.witch_used_antidote:
            return False
        
        # 这里需要更复杂的逻辑来确定谁被狼人杀死
        # 暂时简化处理
        self.game_state.witch_used_antidote = True
        self._emit_event("witch_save", self.game_state, witch_id, target_id)
        return True
    
    def _process_witch_poison(self, witch_id: int, target_id: Optional[int]) -> bool:
        """处理女巫毒人"""
        if self.game_state.witch_used_poison or not target_id:
            return False
        
        target = self.game_state.players.get(target_id)
        if not target or not target.is_alive():
            return False
        
        self.game_state.kill_player(target_id)
        self.game_state.witch_used_poison = True
        self._emit_event("witch_poison", self.game_state, witch_id, target_id)
        return True
    
    def register_event_handler(self, event_name: str, handler: Callable):
        """注册事件处理器"""
        if event_name not in self.event_handlers:
            self.event_handlers[event_name] = []
        self.event_handlers[event_name].append(handler)
    
    def _emit_event(self, event_name: str, *args):
        """触发事件"""
        if event_name in self.event_handlers:
            for handler in self.event_handlers[event_name]:
                try:
                    handler(*args)
                except Exception as e:
                    print(f"事件处理器错误 {event_name}: {e}")
    
    def get_game_state(self) -> Optional[GameState]:
        """获取当前游戏状态"""
        return self.game_state
    
    def is_game_over(self) -> bool:
        """检查游戏是否结束"""
        return (self.game_state and
                self.game_state.current_phase == GamePhase.GAME_OVER)

    # Phase 2 新增方法

    def get_day_phase_manager(self) -> Optional[DayPhaseManager]:
        """获取白天阶段管理器"""
        return self.day_phase_manager

    def get_night_phase_manager(self) -> Optional[NightPhaseManager]:
        """获取夜晚阶段管理器"""
        return self.night_phase_manager

    def get_victory_checker(self) -> Optional[VictoryConditionChecker]:
        """获取胜负条件检查器"""
        return self.victory_checker

    def get_history_manager(self) -> Optional[GameHistoryManager]:
        """获取历史管理器"""
        return self.history_manager

    def check_victory_conditions_detailed(self) -> Dict[str, any]:
        """详细检查胜负条件"""
        if not self.victory_checker:
            return {"result": GameResult.ONGOING, "reason": "no_checker"}

        victory_result = self.victory_checker.check_victory_conditions()

        if victory_result["result"] != GameResult.ONGOING:
            self.game_state.game_result = victory_result["result"]
            self.game_state.current_phase = GamePhase.GAME_OVER

            # 记录游戏结束
            if self.history_manager:
                self.history_manager.record_game_end(
                    victory_result["result"],
                    victory_result["reason"]
                )

            self._emit_event("game_ended", self.game_state, victory_result)

        return victory_result

    def start_day_discussion(self) -> Dict[str, any]:
        """开始白天讨论阶段"""
        if not self.day_phase_manager:
            return {"success": False, "error": "白天阶段管理器未初始化"}

        old_phase = self.game_state.current_phase
        result = self.day_phase_manager.start_discussion_phase()

        if self.history_manager:
            self.history_manager.record_phase_change(old_phase, GamePhase.DAY_DISCUSSION)

        return result

    def start_day_voting(self) -> Dict[str, any]:
        """开始白天投票阶段"""
        if not self.day_phase_manager:
            return {"success": False, "error": "白天阶段管理器未初始化"}

        old_phase = self.game_state.current_phase
        result = self.day_phase_manager.start_voting_phase()

        if self.history_manager:
            self.history_manager.record_phase_change(old_phase, GamePhase.DAY_VOTING)

        return result

    def start_night_phase(self) -> Dict[str, any]:
        """开始夜晚阶段"""
        if not self.night_phase_manager:
            return {"success": False, "error": "夜晚阶段管理器未初始化"}

        old_phase = self.game_state.current_phase
        result = self.night_phase_manager.start_night_phase()

        if self.history_manager:
            self.history_manager.record_phase_change(old_phase, GamePhase.NIGHT)

        return result

    def add_player_speech(self, player_id: int, speech: str) -> Dict[str, any]:
        """添加玩家发言"""
        if not self.day_phase_manager:
            return {"success": False, "error": "白天阶段管理器未初始化"}

        success = self.day_phase_manager.add_speech(player_id, speech)

        if success and self.history_manager:
            action = PlayerAction(
                player_id=player_id,
                action_type="speech",
                additional_data={"content": speech}
            )
            self.history_manager.record_player_action(action)

        return {"success": success}

    def cast_elimination_vote(self, voter_id: int, target_id: Optional[int], reason: str = "") -> Dict[str, any]:
        """投淘汰票"""
        if not self.day_phase_manager:
            return {"success": False, "error": "白天阶段管理器未初始化"}

        result = self.day_phase_manager.cast_vote(voter_id, target_id, reason)

        if result.get("success") and self.history_manager:
            from ..models.player import PlayerVote
            vote = PlayerVote(
                voter_id=voter_id,
                target_id=target_id or 0,
                vote_type=VoteType.ELIMINATION.name,
                round_number=self.game_state.current_round,
                reason=reason
            )
            self.history_manager.record_vote(vote)

        return result

    def resolve_day_voting(self) -> Dict[str, any]:
        """解决白天投票"""
        if not self.day_phase_manager:
            return {"success": False, "error": "白天阶段管理器未初始化"}

        result = self.day_phase_manager.resolve_voting()

        # 记录淘汰事件
        if result.get("eliminated_player") and self.history_manager:
            eliminated = result["eliminated_player"]
            self.history_manager.record_player_death(
                eliminated["id"],
                result.get("reason", "elimination"),
                None
            )

        return result

    def submit_werewolf_action(self, werewolf_votes: Dict[int, int]) -> Dict[str, any]:
        """提交狼人行动"""
        if not self.night_phase_manager:
            return {"success": False, "error": "夜晚阶段管理器未初始化"}

        return self.night_phase_manager.submit_werewolf_action(werewolf_votes)

    def submit_seer_action(self, seer_id: int, target_id: int) -> Dict[str, any]:
        """提交预言家行动"""
        if not self.night_phase_manager:
            return {"success": False, "error": "夜晚阶段管理器未初始化"}

        return self.night_phase_manager.submit_seer_action(seer_id, target_id)

    def submit_witch_action(self, witch_id: int, save_target: Optional[int] = None,
                           poison_target: Optional[int] = None) -> Dict[str, any]:
        """提交女巫行动"""
        if not self.night_phase_manager:
            return {"success": False, "error": "夜晚阶段管理器未初始化"}

        return self.night_phase_manager.submit_witch_action(witch_id, save_target, poison_target)

    def submit_guard_action(self, guard_id: int, target_id: int) -> Dict[str, any]:
        """提交守卫行动"""
        if not self.night_phase_manager:
            return {"success": False, "error": "夜晚阶段管理器未初始化"}

        return self.night_phase_manager.submit_guard_action(guard_id, target_id)

    def resolve_night_actions(self) -> Dict[str, any]:
        """解决夜晚行动"""
        if not self.night_phase_manager:
            return {"success": False, "error": "夜晚阶段管理器未初始化"}

        result = self.night_phase_manager.resolve_night_actions()

        # 记录死亡事件
        if result.get("success") and self.history_manager:
            for death in result.get("deaths", []):
                self.history_manager.record_player_death(
                    death["player_id"],
                    death["cause"],
                    None  # 杀手ID在夜晚行动中通常不公开
                )

        return result

    def get_game_summary(self) -> Dict[str, any]:
        """获取游戏总结"""
        if not self.history_manager:
            return {"error": "历史管理器未初始化"}

        return self.history_manager.generate_game_summary()

    def export_game_history(self, filename: Optional[str] = None) -> str:
        """导出游戏历史"""
        if not self.history_manager:
            return ""

        return self.history_manager.export_to_json(filename)

    def get_victory_probability(self) -> Dict[str, float]:
        """获取胜利概率"""
        if not self.victory_checker:
            return {"villagers": 0.5, "werewolves": 0.5}

        return self.victory_checker.get_victory_probability()

    def get_game_balance_info(self) -> Dict[str, any]:
        """获取游戏平衡信息"""
        if not self.victory_checker:
            return {}

        return self.victory_checker.get_game_balance_info()
