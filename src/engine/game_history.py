"""
游戏历史记录和事件日志系统
支持详细的游戏记录、回放和分析功能
"""
import json
from typing import List, Dict, Optional, Any
from datetime import datetime
from dataclasses import dataclass, asdict

from ..models.enums import GamePhase, Role, GameResult
from ..models.game_state import GameState, RoundInfo
from ..models.player import PlayerAction, PlayerVote


@dataclass
class GameEvent:
    """游戏事件"""
    event_id: str
    event_type: str
    timestamp: str
    round_number: int
    phase: str
    player_id: Optional[int] = None
    target_id: Optional[int] = None
    data: Dict[str, Any] = None
    description: str = ""


class GameHistoryManager:
    """游戏历史管理器"""
    
    def __init__(self, game_state: GameState):
        self.game_state = game_state
        self.events: List[GameEvent] = []
        self.event_counter = 0
        self.game_start_time = None
        self.game_end_time = None
    
    def start_recording(self):
        """开始记录游戏"""
        self.game_start_time = datetime.now()
        self.record_event(
            event_type="game_start",
            description=f"游戏开始，ID: {self.game_state.game_id}",
            data={
                "game_id": self.game_state.game_id,
                "total_players": len(self.game_state.players),
                "role_distribution": {role.name: count for role, count in self.game_state.config.role_distribution.items()}
            }
        )
        
        # 记录角色分配
        for player_id, player in self.game_state.players.items():
            self.record_event(
                event_type="role_assigned",
                player_id=player_id,
                description=f"{player.name} 被分配角色 {player.role.name}",
                data={
                    "player_name": player.name,
                    "role": player.role.name,
                    "faction": player.faction.name
                }
            )
    
    def record_event(self, event_type: str, player_id: Optional[int] = None, 
                    target_id: Optional[int] = None, description: str = "", 
                    data: Optional[Dict[str, Any]] = None):
        """记录游戏事件"""
        self.event_counter += 1
        
        event = GameEvent(
            event_id=f"event_{self.event_counter:04d}",
            event_type=event_type,
            timestamp=datetime.now().isoformat(),
            round_number=self.game_state.current_round,
            phase=self.game_state.current_phase.name,
            player_id=player_id,
            target_id=target_id,
            data=data or {},
            description=description
        )
        
        self.events.append(event)
    
    def record_phase_change(self, old_phase: GamePhase, new_phase: GamePhase):
        """记录阶段变更"""
        self.record_event(
            event_type="phase_change",
            description=f"阶段变更：{old_phase.name} -> {new_phase.name}",
            data={
                "old_phase": old_phase.name,
                "new_phase": new_phase.name
            }
        )
    
    def record_player_action(self, action: PlayerAction):
        """记录玩家行动"""
        player = self.game_state.players.get(action.player_id)
        target = self.game_state.players.get(action.target_id) if action.target_id else None
        
        description = self._generate_action_description(action, player, target)
        
        self.record_event(
            event_type="player_action",
            player_id=action.player_id,
            target_id=action.target_id,
            description=description,
            data={
                "action_type": action.action_type,
                "player_name": player.name if player else "Unknown",
                "target_name": target.name if target else None,
                "additional_data": action.additional_data
            }
        )
    
    def record_vote(self, vote: PlayerVote):
        """记录投票"""
        voter = self.game_state.players.get(vote.voter_id)
        target = self.game_state.players.get(vote.target_id) if vote.target_id != 0 else None
        
        if target:
            description = f"{voter.name} 投票给 {target.name}"
        else:
            description = f"{voter.name} 弃权"
        
        self.record_event(
            event_type="vote_cast",
            player_id=vote.voter_id,
            target_id=vote.target_id if vote.target_id != 0 else None,
            description=description,
            data={
                "vote_type": vote.vote_type,
                "voter_name": voter.name if voter else "Unknown",
                "target_name": target.name if target else "弃权",
                "reason": vote.reason,
                "confidence": vote.confidence
            }
        )
    
    def record_player_death(self, player_id: int, cause: str, killer_id: Optional[int] = None):
        """记录玩家死亡"""
        player = self.game_state.players.get(player_id)
        killer = self.game_state.players.get(killer_id) if killer_id else None
        
        if killer:
            description = f"{player.name} ({player.role.name}) 被 {killer.name} 杀死 ({cause})"
        else:
            description = f"{player.name} ({player.role.name}) 死亡 ({cause})"
        
        self.record_event(
            event_type="player_death",
            player_id=player_id,
            target_id=killer_id,
            description=description,
            data={
                "player_name": player.name if player else "Unknown",
                "role": player.role.name if player else "Unknown",
                "cause": cause,
                "killer_name": killer.name if killer else None,
                "round": self.game_state.current_round
            }
        )
    
    def record_game_end(self, result: GameResult, reason: str):
        """记录游戏结束"""
        self.game_end_time = datetime.now()
        
        winners = []
        if result == GameResult.VILLAGERS_WIN:
            winners = [p for p in self.game_state.players.values() if p.faction.name == "VILLAGERS"]
        elif result == GameResult.WEREWOLVES_WIN:
            winners = [p for p in self.game_state.players.values() if p.faction.name == "WEREWOLVES"]
        
        self.record_event(
            event_type="game_end",
            description=f"游戏结束：{result.name} ({reason})",
            data={
                "result": result.name,
                "reason": reason,
                "total_rounds": self.game_state.current_round,
                "game_duration": (self.game_end_time - self.game_start_time).total_seconds() if self.game_start_time else 0,
                "winners": [{"id": p.player_id, "name": p.name, "role": p.role.name} for p in winners],
                "survivors": [{"id": p.player_id, "name": p.name, "role": p.role.name} for p in self.game_state.get_alive_players()]
            }
        )
    
    def _generate_action_description(self, action: PlayerAction, player, target) -> str:
        """生成行动描述"""
        action_descriptions = {
            "werewolf_kill_vote": f"{player.name} 投票杀死 {target.name if target else 'Unknown'}",
            "seer_check": f"预言家 {player.name} 查验 {target.name if target else 'Unknown'}",
            "witch_save": f"女巫 {player.name} 救了 {target.name if target else 'Unknown'}",
            "witch_poison": f"女巫 {player.name} 毒了 {target.name if target else 'Unknown'}",
            "guard_protect": f"守卫 {player.name} 保护 {target.name if target else 'Unknown'}",
            "hunter_shoot": f"猎人 {player.name} 射击 {target.name if target else 'Unknown'}",
            "speech": f"{player.name} 发言"
        }
        
        return action_descriptions.get(action.action_type, f"{player.name} 执行 {action.action_type}")
    
    def get_events_by_type(self, event_type: str) -> List[GameEvent]:
        """按类型获取事件"""
        return [event for event in self.events if event.event_type == event_type]
    
    def get_events_by_player(self, player_id: int) -> List[GameEvent]:
        """按玩家获取事件"""
        return [event for event in self.events if event.player_id == player_id]
    
    def get_events_by_round(self, round_number: int) -> List[GameEvent]:
        """按回合获取事件"""
        return [event for event in self.events if event.round_number == round_number]
    
    def get_events_by_phase(self, phase: GamePhase) -> List[GameEvent]:
        """按阶段获取事件"""
        return [event for event in self.events if event.phase == phase.name]
    
    def generate_game_summary(self) -> Dict[str, Any]:
        """生成游戏总结"""
        if not self.events:
            return {"error": "没有游戏记录"}
        
        # 基本信息
        game_start_event = next((e for e in self.events if e.event_type == "game_start"), None)
        game_end_event = next((e for e in self.events if e.event_type == "game_end"), None)
        
        # 统计信息
        death_events = self.get_events_by_type("player_death")
        vote_events = self.get_events_by_type("vote_cast")
        action_events = self.get_events_by_type("player_action")
        
        # 玩家统计
        player_stats = {}
        for player_id, player in self.game_state.players.items():
            player_events = self.get_events_by_player(player_id)
            player_votes = [e for e in vote_events if e.player_id == player_id]
            player_actions = [e for e in action_events if e.player_id == player_id]
            
            player_stats[player_id] = {
                "name": player.name,
                "role": player.role.name,
                "faction": player.faction.name,
                "survived": player.is_alive(),
                "total_events": len(player_events),
                "votes_cast": len(player_votes),
                "actions_taken": len(player_actions),
                "death_round": next((e.round_number for e in death_events if e.player_id == player_id), None)
            }
        
        return {
            "game_info": {
                "game_id": self.game_state.game_id,
                "start_time": game_start_event.timestamp if game_start_event else None,
                "end_time": game_end_event.timestamp if game_end_event else None,
                "total_rounds": self.game_state.current_round,
                "total_events": len(self.events),
                "result": game_end_event.data.get("result") if game_end_event else "ONGOING"
            },
            "player_stats": player_stats,
            "event_summary": {
                "deaths": len(death_events),
                "votes": len(vote_events),
                "actions": len(action_events),
                "phase_changes": len(self.get_events_by_type("phase_change"))
            },
            "round_summary": self._generate_round_summary()
        }
    
    def _generate_round_summary(self) -> List[Dict[str, Any]]:
        """生成回合总结"""
        round_summaries = []
        
        for round_num in range(1, self.game_state.current_round + 1):
            round_events = self.get_events_by_round(round_num)
            
            night_events = [e for e in round_events if e.phase == "NIGHT"]
            day_events = [e for e in round_events if e.phase in ["DAY_DISCUSSION", "DAY_VOTING"]]
            
            deaths = [e for e in round_events if e.event_type == "player_death"]
            votes = [e for e in round_events if e.event_type == "vote_cast"]
            
            round_summaries.append({
                "round": round_num,
                "total_events": len(round_events),
                "night_events": len(night_events),
                "day_events": len(day_events),
                "deaths": [{"player_name": e.data.get("player_name"), "cause": e.data.get("cause")} for e in deaths],
                "votes": len(votes)
            })
        
        return round_summaries
    
    def export_to_json(self, filename: Optional[str] = None) -> str:
        """导出为JSON格式"""
        if not filename:
            filename = f"game_{self.game_state.game_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        export_data = {
            "game_summary": self.generate_game_summary(),
            "events": [asdict(event) for event in self.events],
            "final_game_state": {
                "players": {
                    str(pid): {
                        "name": p.name,
                        "role": p.role.name,
                        "faction": p.faction.name,
                        "status": p.status.name,
                        "is_alive": p.is_alive()
                    } for pid, p in self.game_state.players.items()
                },
                "current_round": self.game_state.current_round,
                "current_phase": self.game_state.current_phase.name,
                "game_result": self.game_state.game_result.name
            }
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)
        
        return filename
    
    def create_replay_data(self) -> Dict[str, Any]:
        """创建回放数据"""
        return {
            "game_id": self.game_state.game_id,
            "initial_state": {
                "players": {
                    str(pid): {
                        "name": p.name,
                        "role": p.role.name,
                        "faction": p.faction.name
                    } for pid, p in self.game_state.players.items()
                },
                "config": {
                    "total_players": self.game_state.config.total_players,
                    "role_distribution": {role.name: count for role, count in self.game_state.config.role_distribution.items()}
                }
            },
            "events": [asdict(event) for event in self.events],
            "timeline": self._create_timeline()
        }
    
    def _create_timeline(self) -> List[Dict[str, Any]]:
        """创建时间线"""
        timeline = []
        current_round = 0
        current_phase = None
        
        for event in self.events:
            if event.round_number != current_round or event.phase != current_phase:
                current_round = event.round_number
                current_phase = event.phase
                
                timeline.append({
                    "type": "phase_marker",
                    "round": current_round,
                    "phase": current_phase,
                    "timestamp": event.timestamp
                })
            
            timeline.append({
                "type": "event",
                "event": asdict(event)
            })
        
        return timeline
