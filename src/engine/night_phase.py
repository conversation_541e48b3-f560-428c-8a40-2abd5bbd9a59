"""
夜晚阶段逻辑处理
实现狼人杀人、特殊角色技能等完整夜晚流程
"""
from typing import List, Dict, Optional, Set, Tuple
from datetime import datetime

from ..models.enums import Role, PlayerStatus, ActionType, GamePhase
from ..models.game_state import GameState
from ..models.player import PlayerAction


class NightPhaseManager:
    """夜晚阶段管理器"""
    
    def __init__(self, game_state: GameState):
        self.game_state = game_state
        self.night_actions = {}  # 存储夜晚行动
        self.action_order = [
            Role.WEREWOLF,    # 狼人杀人
            Role.SEER,        # 预言家查验
            Role.WITCH,       # 女巫行动
            Role.GUARD        # 守卫保护
        ]
        self.completed_roles = set()
        self.werewolf_target = None
        self.protected_player = None
        self.witch_saved_player = None
        self.witch_poisoned_player = None
    
    def start_night_phase(self) -> Dict[str, any]:
        """开始夜晚阶段"""
        self.game_state.current_phase = GamePhase.NIGHT
        
        # 重置夜晚状态
        self.night_actions.clear()
        self.completed_roles.clear()
        self.werewolf_target = None
        self.protected_player = None
        self.witch_saved_player = None
        self.witch_poisoned_player = None
        self.game_state.protected_player_id = None
        
        # 获取存活的特殊角色
        active_roles = self._get_active_night_roles()
        
        return {
            "phase": GamePhase.NIGHT,
            "active_roles": active_roles,
            "action_order": [role.name for role in self.action_order if role in active_roles]
        }
    
    def _get_active_night_roles(self) -> Set[Role]:
        """获取存活的夜晚行动角色"""
        active_roles = set()
        
        for player in self.game_state.players.values():
            if player.is_alive() and player.role in self.action_order:
                active_roles.add(player.role)
        
        return active_roles
    
    def submit_werewolf_action(self, werewolf_votes: Dict[int, int]) -> Dict[str, any]:
        """提交狼人行动"""
        werewolves = self.game_state.get_werewolves()
        if not werewolves:
            return {"success": False, "error": "没有存活的狼人"}
        
        # 统计狼人投票
        vote_counts = {}
        valid_votes = 0
        
        for werewolf in werewolves:
            if werewolf.player_id in werewolf_votes:
                target_id = werewolf_votes[werewolf.player_id]
                target = self.game_state.players.get(target_id)
                
                if target and target.is_alive() and target_id != werewolf.player_id:
                    vote_counts[target_id] = vote_counts.get(target_id, 0) + 1
                    valid_votes += 1
        
        if not vote_counts:
            return {"success": False, "error": "没有有效的狼人投票"}
        
        # 选择目标（得票最多的）
        max_votes = max(vote_counts.values())
        candidates = [pid for pid, votes in vote_counts.items() if votes == max_votes]
        
        # 如果平票，随机选择
        import random
        self.werewolf_target = random.choice(candidates)
        
        # 记录行动
        self.night_actions[Role.WEREWOLF] = {
            "action_type": "werewolf_kill",
            "target_id": self.werewolf_target,
            "votes": vote_counts,
            "participants": [w.player_id for w in werewolves if w.player_id in werewolf_votes]
        }
        
        self.completed_roles.add(Role.WEREWOLF)
        
        return {
            "success": True,
            "target_id": self.werewolf_target,
            "target_name": self.game_state.players[self.werewolf_target].name,
            "vote_counts": vote_counts
        }
    
    def submit_seer_action(self, seer_id: int, target_id: int) -> Dict[str, any]:
        """提交预言家行动"""
        seer = self.game_state.players.get(seer_id)
        if not seer or seer.role != Role.SEER or not seer.is_alive():
            return {"success": False, "error": "预言家无效"}
        
        target = self.game_state.players.get(target_id)
        if not target or not target.is_alive():
            return {"success": False, "error": "查验目标无效"}
        
        if target_id == seer_id:
            return {"success": False, "error": "不能查验自己"}
        
        # 执行查验
        is_werewolf = target.role == Role.WEREWOLF
        
        # 记录行动
        self.night_actions[Role.SEER] = {
            "action_type": "seer_check",
            "seer_id": seer_id,
            "target_id": target_id,
            "result": is_werewolf
        }
        
        self.completed_roles.add(Role.SEER)
        
        return {
            "success": True,
            "target_name": target.name,
            "is_werewolf": is_werewolf,
            "message": f"查验结果：{target.name} {'是' if is_werewolf else '不是'}狼人"
        }
    
    def submit_witch_action(self, witch_id: int, save_target: Optional[int] = None, 
                           poison_target: Optional[int] = None) -> Dict[str, any]:
        """提交女巫行动"""
        witch = self.game_state.players.get(witch_id)
        if not witch or witch.role != Role.WITCH or not witch.is_alive():
            return {"success": False, "error": "女巫无效"}
        
        actions_taken = []
        
        # 处理救人
        if save_target is not None:
            if self.game_state.witch_used_antidote:
                return {"success": False, "error": "解药已使用"}
            
            # 只能救被狼人杀死的人
            if save_target != self.werewolf_target:
                return {"success": False, "error": "只能救被狼人杀死的人"}
            
            self.witch_saved_player = save_target
            self.game_state.witch_used_antidote = True
            actions_taken.append(f"救了 {self.game_state.players[save_target].name}")
        
        # 处理毒人
        if poison_target is not None:
            if self.game_state.witch_used_poison:
                return {"success": False, "error": "毒药已使用"}
            
            target = self.game_state.players.get(poison_target)
            if not target or not target.is_alive():
                return {"success": False, "error": "毒杀目标无效"}
            
            if poison_target == witch_id:
                return {"success": False, "error": "不能毒杀自己"}
            
            self.witch_poisoned_player = poison_target
            self.game_state.witch_used_poison = True
            actions_taken.append(f"毒了 {target.name}")
        
        # 记录行动
        self.night_actions[Role.WITCH] = {
            "action_type": "witch_action",
            "witch_id": witch_id,
            "save_target": save_target,
            "poison_target": poison_target,
            "antidote_used": self.game_state.witch_used_antidote,
            "poison_used": self.game_state.witch_used_poison
        }
        
        self.completed_roles.add(Role.WITCH)
        
        return {
            "success": True,
            "actions": actions_taken,
            "antidote_remaining": not self.game_state.witch_used_antidote,
            "poison_remaining": not self.game_state.witch_used_poison
        }
    
    def submit_guard_action(self, guard_id: int, target_id: int) -> Dict[str, any]:
        """提交守卫行动"""
        guard = self.game_state.players.get(guard_id)
        if not guard or guard.role != Role.GUARD or not guard.is_alive():
            return {"success": False, "error": "守卫无效"}
        
        target = self.game_state.players.get(target_id)
        if not target or not target.is_alive():
            return {"success": False, "error": "保护目标无效"}
        
        # 守卫不能连续两晚保护同一人（可选规则）
        last_protected = getattr(self.game_state, 'last_protected_player_id', None)
        if last_protected == target_id:
            return {"success": False, "error": "不能连续保护同一人"}
        
        self.protected_player = target_id
        self.game_state.protected_player_id = target_id
        self.game_state.last_protected_player_id = target_id
        
        # 记录行动
        self.night_actions[Role.GUARD] = {
            "action_type": "guard_protect",
            "guard_id": guard_id,
            "target_id": target_id
        }
        
        self.completed_roles.add(Role.GUARD)
        
        return {
            "success": True,
            "target_name": target.name,
            "message": f"保护了 {target.name}"
        }
    
    def get_night_status(self) -> Dict[str, any]:
        """获取夜晚状态"""
        active_roles = self._get_active_night_roles()
        pending_roles = active_roles - self.completed_roles
        
        return {
            "completed_roles": [role.name for role in self.completed_roles],
            "pending_roles": [role.name for role in pending_roles],
            "progress": len(self.completed_roles) / len(active_roles) if active_roles else 1.0,
            "can_resolve": len(pending_roles) == 0
        }
    
    def resolve_night_actions(self) -> Dict[str, any]:
        """解决夜晚行动"""
        if not self.is_night_complete():
            return {"success": False, "error": "夜晚行动未完成"}
        
        deaths = []
        saved_players = []
        protected_players = []
        special_events = []
        
        # 处理狼人杀人
        if self.werewolf_target:
            target = self.game_state.players[self.werewolf_target]
            
            # 检查是否被救或被保护
            if self.witch_saved_player == self.werewolf_target:
                saved_players.append({
                    "player_id": self.werewolf_target,
                    "player_name": target.name,
                    "saved_by": "witch"
                })
            elif self.protected_player == self.werewolf_target:
                protected_players.append({
                    "player_id": self.werewolf_target,
                    "player_name": target.name,
                    "protected_by": "guard"
                })
            else:
                # 真正死亡
                self.game_state.kill_player(self.werewolf_target)
                deaths.append({
                    "player_id": self.werewolf_target,
                    "player_name": target.name,
                    "role": target.role,
                    "cause": "werewolf_kill"
                })
        
        # 处理女巫毒杀
        if self.witch_poisoned_player:
            target = self.game_state.players[self.witch_poisoned_player]
            self.game_state.kill_player(self.witch_poisoned_player)
            deaths.append({
                "player_id": self.witch_poisoned_player,
                "player_name": target.name,
                "role": target.role,
                "cause": "witch_poison"
            })
        
        # 记录所有行动到游戏历史
        self._record_night_actions()
        
        return {
            "success": True,
            "deaths": deaths,
            "saved_players": saved_players,
            "protected_players": protected_players,
            "special_events": special_events,
            "night_actions": self.night_actions
        }
    
    def _record_night_actions(self):
        """记录夜晚行动到游戏历史"""
        if not self.game_state.current_round_info:
            return
        
        for role, action_data in self.night_actions.items():
            if role == Role.WEREWOLF:
                for participant_id in action_data["participants"]:
                    action = PlayerAction(
                        player_id=participant_id,
                        action_type="werewolf_kill_vote",
                        target_id=action_data["target_id"]
                    )
                    self.game_state.current_round_info.add_action(action)
            
            elif role == Role.SEER:
                action = PlayerAction(
                    player_id=action_data["seer_id"],
                    action_type="seer_check",
                    target_id=action_data["target_id"],
                    additional_data={"result": action_data["result"]}
                )
                self.game_state.current_round_info.add_action(action)
            
            elif role == Role.WITCH:
                witch_id = action_data["witch_id"]
                if action_data["save_target"]:
                    action = PlayerAction(
                        player_id=witch_id,
                        action_type="witch_save",
                        target_id=action_data["save_target"]
                    )
                    self.game_state.current_round_info.add_action(action)
                
                if action_data["poison_target"]:
                    action = PlayerAction(
                        player_id=witch_id,
                        action_type="witch_poison",
                        target_id=action_data["poison_target"]
                    )
                    self.game_state.current_round_info.add_action(action)
            
            elif role == Role.GUARD:
                action = PlayerAction(
                    player_id=action_data["guard_id"],
                    action_type="guard_protect",
                    target_id=action_data["target_id"]
                )
                self.game_state.current_round_info.add_action(action)
    
    def is_night_complete(self) -> bool:
        """检查夜晚是否完成"""
        active_roles = self._get_active_night_roles()
        return len(self.completed_roles) >= len(active_roles)
    
    def get_role_action_info(self, role: Role) -> Dict[str, any]:
        """获取特定角色的行动信息"""
        if role == Role.WEREWOLF:
            werewolves = self.game_state.get_werewolves()
            return {
                "role": "werewolf",
                "players": [{"id": w.player_id, "name": w.name} for w in werewolves],
                "targets": [{"id": p.player_id, "name": p.name} 
                           for p in self.game_state.get_alive_players() 
                           if not p.is_werewolf()],
                "completed": Role.WEREWOLF in self.completed_roles
            }
        
        elif role == Role.SEER:
            seers = self.game_state.get_players_by_role(Role.SEER)
            if not seers:
                return {"role": "seer", "available": False}
            
            seer = seers[0]
            return {
                "role": "seer",
                "player": {"id": seer.player_id, "name": seer.name},
                "targets": [{"id": p.player_id, "name": p.name} 
                           for p in self.game_state.get_alive_players() 
                           if p.player_id != seer.player_id],
                "completed": Role.SEER in self.completed_roles
            }
        
        elif role == Role.WITCH:
            witches = self.game_state.get_players_by_role(Role.WITCH)
            if not witches:
                return {"role": "witch", "available": False}
            
            witch = witches[0]
            return {
                "role": "witch",
                "player": {"id": witch.player_id, "name": witch.name},
                "antidote_available": not self.game_state.witch_used_antidote,
                "poison_available": not self.game_state.witch_used_poison,
                "werewolf_target": self.werewolf_target,
                "werewolf_target_name": self.game_state.players[self.werewolf_target].name if self.werewolf_target else None,
                "poison_targets": [{"id": p.player_id, "name": p.name} 
                                 for p in self.game_state.get_alive_players() 
                                 if p.player_id != witch.player_id],
                "completed": Role.WITCH in self.completed_roles
            }
        
        elif role == Role.GUARD:
            guards = self.game_state.get_players_by_role(Role.GUARD)
            if not guards:
                return {"role": "guard", "available": False}
            
            guard = guards[0]
            last_protected = getattr(self.game_state, 'last_protected_player_id', None)
            
            return {
                "role": "guard",
                "player": {"id": guard.player_id, "name": guard.name},
                "targets": [{"id": p.player_id, "name": p.name, "can_protect": p.player_id != last_protected} 
                           for p in self.game_state.get_alive_players()],
                "last_protected": last_protected,
                "completed": Role.GUARD in self.completed_roles
            }
        
        return {"role": role.name.lower(), "available": False}
