"""
游戏状态数据结构
定义游戏的完整状态信息
"""
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Set
from .enums import GamePhase, GameResult, Role, VoteType, PlayerStatus
from .player import PlayerInfo, PlayerAction, PlayerVote


@dataclass
class GameConfig:
    """游戏配置"""
    total_players: int
    role_distribution: Dict[Role, int]
    enable_special_roles: bool = True
    discussion_time_limit: int = 300  # 讨论时间限制（秒）
    voting_time_limit: int = 60      # 投票时间限制（秒）
    allow_tie_votes: bool = False    # 是否允许平票

    def validate(self) -> bool:
        """验证配置是否有效"""
        total_roles = sum(self.role_distribution.values())
        return total_roles == self.total_players


@dataclass
class RoundInfo:
    """回合信息"""
    round_number: int
    phase: GamePhase
    start_time: Optional[str] = None
    end_time: Optional[str] = None
    actions: List[PlayerAction] = field(default_factory=list)
    votes: List[PlayerVote] = field(default_factory=list)
    eliminated_player_id: Optional[int] = None
    night_deaths: List[int] = field(default_factory=list)
    
    def add_action(self, action: PlayerAction):
        """添加玩家行动"""
        action.round_number = self.round_number
        action.phase = self.phase.name
        self.actions.append(action)
    
    def add_vote(self, vote: PlayerVote):
        """添加投票记录"""
        vote.round_number = self.round_number
        self.votes.append(vote)


@dataclass
class GameState:
    """游戏状态"""
    game_id: str
    config: GameConfig
    players: Dict[int, PlayerInfo]
    current_phase: GamePhase = GamePhase.SETUP
    current_round: int = 0
    game_result: GameResult = GameResult.ONGOING
    rounds_history: List[RoundInfo] = field(default_factory=list)
    current_round_info: Optional[RoundInfo] = None
    
    # 投票相关
    current_votes: Dict[int, int] = field(default_factory=dict)  # voter_id -> target_id
    vote_counts: Dict[int, int] = field(default_factory=dict)    # target_id -> vote_count
    
    # 特殊状态
    protected_player_id: Optional[int] = None  # 被守卫保护的玩家
    witch_used_antidote: bool = False         # 女巫是否使用过解药
    witch_used_poison: bool = False           # 女巫是否使用过毒药
    
    def __post_init__(self):
        """初始化后处理"""
        if self.current_round_info is None and self.current_phase != GamePhase.SETUP:
            self.start_new_round()
    
    def start_new_round(self):
        """开始新回合"""
        self.current_round += 1
        self.current_round_info = RoundInfo(
            round_number=self.current_round,
            phase=self.current_phase
        )
    
    def end_current_round(self):
        """结束当前回合"""
        if self.current_round_info:
            self.rounds_history.append(self.current_round_info)
            self.current_round_info = None
    
    def get_alive_players(self) -> List[PlayerInfo]:
        """获取存活玩家列表"""
        return [player for player in self.players.values() if player.is_alive()]
    
    def get_alive_player_ids(self) -> List[int]:
        """获取存活玩家ID列表"""
        return [player.player_id for player in self.players.values() if player.is_alive()]
    
    def get_players_by_role(self, role: Role) -> List[PlayerInfo]:
        """根据角色获取玩家列表"""
        return [player for player in self.players.values() 
                if player.role == role and player.is_alive()]
    
    def get_werewolves(self) -> List[PlayerInfo]:
        """获取存活的狼人列表"""
        return self.get_players_by_role(Role.WEREWOLF)
    
    def get_villagers(self) -> List[PlayerInfo]:
        """获取存活的村民阵营玩家列表"""
        return [player for player in self.players.values() 
                if player.is_villager_faction() and player.is_alive()]
    
    def add_vote(self, voter_id: int, target_id: int, vote_type: VoteType = VoteType.ELIMINATION):
        """添加投票"""
        # 移除之前的投票
        if voter_id in self.current_votes:
            old_target = self.current_votes[voter_id]
            self.vote_counts[old_target] = max(0, self.vote_counts.get(old_target, 0) - 1)
        
        # 添加新投票
        self.current_votes[voter_id] = target_id
        self.vote_counts[target_id] = self.vote_counts.get(target_id, 0) + 1
        
        # 记录投票历史
        if self.current_round_info:
            vote = PlayerVote(
                voter_id=voter_id,
                target_id=target_id,
                vote_type=vote_type.name,
                round_number=self.current_round
            )
            self.current_round_info.add_vote(vote)
    
    def clear_votes(self):
        """清空当前投票"""
        self.current_votes.clear()
        self.vote_counts.clear()
    
    def get_vote_leader(self) -> Optional[int]:
        """获取得票最多的玩家ID"""
        if not self.vote_counts:
            return None
        
        max_votes = max(self.vote_counts.values())
        candidates = [player_id for player_id, votes in self.vote_counts.items() 
                     if votes == max_votes]
        
        # 如果有平票且不允许平票，返回None
        if len(candidates) > 1 and not self.config.allow_tie_votes:
            return None
        
        return candidates[0] if candidates else None
    
    def eliminate_player(self, player_id: int):
        """淘汰玩家"""
        if player_id in self.players:
            self.players[player_id].status = PlayerStatus.DEAD
            if self.current_round_info:
                self.current_round_info.eliminated_player_id = player_id
    
    def kill_player(self, player_id: int):
        """杀死玩家（夜晚死亡）"""
        if player_id in self.players:
            self.players[player_id].status = PlayerStatus.DEAD
            if self.current_round_info:
                self.current_round_info.night_deaths.append(player_id)
    
    def check_game_end(self) -> GameResult:
        """检查游戏是否结束"""
        alive_werewolves = len(self.get_werewolves())
        alive_villagers = len(self.get_villagers())
        
        if alive_werewolves == 0:
            self.game_result = GameResult.VILLAGERS_WIN
        elif alive_werewolves >= alive_villagers:
            self.game_result = GameResult.WEREWOLVES_WIN
        else:
            self.game_result = GameResult.ONGOING
        
        return self.game_result
