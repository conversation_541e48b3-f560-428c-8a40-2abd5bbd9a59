"""
行动对话框组件
提供特殊角色技能使用界面
"""
import tkinter as tk
from tkinter import ttk, messagebox
from typing import Optional, List, Dict, Any
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../..'))

from ...styles.colors import Colors, RoleColors
from ...styles.fonts import font_manager
from ....models.player import PlayerInfo
from ....models.enums import Role, ActionType

class ActionDialog:
    """行动对话框"""

    def __init__(self, parent, player_role: Role, players: List[PlayerInfo], available_actions: List[str]):
        self.parent = parent
        self.player_role = player_role
        self.players = players
        self.available_actions = available_actions
        self.result = None

        self.dialog = None
        self.action_var = None
        self.target_var = None
        self.action_frame = None
        self.target_frame = None

        self._create_dialog()

    def _create_dialog(self):
        """创建对话框"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title(f"{self._get_role_name()} - 选择行动")
        self.dialog.geometry("450x600")
        self.dialog.resizable(False, False)
        self.dialog.transient(self.parent)
        self.dialog.grab_set()

        # 居中显示
        self._center_dialog()

        # 创建内容
        self._create_content()

        # 绑定事件
        self.dialog.protocol("WM_DELETE_WINDOW", self._on_cancel)

    def _center_dialog(self):
        """居中显示对话框"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (self.dialog.winfo_width() // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")

    def _create_content(self):
        """创建对话框内容"""
        # 角色标题
        role_color = RoleColors.get_role_color(self.player_role.name)
        title_label = tk.Label(
            self.dialog,
            text=f"{self._get_role_name()} 夜晚行动",
            font=font_manager.get_title_font(),
            fg=role_color
        )
        title_label.pack(pady=(10, 5))

        # 角色描述
        desc_label = tk.Label(
            self.dialog,
            text=self._get_role_description(),
            font=font_manager.get_body_font(),
            wraplength=400,
            justify=tk.CENTER
        )
        desc_label.pack(pady=(0, 20))

        # 行动选择区域
        self._create_action_selection()

        # 目标选择区域
        self._create_target_selection()

        # 按钮区域
        self._create_buttons()

    def _create_action_selection(self):
        """创建行动选择区域"""
        self.action_frame = ttk.LabelFrame(self.dialog, text="选择行动")
        self.action_frame.pack(fill=tk.X, padx=20, pady=(0, 10))

        self.action_var = tk.StringVar()

        # 根据角色创建不同的行动选项
        actions = self._get_role_actions()

        for action_key, action_info in actions.items():
            if action_key in self.available_actions:
                radio = tk.Radiobutton(
                    self.action_frame,
                    text=action_info['name'],
                    variable=self.action_var,
                    value=action_key,
                    font=font_manager.get_body_font(),
                    command=self._on_action_change
                )
                radio.pack(anchor=tk.W, padx=10, pady=2)

                # 添加描述
                desc_label = tk.Label(
                    self.action_frame,
                    text=f"  {action_info['description']}",
                    font=font_manager.get_small_font(),
                    fg=Colors.TEXT_LIGHT
                )
                desc_label.pack(anchor=tk.W, padx=20, pady=(0, 5))

        # 跳过选项
        skip_radio = tk.Radiobutton(
            self.action_frame,
            text="跳过行动",
            variable=self.action_var,
            value="skip",
            font=font_manager.get_body_font(),
            command=self._on_action_change
        )
        skip_radio.pack(anchor=tk.W, padx=10, pady=2)

        # 默认选择跳过
        self.action_var.set("skip")

    def _create_target_selection(self):
        """创建目标选择区域"""
        self.target_frame = ttk.LabelFrame(self.dialog, text="选择目标")
        self.target_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=(0, 10))

        # 目标列表
        list_container = tk.Frame(self.target_frame)
        list_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.target_listbox = tk.Listbox(
            list_container,
            font=font_manager.get_body_font(),
            selectmode=tk.SINGLE
        )

        scrollbar = ttk.Scrollbar(list_container, orient=tk.VERTICAL)
        self.target_listbox.config(yscrollcommand=scrollbar.set)
        scrollbar.config(command=self.target_listbox.yview)

        # 布局
        self.target_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 绑定事件
        self.target_listbox.bind('<<ListboxSelect>>', self._on_target_select)

        # 初始状态禁用
        self.target_frame.configure(state='disabled')
        self._set_target_frame_state(False)

    def _create_buttons(self):
        """创建按钮区域"""
        button_frame = tk.Frame(self.dialog)
        button_frame.pack(fill=tk.X, padx=20, pady=(0, 20))

        # 确认按钮
        self.confirm_button = tk.Button(
            button_frame,
            text="确认行动",
            font=font_manager.get_button_font(),
            command=self._on_confirm,
            bg=Colors.PRIMARY,
            fg=Colors.TEXT_WHITE,
            activebackground=Colors.PRIMARY_LIGHT
        )
        self.confirm_button.pack(side=tk.LEFT, padx=(0, 5))

        # 取消按钮
        self.cancel_button = tk.Button(
            button_frame,
            text="取消",
            font=font_manager.get_button_font(),
            command=self._on_cancel,
            bg=Colors.BACKGROUND_DARK,
            fg=Colors.TEXT,
            activebackground=Colors.BORDER
        )
        self.cancel_button.pack(side=tk.RIGHT)

    def _get_role_name(self) -> str:
        """获取角色名称"""
        role_names = {
            Role.SEER: "预言家",
            Role.WITCH: "女巫",
            Role.GUARD: "守卫",
            Role.WEREWOLF: "狼人"
        }
        return role_names.get(self.player_role, "未知角色")

    def _get_role_description(self) -> str:
        """获取角色描述"""
        descriptions = {
            Role.SEER: "每晚可以查验一个玩家的身份",
            Role.WITCH: "拥有一瓶解药和一瓶毒药，各用一次",
            Role.GUARD: "每晚可以保护一个玩家不被狼人杀死",
            Role.WEREWOLF: "与其他狼人协商选择杀害目标"
        }
        return descriptions.get(self.player_role, "")

    def _get_role_actions(self) -> Dict[str, Dict[str, str]]:
        """获取角色可用行动"""
        actions = {
            Role.SEER: {
                "seer_check": {
                    "name": "查验身份",
                    "description": "查看目标玩家的真实身份"
                }
            },
            Role.WITCH: {
                "witch_save": {
                    "name": "使用解药",
                    "description": "救治被狼人杀害的玩家"
                },
                "witch_poison": {
                    "name": "使用毒药",
                    "description": "毒杀一个玩家"
                }
            },
            Role.GUARD: {
                "guard_protect": {
                    "name": "保护玩家",
                    "description": "保护目标玩家免受狼人攻击"
                }
            },
            Role.WEREWOLF: {
                "werewolf_kill": {
                    "name": "杀害玩家",
                    "description": "选择要杀害的目标"
                }
            }
        }
        return actions.get(self.player_role, {})

    def _on_action_change(self):
        """行动选择改变事件"""
        selected_action = self.action_var.get()

        if selected_action == "skip":
            self._set_target_frame_state(False)
        else:
            self._set_target_frame_state(True)
            self._populate_target_list(selected_action)

    def _set_target_frame_state(self, enabled: bool):
        """设置目标选择框架状态"""
        state = tk.NORMAL if enabled else tk.DISABLED

        # 更新列表框状态
        self.target_listbox.config(state=state)

        if not enabled:
            self.target_listbox.delete(0, tk.END)

    def _populate_target_list(self, action: str):
        """填充目标列表"""
        self.target_listbox.delete(0, tk.END)

        for player in self.players:
            if self._can_target_player(player, action):
                display_text = self._get_player_display_text(player)
                self.target_listbox.insert(tk.END, display_text)

    def _can_target_player(self, player: PlayerInfo, action: str) -> bool:
        """判断是否可以选择该玩家作为目标"""
        if action in ["seer_check", "guard_protect"]:
            return player.is_alive()  # 只能选择存活玩家
        elif action == "werewolf_kill":
            return player.is_alive() and player.role != Role.WEREWOLF  # 不能杀同伴
        elif action == "witch_poison":
            return player.is_alive()  # 只能毒存活玩家
        elif action == "witch_save":
            return True  # 女巫救人可能需要特殊逻辑

        return True

    def _get_player_display_text(self, player: PlayerInfo) -> str:
        """获取玩家显示文本"""
        status = "存活" if player.is_alive() else "死亡"
        return f"{player.name} ({status})"

    def _on_target_select(self, event):
        """目标选择事件"""
        # 这里可以添加目标选择的逻辑
        pass

    def _on_confirm(self):
        """确认行动"""
        selected_action = self.action_var.get()

        if selected_action == "skip":
            self.result = {
                'action': 'skip',
                'target_id': None
            }
        else:
            # 检查是否选择了目标
            selection = self.target_listbox.curselection()
            if not selection:
                messagebox.showwarning("警告", "请选择一个目标玩家")
                return

            # 获取目标玩家
            index = selection[0]
            valid_players = [p for p in self.players if self._can_target_player(p, selected_action)]
            if index >= len(valid_players):
                messagebox.showerror("错误", "无效的目标选择")
                return

            target_player = valid_players[index]

            self.result = {
                'action': selected_action,
                'target_id': target_player.player_id
            }

        self.dialog.destroy()

    def _on_cancel(self):
        """取消行动"""
        self.result = None
        self.dialog.destroy()

    def show(self) -> Optional[Dict[str, Any]]:
        """显示对话框并返回结果"""
        self.dialog.wait_window()
        return self.result

def show_action_dialog(parent, player_role: Role, players: List[PlayerInfo], available_actions: List[str]) -> Optional[Dict[str, Any]]:
    """显示行动对话框的便捷函数"""
    dialog = ActionDialog(parent, player_role, players, available_actions)
    return dialog.show()