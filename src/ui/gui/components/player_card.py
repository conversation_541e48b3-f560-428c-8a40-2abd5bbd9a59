"""
玩家卡片组件
显示单个玩家的信息，包括姓名、角色、状态等
"""
import tkinter as tk
from tkinter import ttk
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../..'))

from ...styles.colors import Colors, RoleColors
from ...styles.fonts import font_manager
from ....models.enums import Role, PlayerStatus
from ....models.player import PlayerInfo

class PlayerCard(ttk.Frame):
    """玩家卡片组件"""

    def __init__(self, parent, player_info: PlayerInfo, show_role: bool = False, **kwargs):
        super().__init__(parent, **kwargs)

        self.player_info = player_info
        self.show_role = show_role
        self.is_selected = False
        self.on_click_callback = None

        self._create_widgets()
        self._update_display()
        self._bind_events()

    def _create_widgets(self):
        """创建组件"""
        # 主框架
        self.main_frame = tk.Frame(self, relief=tk.RAISED, borderwidth=1)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)

        # 玩家名称
        self.name_label = tk.Label(
            self.main_frame,
            font=font_manager.get_body_font(),
            anchor=tk.W
        )
        self.name_label.pack(fill=tk.X, padx=5, pady=(5, 2))

        # 角色信息（可选显示）
        self.role_label = tk.Label(
            self.main_frame,
            font=font_manager.get_small_font(),
            anchor=tk.W
        )

        # 状态信息
        self.status_frame = tk.Frame(self.main_frame)
        self.status_frame.pack(fill=tk.X, padx=5, pady=(2, 5))

        # 生存状态指示器
        self.status_indicator = tk.Label(
            self.status_frame,
            width=2,
            font=font_manager.get_small_font()
        )
        self.status_indicator.pack(side=tk.LEFT)

        # 状态文本
        self.status_label = tk.Label(
            self.status_frame,
            font=font_manager.get_small_font(),
            anchor=tk.W
        )
        self.status_label.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 0))

    def _update_display(self):
        """更新显示内容"""
        # 更新玩家名称
        self.name_label.config(text=self.player_info.name)

        # 更新角色信息
        if self.show_role:
            role_name = self._get_role_name(self.player_info.role)
            role_color = RoleColors.get_role_color(self.player_info.role.name)
            self.role_label.config(text=f"角色: {role_name}", fg=role_color)
            self.role_label.pack(fill=tk.X, padx=5, pady=2)
        else:
            self.role_label.pack_forget()

        # 更新状态
        self._update_status()

        # 更新背景色
        self._update_background()

    def _update_status(self):
        """更新状态显示"""
        if self.player_info.is_alive():
            self.status_indicator.config(text="●", fg=Colors.SUCCESS)
            self.status_label.config(text="存活", fg=Colors.SUCCESS)
        else:
            self.status_indicator.config(text="●", fg=Colors.DANGER)
            self.status_label.config(text="死亡", fg=Colors.DANGER)

    def _update_background(self):
        """更新背景色"""
        if self.is_selected:
            bg_color = Colors.PRIMARY_LIGHT
            fg_color = Colors.TEXT_WHITE
        elif not self.player_info.is_alive():
            bg_color = Colors.BACKGROUND_DARK
            fg_color = Colors.TEXT_LIGHT
        else:
            bg_color = Colors.BACKGROUND_LIGHT
            fg_color = Colors.TEXT

        # 更新所有组件的背景色
        self.main_frame.config(bg=bg_color)
        self.name_label.config(bg=bg_color, fg=fg_color)
        self.role_label.config(bg=bg_color)
        self.status_frame.config(bg=bg_color)
        self.status_indicator.config(bg=bg_color)
        self.status_label.config(bg=bg_color)

    def _bind_events(self):
        """绑定事件"""
        # 绑定点击事件
        widgets = [self.main_frame, self.name_label, self.role_label,
                  self.status_frame, self.status_indicator, self.status_label]

        for widget in widgets:
            widget.bind("<Button-1>", self._on_click)
            widget.bind("<Enter>", self._on_enter)
            widget.bind("<Leave>", self._on_leave)

    def _on_click(self, event):
        """点击事件处理"""
        if self.on_click_callback:
            self.on_click_callback(self.player_info.player_id)

    def _on_enter(self, event):
        """鼠标进入事件"""
        if not self.is_selected and self.player_info.is_alive():
            self.main_frame.config(relief=tk.RAISED, borderwidth=2)

    def _on_leave(self, event):
        """鼠标离开事件"""
        if not self.is_selected:
            self.main_frame.config(relief=tk.RAISED, borderwidth=1)

    def _get_role_name(self, role: Role) -> str:
        """获取角色中文名"""
        role_names = {
            Role.VILLAGER: "村民",
            Role.WEREWOLF: "狼人",
            Role.SEER: "预言家",
            Role.WITCH: "女巫",
            Role.GUARD: "守卫",
            Role.HUNTER: "猎人"
        }
        return role_names.get(role, "未知角色")

    def set_selected(self, selected: bool):
        """设置选中状态"""
        self.is_selected = selected
        self._update_background()

        if selected:
            self.main_frame.config(relief=tk.SUNKEN, borderwidth=2)
        else:
            self.main_frame.config(relief=tk.RAISED, borderwidth=1)

    def set_click_callback(self, callback):
        """设置点击回调函数"""
        self.on_click_callback = callback

    def update_player_info(self, player_info: PlayerInfo):
        """更新玩家信息"""
        self.player_info = player_info
        self._update_display()

    def set_show_role(self, show_role: bool):
        """设置是否显示角色"""
        self.show_role = show_role
        self._update_display()

class PlayerListPanel(ttk.Frame):
    """玩家列表面板"""

    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)

        self.player_cards = {}
        self.selected_player_id = None
        self.on_player_select_callback = None
        self.show_roles = False

        self._create_widgets()

    def _create_widgets(self):
        """创建组件"""
        # 滚动框架
        self.canvas = tk.Canvas(self)
        self.scrollbar = ttk.Scrollbar(self, orient=tk.VERTICAL, command=self.canvas.yview)
        self.scrollable_frame = ttk.Frame(self.canvas)

        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )

        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=self.scrollbar.set)

        # 布局
        self.canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def add_player(self, player_info: PlayerInfo):
        """添加玩家"""
        player_card = PlayerCard(
            self.scrollable_frame,
            player_info,
            show_role=self.show_roles
        )
        player_card.set_click_callback(self._on_player_click)
        player_card.pack(fill=tk.X, pady=2)

        self.player_cards[player_info.player_id] = player_card

    def remove_player(self, player_id: int):
        """移除玩家"""
        if player_id in self.player_cards:
            self.player_cards[player_id].destroy()
            del self.player_cards[player_id]

    def update_player(self, player_info: PlayerInfo):
        """更新玩家信息"""
        if player_info.player_id in self.player_cards:
            self.player_cards[player_info.player_id].update_player_info(player_info)

    def clear_players(self):
        """清空所有玩家"""
        for card in self.player_cards.values():
            card.destroy()
        self.player_cards.clear()
        self.selected_player_id = None

    def set_show_roles(self, show_roles: bool):
        """设置是否显示角色"""
        self.show_roles = show_roles
        for card in self.player_cards.values():
            card.set_show_role(show_roles)

    def select_player(self, player_id: int):
        """选择玩家"""
        # 取消之前的选择
        if self.selected_player_id and self.selected_player_id in self.player_cards:
            self.player_cards[self.selected_player_id].set_selected(False)

        # 设置新选择
        self.selected_player_id = player_id
        if player_id and player_id in self.player_cards:
            self.player_cards[player_id].set_selected(True)

    def _on_player_click(self, player_id: int):
        """玩家点击事件"""
        self.select_player(player_id)

        if self.on_player_select_callback:
            self.on_player_select_callback(player_id)

    def set_player_select_callback(self, callback):
        """设置玩家选择回调"""
        self.on_player_select_callback = callback

    def get_selected_player_id(self) -> int:
        """获取选中的玩家ID"""
        return self.selected_player_id