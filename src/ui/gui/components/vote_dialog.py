"""
投票对话框组件
提供投票选择界面
"""
import tkinter as tk
from tkinter import ttk, messagebox
from typing import Optional, List, Callable
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../..'))

from ...styles.colors import Colors, RoleColors
from ...styles.fonts import font_manager
from ....models.player import PlayerInfo
from ....models.enums import VoteType

class VoteDialog:
    """投票对话框"""

    def __init__(self, parent, players: List[PlayerInfo], vote_type: VoteType = VoteType.ELIMINATION):
        self.parent = parent
        self.players = players
        self.vote_type = vote_type
        self.selected_player_id = None
        self.result = None
        self.reason = ""

        self.dialog = None
        self.player_listbox = None
        self.reason_entry = None
        self.confirm_button = None
        self.cancel_button = None
        self.abstain_button = None

        self._create_dialog()

    def _create_dialog(self):
        """创建对话框"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title(self._get_dialog_title())
        self.dialog.geometry("400x500")
        self.dialog.resizable(False, False)
        self.dialog.transient(self.parent)
        self.dialog.grab_set()

        # 居中显示
        self._center_dialog()

        # 创建内容
        self._create_content()

        # 绑定事件
        self.dialog.protocol("WM_DELETE_WINDOW", self._on_cancel)

    def _center_dialog(self):
        """居中显示对话框"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (self.dialog.winfo_width() // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")

    def _create_content(self):
        """创建对话框内容"""
        # 标题
        title_label = tk.Label(
            self.dialog,
            text=self._get_dialog_title(),
            font=font_manager.get_title_font(),
            fg=Colors.PRIMARY
        )
        title_label.pack(pady=(10, 20))

        # 说明文本
        desc_label = tk.Label(
            self.dialog,
            text=self._get_description(),
            font=font_manager.get_body_font(),
            wraplength=350,
            justify=tk.CENTER
        )
        desc_label.pack(pady=(0, 20))

        # 玩家选择区域
        self._create_player_selection()

        # 投票原因输入
        if self.vote_type == VoteType.ELIMINATION:
            self._create_reason_input()

        # 按钮区域
        self._create_buttons()

    def _create_player_selection(self):
        """创建玩家选择区域"""
        # 玩家列表框架
        list_frame = ttk.LabelFrame(self.dialog, text="选择玩家")
        list_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=(0, 10))

        # 创建列表框和滚动条
        list_container = tk.Frame(list_frame)
        list_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.player_listbox = tk.Listbox(
            list_container,
            font=font_manager.get_body_font(),
            selectmode=tk.SINGLE
        )

        scrollbar = ttk.Scrollbar(list_container, orient=tk.VERTICAL)
        self.player_listbox.config(yscrollcommand=scrollbar.set)
        scrollbar.config(command=self.player_listbox.yview)

        # 布局
        self.player_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 填充玩家列表
        self._populate_player_list()

        # 绑定选择事件
        self.player_listbox.bind('<<ListboxSelect>>', self._on_player_select)
        self.player_listbox.bind('<Double-Button-1>', self._on_confirm)

    def _populate_player_list(self):
        """填充玩家列表"""
        for player in self.players:
            if self._should_show_player(player):
                display_text = self._get_player_display_text(player)
                self.player_listbox.insert(tk.END, display_text)

                # 设置颜色
                index = self.player_listbox.size() - 1
                if not player.is_alive():
                    self.player_listbox.itemconfig(index, fg=Colors.TEXT_LIGHT)

    def _should_show_player(self, player: PlayerInfo) -> bool:
        """判断是否应该显示该玩家"""
        if self.vote_type == VoteType.ELIMINATION:
            return player.is_alive()  # 投票只显示存活玩家
        else:
            return True  # 其他类型显示所有玩家

    def _get_player_display_text(self, player: PlayerInfo) -> str:
        """获取玩家显示文本"""
        status = "存活" if player.is_alive() else "死亡"
        return f"{player.name} ({status})"

    def _create_reason_input(self):
        """创建投票原因输入"""
        reason_frame = ttk.LabelFrame(self.dialog, text="投票原因（可选）")
        reason_frame.pack(fill=tk.X, padx=20, pady=(0, 10))

        self.reason_entry = tk.Entry(
            reason_frame,
            font=font_manager.get_body_font()
        )
        self.reason_entry.pack(fill=tk.X, padx=5, pady=5)
        self.reason_entry.insert(0, "基于发言分析")

    def _create_buttons(self):
        """创建按钮区域"""
        button_frame = tk.Frame(self.dialog)
        button_frame.pack(fill=tk.X, padx=20, pady=(0, 20))

        # 确认按钮
        self.confirm_button = tk.Button(
            button_frame,
            text="确认投票",
            font=font_manager.get_button_font(),
            command=self._on_confirm,
            state=tk.DISABLED,
            bg=Colors.PRIMARY,
            fg=Colors.TEXT_WHITE,
            activebackground=Colors.PRIMARY_LIGHT
        )
        self.confirm_button.pack(side=tk.LEFT, padx=(0, 5))

        # 弃权按钮（仅投票时显示）
        if self.vote_type == VoteType.ELIMINATION:
            self.abstain_button = tk.Button(
                button_frame,
                text="弃权",
                font=font_manager.get_button_font(),
                command=self._on_abstain,
                bg=Colors.WARNING,
                fg=Colors.TEXT_WHITE,
                activebackground=Colors.WARNING_LIGHT
            )
            self.abstain_button.pack(side=tk.LEFT, padx=5)

        # 取消按钮
        self.cancel_button = tk.Button(
            button_frame,
            text="取消",
            font=font_manager.get_button_font(),
            command=self._on_cancel,
            bg=Colors.BACKGROUND_DARK,
            fg=Colors.TEXT,
            activebackground=Colors.BORDER
        )
        self.cancel_button.pack(side=tk.RIGHT)

    def _get_dialog_title(self) -> str:
        """获取对话框标题"""
        titles = {
            VoteType.ELIMINATION: "投票淘汰",
            VoteType.WEREWOLF_KILL: "狼人杀人",
            VoteType.SEER_CHECK: "预言家查验",
            VoteType.WITCH_SAVE: "女巫救人",
            VoteType.WITCH_POISON: "女巫毒人",
            VoteType.GUARD_PROTECT: "守卫保护"
        }
        return titles.get(self.vote_type, "选择玩家")

    def _get_description(self) -> str:
        """获取描述文本"""
        descriptions = {
            VoteType.ELIMINATION: "选择要投票淘汰的玩家，或选择弃权",
            VoteType.WEREWOLF_KILL: "选择要杀害的玩家",
            VoteType.SEER_CHECK: "选择要查验身份的玩家",
            VoteType.WITCH_SAVE: "选择要救治的玩家",
            VoteType.WITCH_POISON: "选择要毒杀的玩家",
            VoteType.GUARD_PROTECT: "选择要保护的玩家"
        }
        return descriptions.get(self.vote_type, "请选择一个玩家")

    def _on_player_select(self, event):
        """玩家选择事件"""
        selection = self.player_listbox.curselection()
        if selection:
            index = selection[0]
            # 找到对应的玩家
            valid_players = [p for p in self.players if self._should_show_player(p)]
            if index < len(valid_players):
                self.selected_player_id = valid_players[index].player_id
                self.confirm_button.config(state=tk.NORMAL)

    def _on_confirm(self, event=None):
        """确认投票"""
        if self.selected_player_id is None:
            messagebox.showwarning("警告", "请先选择一个玩家")
            return

        # 获取投票原因
        if self.reason_entry:
            self.reason = self.reason_entry.get().strip()

        self.result = {
            'action': 'vote',
            'target_id': self.selected_player_id,
            'reason': self.reason
        }

        self.dialog.destroy()

    def _on_abstain(self):
        """弃权"""
        self.result = {
            'action': 'abstain',
            'target_id': None,
            'reason': "选择弃权"
        }

        self.dialog.destroy()

    def _on_cancel(self):
        """取消"""
        self.result = None
        self.dialog.destroy()

    def show(self) -> Optional[dict]:
        """显示对话框并返回结果"""
        self.dialog.wait_window()
        return self.result

def show_vote_dialog(parent, players: List[PlayerInfo], vote_type: VoteType = VoteType.ELIMINATION) -> Optional[dict]:
    """显示投票对话框的便捷函数"""
    dialog = VoteDialog(parent, players, vote_type)
    return dialog.show()