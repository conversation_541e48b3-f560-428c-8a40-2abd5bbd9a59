"""
游戏面板组件
显示游戏状态、阶段信息和操作区域
"""
import tkinter as tk
from tkinter import ttk
from typing import Optional, Callable, Dict, List
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))

from ..styles.colors import Colors
from ..styles.fonts import font_manager
from ...models.enums import GamePhase, GameResult, Role
from ...models.game_state import GameState

class GameBoard(ttk.Frame):
    """游戏面板组件"""

    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)

        self.game_state: Optional[GameState] = None
        self.on_vote_callback: Optional[Callable] = None
        self.on_action_callback: Optional[Callable] = None

        self._create_widgets()

    def _create_widgets(self):
        """创建组件"""
        # 游戏状态显示区域
        self._create_status_area()

        # 阶段信息区域
        self._create_phase_area()

        # 操作区域
        self._create_action_area()

    def _create_status_area(self):
        """创建状态显示区域"""
        self.status_frame = ttk.LabelFrame(self, text="游戏状态")
        self.status_frame.pack(fill=tk.X, pady=(0, 10))

        # 游戏基本信息
        self.info_frame = tk.Frame(self.status_frame)
        self.info_frame.pack(fill=tk.X, padx=5, pady=5)

        # 游戏ID
        self.game_id_label = tk.Label(
            self.info_frame,
            text="游戏ID: 未开始",
            font=font_manager.get_body_font(),
            anchor=tk.W
        )
        self.game_id_label.pack(fill=tk.X)

        # 玩家统计
        self.player_stats_label = tk.Label(
            self.info_frame,
            text="玩家: 0/0 存活",
            font=font_manager.get_body_font(),
            anchor=tk.W
        )
        self.player_stats_label.pack(fill=tk.X)

        # 阵营统计
        self.faction_stats_label = tk.Label(
            self.info_frame,
            text="村民: 0 | 狼人: 0",
            font=font_manager.get_body_font(),
            anchor=tk.W
        )
        self.faction_stats_label.pack(fill=tk.X)

    def _create_phase_area(self):
        """创建阶段信息区域"""
        self.phase_frame = ttk.LabelFrame(self, text="当前阶段")
        self.phase_frame.pack(fill=tk.X, pady=(0, 10))

        # 阶段显示
        self.phase_display_frame = tk.Frame(self.phase_frame)
        self.phase_display_frame.pack(fill=tk.X, padx=5, pady=5)

        # 阶段名称
        self.phase_name_label = tk.Label(
            self.phase_display_frame,
            text="等待开始",
            font=font_manager.get_title_font(),
            anchor=tk.CENTER
        )
        self.phase_name_label.pack(fill=tk.X)

        # 回合信息
        self.round_info_label = tk.Label(
            self.phase_display_frame,
            text="第 0 回合",
            font=font_manager.get_body_font(),
            anchor=tk.CENTER
        )
        self.round_info_label.pack(fill=tk.X, pady=(5, 0))

        # 阶段描述
        self.phase_desc_label = tk.Label(
            self.phase_display_frame,
            text="点击开始新游戏",
            font=font_manager.get_body_font(),
            anchor=tk.CENTER,
            wraplength=300
        )
        self.phase_desc_label.pack(fill=tk.X, pady=(5, 0))

    def _create_action_area(self):
        """创建操作区域"""
        self.action_frame = ttk.LabelFrame(self, text="操作区域")
        self.action_frame.pack(fill=tk.BOTH, expand=True)

        # 操作按钮区域
        self.button_frame = tk.Frame(self.action_frame)
        self.button_frame.pack(fill=tk.X, padx=5, pady=5)

        # 投票按钮
        self.vote_button = tk.Button(
            self.button_frame,
            text="投票",
            font=font_manager.get_button_font(),
            command=self._on_vote_click,
            state=tk.DISABLED,
            bg=Colors.PRIMARY,
            fg=Colors.TEXT_WHITE,
            activebackground=Colors.PRIMARY_LIGHT
        )
        self.vote_button.pack(side=tk.LEFT, padx=(0, 5))

        # 技能按钮
        self.skill_button = tk.Button(
            self.button_frame,
            text="使用技能",
            font=font_manager.get_button_font(),
            command=self._on_skill_click,
            state=tk.DISABLED,
            bg=Colors.SECONDARY,
            fg=Colors.TEXT_WHITE,
            activebackground=Colors.SECONDARY_LIGHT
        )
        self.skill_button.pack(side=tk.LEFT, padx=5)

        # 弃权按钮
        self.abstain_button = tk.Button(
            self.button_frame,
            text="弃权",
            font=font_manager.get_button_font(),
            command=self._on_abstain_click,
            state=tk.DISABLED,
            bg=Colors.WARNING,
            fg=Colors.TEXT_WHITE,
            activebackground=Colors.WARNING_LIGHT
        )
        self.abstain_button.pack(side=tk.LEFT, padx=5)

        # 操作提示区域
        self.hint_frame = tk.Frame(self.action_frame)
        self.hint_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=(0, 5))

        # 操作提示文本
        self.hint_text = tk.Text(
            self.hint_frame,
            height=6,
            wrap=tk.WORD,
            font=font_manager.get_body_font(),
            state=tk.DISABLED,
            bg=Colors.BACKGROUND_LIGHT
        )
        self.hint_text.pack(fill=tk.BOTH, expand=True)

    def update_game_state(self, game_state: GameState):
        """更新游戏状态"""
        self.game_state = game_state

        # 更新状态信息
        self._update_status_info(game_state)

        # 更新阶段信息
        self._update_phase_info(game_state)

        # 更新操作区域
        self._update_action_area(game_state)

    def _update_status_info(self, game_state: GameState):
        """更新状态信息"""
        # 游戏ID
        self.game_id_label.config(text=f"游戏ID: {game_state.game_id}")

        # 玩家统计
        total_players = len(game_state.players)
        alive_players = len(game_state.get_alive_players())
        self.player_stats_label.config(text=f"玩家: {alive_players}/{total_players} 存活")

        # 阵营统计
        villager_count = len([p for p in game_state.get_alive_players()
                             if p.faction.name == "VILLAGERS"])
        werewolf_count = len([p for p in game_state.get_alive_players()
                             if p.faction.name == "WEREWOLVES"])
        self.faction_stats_label.config(text=f"村民: {villager_count} | 狼人: {werewolf_count}")

    def _update_phase_info(self, game_state: GameState):
        """更新阶段信息"""
        # 阶段名称
        phase_name = self._get_phase_name(game_state.current_phase)
        self.phase_name_label.config(text=phase_name)

        # 回合信息
        self.round_info_label.config(text=f"第 {game_state.current_round} 回合")

        # 阶段描述
        phase_desc = self._get_phase_description(game_state.current_phase)
        self.phase_desc_label.config(text=phase_desc)

        # 设置阶段颜色
        phase_color = self._get_phase_color(game_state.current_phase)
        self.phase_name_label.config(fg=phase_color)

    def _update_action_area(self, game_state: GameState):
        """更新操作区域"""
        # 根据游戏阶段启用/禁用按钮
        if game_state.current_phase == GamePhase.DAY_VOTING:
            self.vote_button.config(state=tk.NORMAL)
            self.abstain_button.config(state=tk.NORMAL)
            self.skill_button.config(state=tk.DISABLED)
        elif game_state.current_phase == GamePhase.NIGHT:
            self.vote_button.config(state=tk.DISABLED)
            self.abstain_button.config(state=tk.DISABLED)
            self.skill_button.config(state=tk.NORMAL)
        else:
            self.vote_button.config(state=tk.DISABLED)
            self.abstain_button.config(state=tk.DISABLED)
            self.skill_button.config(state=tk.DISABLED)

        # 更新操作提示
        self._update_action_hints(game_state)

    def _update_action_hints(self, game_state: GameState):
        """更新操作提示"""
        self.hint_text.config(state=tk.NORMAL)
        self.hint_text.delete(1.0, tk.END)

        hints = self._get_action_hints(game_state.current_phase)
        self.hint_text.insert(tk.END, hints)

        self.hint_text.config(state=tk.DISABLED)

    def _get_phase_name(self, phase: GamePhase) -> str:
        """获取阶段名称"""
        phase_names = {
            GamePhase.SETUP: "游戏设置",
            GamePhase.NIGHT: "夜晚阶段",
            GamePhase.DAY_DISCUSSION: "白天讨论",
            GamePhase.DAY_VOTING: "白天投票",
            GamePhase.GAME_OVER: "游戏结束"
        }
        return phase_names.get(phase, "未知阶段")

    def _get_phase_description(self, phase: GamePhase) -> str:
        """获取阶段描述"""
        descriptions = {
            GamePhase.SETUP: "准备开始游戏",
            GamePhase.NIGHT: "狼人行动，特殊角色使用技能",
            GamePhase.DAY_DISCUSSION: "所有玩家发言讨论",
            GamePhase.DAY_VOTING: "投票淘汰可疑玩家",
            GamePhase.GAME_OVER: "游戏已结束"
        }
        return descriptions.get(phase, "")

    def _get_phase_color(self, phase: GamePhase) -> str:
        """获取阶段颜色"""
        colors = {
            GamePhase.SETUP: Colors.INFO,
            GamePhase.NIGHT: Colors.PRIMARY_DARK,
            GamePhase.DAY_DISCUSSION: Colors.WARNING,
            GamePhase.DAY_VOTING: Colors.DANGER,
            GamePhase.GAME_OVER: Colors.SUCCESS
        }
        return colors.get(phase, Colors.TEXT)

    def _get_action_hints(self, phase: GamePhase) -> str:
        """获取操作提示"""
        hints = {
            GamePhase.SETUP: "游戏准备中，等待开始...",
            GamePhase.NIGHT: "夜晚阶段：\n- 狼人选择杀害目标\n- 预言家查验身份\n- 女巫使用药剂\n- 守卫保护玩家",
            GamePhase.DAY_DISCUSSION: "白天讨论：\n- 所有玩家发言\n- 分析昨夜情况\n- 讨论可疑对象",
            GamePhase.DAY_VOTING: "投票阶段：\n- 选择要淘汰的玩家\n- 点击投票按钮确认\n- 可以选择弃权",
            GamePhase.GAME_OVER: "游戏已结束\n查看最终结果"
        }
        return hints.get(phase, "")

    def _on_vote_click(self):
        """投票按钮点击"""
        if self.on_vote_callback:
            self.on_vote_callback()

    def _on_skill_click(self):
        """技能按钮点击"""
        if self.on_action_callback:
            self.on_action_callback("skill")

    def _on_abstain_click(self):
        """弃权按钮点击"""
        if self.on_action_callback:
            self.on_action_callback("abstain")

    def set_vote_callback(self, callback: Callable):
        """设置投票回调"""
        self.on_vote_callback = callback

    def set_action_callback(self, callback: Callable):
        """设置操作回调"""
        self.on_action_callback = callback

    def add_hint_message(self, message: str):
        """添加提示消息"""
        self.hint_text.config(state=tk.NORMAL)
        self.hint_text.insert(tk.END, f"\n{message}")
        self.hint_text.see(tk.END)
        self.hint_text.config(state=tk.DISABLED)