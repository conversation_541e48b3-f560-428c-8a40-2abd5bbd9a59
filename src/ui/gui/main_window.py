"""
主窗口实现
狼人杀游戏的主界面窗口
"""
import tkinter as tk
from tkinter import ttk, messagebox
from typing import Optional, Callable
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))

from ..styles.colors import Colors, ThemeColors
from ..styles.fonts import font_manager
from ...models.enums import GamePhase, GameResult
from ...models.game_state import GameState
from ...engine.game_engine import GameEngine
from .components.player_card import PlayerListPanel
from .game_board import GameBoard

class MainWindow:
    """主窗口类"""

    def __init__(self):
        self.root = tk.Tk()
        self.game_engine: Optional[GameEngine] = None
        self.game_state: Optional[GameState] = None

        # 回调函数
        self.on_new_game: Optional[Callable] = None
        self.on_settings: Optional[Callable] = None
        self.on_help: Optional[Callable] = None
        self.on_exit: Optional[Callable] = None

        # UI组件
        self.menu_bar = None
        self.status_bar = None
        self.main_frame = None
        self.left_panel = None
        self.center_panel = None
        self.right_panel = None

        self._setup_window()
        self._create_menu()
        self._create_layout()
        self._create_status_bar()
        self._apply_theme()

    def _setup_window(self):
        """设置窗口基本属性"""
        self.root.title("狼人杀AI游戏")
        self.root.geometry("1280x800")
        self.root.minsize(1024, 768)

        # 设置窗口图标（如果有的话）
        try:
            # 这里可以设置窗口图标
            # self.root.iconbitmap("path/to/icon.ico")
            pass
        except:
            pass

        # 设置窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self._on_window_close)

        # 居中显示窗口
        self._center_window()

    def _center_window(self):
        """将窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

    def _create_menu(self):
        """创建菜单栏"""
        self.menu_bar = tk.Menu(self.root)
        self.root.config(menu=self.menu_bar)

        # 文件菜单
        file_menu = tk.Menu(self.menu_bar, tearoff=0)
        self.menu_bar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="新游戏", command=self._on_new_game, accelerator="Ctrl+N")
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self._on_window_close, accelerator="Ctrl+Q")

        # 游戏菜单
        game_menu = tk.Menu(self.menu_bar, tearoff=0)
        self.menu_bar.add_cascade(label="游戏", menu=game_menu)
        game_menu.add_command(label="暂停/继续", command=self._on_pause_resume)
        game_menu.add_command(label="重新开始", command=self._on_restart)

        # 设置菜单
        settings_menu = tk.Menu(self.menu_bar, tearoff=0)
        self.menu_bar.add_cascade(label="设置", menu=settings_menu)
        settings_menu.add_command(label="游戏设置", command=self._on_settings)
        settings_menu.add_command(label="音效设置", command=self._on_sound_settings)

        # 帮助菜单
        help_menu = tk.Menu(self.menu_bar, tearoff=0)
        self.menu_bar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="游戏规则", command=self._on_help)
        help_menu.add_command(label="操作指南", command=self._on_tutorial)
        help_menu.add_separator()
        help_menu.add_command(label="关于", command=self._on_about)

        # 绑定快捷键
        self.root.bind('<Control-n>', lambda e: self._on_new_game())
        self.root.bind('<Control-q>', lambda e: self._on_window_close())

    def _create_layout(self):
        """创建主布局"""
        # 主框架
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建三列布局
        self._create_left_panel()
        self._create_center_panel()
        self._create_right_panel()

    def _create_left_panel(self):
        """创建左侧面板（玩家信息）"""
        self.left_panel = ttk.Frame(self.main_frame, width=250)
        self.left_panel.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 5))
        self.left_panel.pack_propagate(False)

        # 玩家信息标题
        title_label = ttk.Label(
            self.left_panel,
            text="玩家信息",
            font=font_manager.get_title_font()
        )
        title_label.pack(pady=(0, 10))

        # 玩家列表面板
        self.player_list_panel = PlayerListPanel(self.left_panel)
        self.player_list_panel.pack(fill=tk.BOTH, expand=True)
        self.player_list_panel.set_player_select_callback(self._on_player_select)

        # 游戏状态信息
        self.game_info_frame = ttk.LabelFrame(self.left_panel, text="游戏状态")
        self.game_info_frame.pack(fill=tk.X, pady=(10, 0))

        # 当前阶段
        self.phase_label = ttk.Label(
            self.game_info_frame,
            text="当前阶段: 等待开始",
            font=font_manager.get_body_font()
        )
        self.phase_label.pack(anchor=tk.W, padx=5, pady=2)

        # 回合信息
        self.round_label = ttk.Label(
            self.game_info_frame,
            text="回合: 0",
            font=font_manager.get_body_font()
        )
        self.round_label.pack(anchor=tk.W, padx=5, pady=2)

    def _create_center_panel(self):
        """创建中央面板（游戏主区域）"""
        self.center_panel = ttk.Frame(self.main_frame)
        self.center_panel.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)

        # 游戏面板
        self.game_board = GameBoard(self.center_panel)
        self.game_board.pack(fill=tk.BOTH, expand=True)
        self.game_board.set_vote_callback(self._on_vote)
        self.game_board.set_action_callback(self._on_action)

        # 控制按钮框架
        self.control_frame = ttk.Frame(self.center_panel)
        self.control_frame.pack(fill=tk.X, pady=(10, 0))

        # 开始游戏按钮
        self.start_button = ttk.Button(
            self.control_frame,
            text="开始新游戏",
            command=self._on_new_game
        )
        self.start_button.pack(side=tk.LEFT, padx=(0, 5))

        # 暂停按钮
        self.pause_button = ttk.Button(
            self.control_frame,
            text="暂停",
            command=self._on_pause_resume,
            state=tk.DISABLED
        )
        self.pause_button.pack(side=tk.LEFT, padx=5)

        # 设置按钮
        self.settings_button = ttk.Button(
            self.control_frame,
            text="设置",
            command=self._on_settings
        )
        self.settings_button.pack(side=tk.LEFT, padx=5)

        # 帮助按钮
        self.help_button = ttk.Button(
            self.control_frame,
            text="帮助",
            command=self._on_help
        )
        self.help_button.pack(side=tk.LEFT, padx=5)

    def _create_right_panel(self):
        """创建右侧面板（聊天/发言）"""
        self.right_panel = ttk.Frame(self.main_frame, width=300)
        self.right_panel.pack(side=tk.RIGHT, fill=tk.Y, padx=(5, 0))
        self.right_panel.pack_propagate(False)

        # 聊天/发言标题
        title_label = ttk.Label(
            self.right_panel,
            text="发言记录",
            font=font_manager.get_title_font()
        )
        title_label.pack(pady=(0, 10))

        # 发言历史区域
        self.chat_frame = ttk.Frame(self.right_panel)
        self.chat_frame.pack(fill=tk.BOTH, expand=True)

        # 发言历史文本框
        self.chat_text = tk.Text(
            self.chat_frame,
            wrap=tk.WORD,
            font=font_manager.get_body_font(),
            state=tk.DISABLED
        )

        # 滚动条
        chat_scrollbar = ttk.Scrollbar(self.chat_frame, orient=tk.VERTICAL, command=self.chat_text.yview)
        self.chat_text.configure(yscrollcommand=chat_scrollbar.set)

        # 布局
        self.chat_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        chat_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 输入区域
        self.input_frame = ttk.Frame(self.right_panel)
        self.input_frame.pack(fill=tk.X, pady=(10, 0))

        # 输入框
        self.input_entry = ttk.Entry(
            self.input_frame,
            font=font_manager.get_body_font()
        )
        self.input_entry.pack(fill=tk.X, pady=(0, 5))
        self.input_entry.bind('<Return>', self._on_send_message)

        # 发送按钮
        self.send_button = ttk.Button(
            self.input_frame,
            text="发送",
            command=self._on_send_message
        )
        self.send_button.pack(fill=tk.X)

    def _create_status_bar(self):
        """创建状态栏"""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)

        # 状态标签
        self.status_label = ttk.Label(
            self.status_bar,
            text="就绪",
            font=font_manager.get_small_font()
        )
        self.status_label.pack(side=tk.LEFT, padx=5, pady=2)

        # 时间标签
        self.time_label = ttk.Label(
            self.status_bar,
            text="",
            font=font_manager.get_small_font()
        )
        self.time_label.pack(side=tk.RIGHT, padx=5, pady=2)

    def _apply_theme(self):
        """应用主题样式"""
        style = ttk.Style()

        # 配置样式
        style.configure('TFrame', background=Colors.BACKGROUND)
        style.configure('TLabel', background=Colors.BACKGROUND, foreground=Colors.TEXT)
        style.configure('TButton', font=font_manager.get_button_font())

        # 设置根窗口背景
        self.root.configure(bg=Colors.BACKGROUND)

    # 事件处理方法
    def _on_new_game(self):
        """新游戏事件"""
        if self.on_new_game:
            self.on_new_game()

    def _on_pause_resume(self):
        """暂停/继续事件"""
        # TODO: 实现暂停/继续逻辑
        current_text = self.pause_button.cget('text')
        if current_text == "暂停":
            self.pause_button.config(text="继续")
            self.update_status("游戏已暂停")
        else:
            self.pause_button.config(text="暂停")
            self.update_status("游戏继续")

    def _on_restart(self):
        """重新开始事件"""
        result = messagebox.askyesno("确认", "确定要重新开始游戏吗？")
        if result:
            self._on_new_game()

    def _on_settings(self):
        """设置事件"""
        if self.on_settings:
            self.on_settings()
        else:
            messagebox.showinfo("设置", "设置功能正在开发中...")

    def _on_sound_settings(self):
        """音效设置事件"""
        messagebox.showinfo("音效设置", "音效设置功能正在开发中...")

    def _on_help(self):
        """帮助事件"""
        if self.on_help:
            self.on_help()
        else:
            self._show_game_rules()

    def _on_tutorial(self):
        """教程事件"""
        messagebox.showinfo("操作指南", "操作指南功能正在开发中...")

    def _on_about(self):
        """关于事件"""
        messagebox.showinfo(
            "关于",
            "狼人杀AI游戏 v1.0\n\n"
            "一个基于Python的狼人杀AI游戏实现\n"
            "支持多种AI策略和人机对战\n\n"
            "开发者: AI Assistant"
        )

    def _on_send_message(self, event=None):
        """发送消息事件"""
        message = self.input_entry.get().strip()
        if message:
            self.add_chat_message("玩家", message)
            self.input_entry.delete(0, tk.END)

    def _on_player_select(self, player_id: int):
        """玩家选择事件"""
        if self.game_state:
            player = self.game_state.players.get(player_id)
            if player:
                self.add_chat_message("系统", f"选择了玩家: {player.name}")

    def _on_vote(self):
        """投票事件"""
        selected_player_id = self.player_list_panel.get_selected_player_id()
        if selected_player_id and self.game_state:
            player = self.game_state.players.get(selected_player_id)
            if player:
                self.add_chat_message("系统", f"投票给: {player.name}")
                # TODO: 实际投票逻辑

    def _on_action(self, action_type: str):
        """操作事件"""
        if action_type == "skill":
            self.add_chat_message("系统", "使用技能")
            # TODO: 技能使用逻辑
        elif action_type == "abstain":
            self.add_chat_message("系统", "选择弃权")
            # TODO: 弃权逻辑

    def _on_window_close(self):
        """窗口关闭事件"""
        if self.on_exit:
            self.on_exit()
        else:
            result = messagebox.askyesno("确认", "确定要退出游戏吗？")
            if result:
                self.root.quit()

    def _show_game_rules(self):
        """显示游戏规则"""
        rules_text = """
狼人杀游戏规则：

1. 角色介绍：
   - 村民：普通村民，白天参与讨论和投票
   - 狼人：夜晚杀人，白天伪装成村民
   - 预言家：每晚可以查验一个人的身份
   - 女巫：有一瓶解药和一瓶毒药，各用一次
   - 守卫：每晚可以保护一个人不被狼人杀死
   - 猎人：被淘汰时可以开枪带走一个人

2. 游戏流程：
   - 夜晚：狼人杀人，特殊角色使用技能
   - 白天：讨论阶段，所有人发言讨论
   - 投票：投票淘汰一个人

3. 胜利条件：
   - 村民阵营：淘汰所有狼人
   - 狼人阵营：狼人数量 >= 村民数量
        """

        # 创建规则窗口
        rules_window = tk.Toplevel(self.root)
        rules_window.title("游戏规则")
        rules_window.geometry("500x400")
        rules_window.resizable(False, False)

        # 规则文本
        rules_text_widget = tk.Text(
            rules_window,
            wrap=tk.WORD,
            font=font_manager.get_body_font(),
            padx=10,
            pady=10
        )
        rules_text_widget.pack(fill=tk.BOTH, expand=True)
        rules_text_widget.insert(tk.END, rules_text)
        rules_text_widget.config(state=tk.DISABLED)

        # 关闭按钮
        close_button = ttk.Button(
            rules_window,
            text="关闭",
            command=rules_window.destroy
        )
        close_button.pack(pady=10)

    # 公共方法
    def set_game_engine(self, game_engine: GameEngine):
        """设置游戏引擎"""
        self.game_engine = game_engine

    def update_game_state(self, game_state: GameState):
        """更新游戏状态显示"""
        self.game_state = game_state

        # 更新阶段信息
        phase_text = self._get_phase_text(game_state.current_phase)
        self.phase_label.config(text=f"当前阶段: {phase_text}")

        # 更新回合信息
        self.round_label.config(text=f"回合: {game_state.current_round}")

        # 更新游戏面板
        self.game_board.update_game_state(game_state)

        # 更新玩家列表
        self._update_player_list(game_state)

        # 更新按钮状态
        self._update_button_states(game_state)

    def _update_player_list(self, game_state: GameState):
        """更新玩家列表"""
        # 清空现有玩家
        self.player_list_panel.clear_players()

        # 添加所有玩家
        for player_id, player in game_state.players.items():
            self.player_list_panel.add_player(player)

        # 根据游戏阶段决定是否显示角色
        show_roles = game_state.current_phase == GamePhase.GAME_OVER
        self.player_list_panel.set_show_roles(show_roles)

    def _get_phase_text(self, phase: GamePhase) -> str:
        """获取阶段文本"""
        phase_map = {
            GamePhase.SETUP: "游戏设置",
            GamePhase.NIGHT: "夜晚阶段",
            GamePhase.DAY_DISCUSSION: "白天讨论",
            GamePhase.DAY_VOTING: "白天投票",
            GamePhase.GAME_OVER: "游戏结束"
        }
        return phase_map.get(phase, "未知阶段")



    def _get_result_text(self, result: GameResult) -> str:
        """获取结果文本"""
        result_map = {
            GameResult.VILLAGERS_WIN: "村民胜利",
            GameResult.WEREWOLVES_WIN: "狼人胜利",
            GameResult.DRAW: "平局",
            GameResult.ONGOING: "进行中"
        }
        return result_map.get(result, "未知结果")

    def _update_button_states(self, game_state: GameState):
        """更新按钮状态"""
        if game_state.current_phase == GamePhase.SETUP:
            self.start_button.config(state=tk.NORMAL, text="开始游戏")
            self.pause_button.config(state=tk.DISABLED)
        elif game_state.current_phase == GamePhase.GAME_OVER:
            self.start_button.config(state=tk.NORMAL, text="开始新游戏")
            self.pause_button.config(state=tk.DISABLED)
        else:
            self.start_button.config(state=tk.DISABLED)
            self.pause_button.config(state=tk.NORMAL)

    def add_chat_message(self, sender: str, message: str):
        """添加聊天消息"""
        self.chat_text.config(state=tk.NORMAL)
        self.chat_text.insert(tk.END, f"{sender}: {message}\n")
        self.chat_text.see(tk.END)
        self.chat_text.config(state=tk.DISABLED)

    def update_status(self, status: str):
        """更新状态栏"""
        self.status_label.config(text=status)

    def show(self):
        """显示窗口"""
        self.root.mainloop()

    def destroy(self):
        """销毁窗口"""
        self.root.destroy()