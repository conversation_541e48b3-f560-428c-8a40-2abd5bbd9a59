"""
GUI颜色定义
定义整个应用的颜色方案
"""

class Colors:
    """颜色常量定义"""

    # 主色调
    PRIMARY = "#2C3E50"          # 深蓝色
    PRIMARY_LIGHT = "#34495E"    # 浅深蓝色
    PRIMARY_DARK = "#1A252F"     # 更深蓝色

    # 辅助色
    SECONDARY = "#E67E22"        # 橙色
    SECONDARY_LIGHT = "#F39C12"  # 浅橙色
    SECONDARY_DARK = "#D35400"   # 深橙色

    # 背景色
    BACKGROUND = "#ECF0F1"       # 浅灰色
    BACKGROUND_DARK = "#BDC3C7"  # 深灰色
    BACKGROUND_LIGHT = "#FFFFFF" # 白色

    # 文字色
    TEXT = "#2C3E50"             # 深灰色
    TEXT_LIGHT = "#7F8C8D"       # 浅灰色
    TEXT_WHITE = "#FFFFFF"       # 白色

    # 状态色
    SUCCESS = "#27AE60"          # 绿色
    SUCCESS_LIGHT = "#2ECC71"    # 浅绿色
    SUCCESS_DARK = "#1E8449"     # 深绿色

    WARNING = "#F39C12"          # 黄色
    WARNING_LIGHT = "#F1C40F"    # 浅黄色
    WARNING_DARK = "#E67E22"     # 深黄色

    DANGER = "#E74C3C"           # 红色
    DANGER_LIGHT = "#EC7063"     # 浅红色
    DANGER_DARK = "#C0392B"      # 深红色

    INFO = "#3498DB"             # 蓝色
    INFO_LIGHT = "#5DADE2"       # 浅蓝色
    INFO_DARK = "#2980B9"        # 深蓝色

    # 角色颜色
    VILLAGER = "#27AE60"         # 村民 - 绿色
    WEREWOLF = "#E74C3C"         # 狼人 - 红色
    SEER = "#3498DB"             # 预言家 - 蓝色
    WITCH = "#9B59B6"            # 女巫 - 紫色
    GUARD = "#F39C12"            # 守卫 - 橙色
    HUNTER = "#8B4513"           # 猎人 - 棕色

    # 边框色
    BORDER = "#BDC3C7"           # 边框灰色
    BORDER_LIGHT = "#D5DBDB"     # 浅边框色
    BORDER_DARK = "#85929E"      # 深边框色

    # 阴影色
    SHADOW = "#34495E"           # 阴影色
    SHADOW_LIGHT = "#5D6D7E"     # 浅阴影色

class RoleColors:
    """角色颜色映射"""

    @staticmethod
    def get_role_color(role_name: str) -> str:
        """根据角色名获取颜色"""
        role_color_map = {
            "VILLAGER": Colors.VILLAGER,
            "WEREWOLF": Colors.WEREWOLF,
            "SEER": Colors.SEER,
            "WITCH": Colors.WITCH,
            "GUARD": Colors.GUARD,
            "HUNTER": Colors.HUNTER,
        }
        return role_color_map.get(role_name.upper(), Colors.TEXT)

class ThemeColors:
    """主题颜色方案"""

    # 默认主题
    DEFAULT = {
        "bg": Colors.BACKGROUND,
        "fg": Colors.TEXT,
        "select_bg": Colors.PRIMARY,
        "select_fg": Colors.TEXT_WHITE,
        "button_bg": Colors.PRIMARY,
        "button_fg": Colors.TEXT_WHITE,
        "button_active_bg": Colors.PRIMARY_LIGHT,
        "entry_bg": Colors.BACKGROUND_LIGHT,
        "entry_fg": Colors.TEXT,
        "frame_bg": Colors.BACKGROUND,
    }

    # 深色主题
    DARK = {
        "bg": Colors.PRIMARY_DARK,
        "fg": Colors.TEXT_WHITE,
        "select_bg": Colors.SECONDARY,
        "select_fg": Colors.TEXT_WHITE,
        "button_bg": Colors.SECONDARY,
        "button_fg": Colors.TEXT_WHITE,
        "button_active_bg": Colors.SECONDARY_LIGHT,
        "entry_bg": Colors.PRIMARY,
        "entry_fg": Colors.TEXT_WHITE,
        "frame_bg": Colors.PRIMARY_DARK,
    }