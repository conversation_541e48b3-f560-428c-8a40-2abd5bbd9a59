"""
GUI字体定义
定义整个应用的字体方案
"""
import tkinter.font as tkfont
import platform

class Fonts:
    """字体常量定义"""

    # 根据操作系统选择合适的字体
    system = platform.system()

    if system == "Windows":
        DEFAULT_FAMILY = "Microsoft YaHei"
        FALLBACK_FAMILY = "SimHei"
    elif system == "Darwin":  # macOS
        DEFAULT_FAMILY = "PingFang SC"
        FALLBACK_FAMILY = "Helvetica"
    else:  # Linux
        DEFAULT_FAMILY = "DejaVu Sans"
        FALLBACK_FAMILY = "Liberation Sans"

    # 字体大小
    SIZE_LARGE = 16
    SIZE_MEDIUM = 12
    SIZE_SMALL = 10
    SIZE_TINY = 8

    # 字体权重
    WEIGHT_BOLD = "bold"
    WEIGHT_NORMAL = "normal"

    # 字体样式
    SLANT_ITALIC = "italic"
    SLANT_ROMAN = "roman"

class FontManager:
    """字体管理器"""

    def __init__(self):
        self._fonts = {}
        self._initialized = False

    def _ensure_initialized(self):
        """确保字体已初始化"""
        if not self._initialized:
            self._create_fonts()
            self._initialized = True

    def _create_fonts(self):
        """创建字体对象"""
        try:
            # 标题字体
            self._fonts['title'] = tkfont.Font(
                family=Fonts.DEFAULT_FAMILY,
                size=Fonts.SIZE_LARGE,
                weight=Fonts.WEIGHT_BOLD
            )

            # 正文字体
            self._fonts['body'] = tkfont.Font(
                family=Fonts.DEFAULT_FAMILY,
                size=Fonts.SIZE_MEDIUM,
                weight=Fonts.WEIGHT_NORMAL
            )

            # 按钮字体
            self._fonts['button'] = tkfont.Font(
                family=Fonts.DEFAULT_FAMILY,
                size=Fonts.SIZE_SMALL,
                weight=Fonts.WEIGHT_NORMAL
            )

            # 小字体
            self._fonts['small'] = tkfont.Font(
                family=Fonts.DEFAULT_FAMILY,
                size=Fonts.SIZE_TINY,
                weight=Fonts.WEIGHT_NORMAL
            )

            # 粗体字体
            self._fonts['bold'] = tkfont.Font(
                family=Fonts.DEFAULT_FAMILY,
                size=Fonts.SIZE_MEDIUM,
                weight=Fonts.WEIGHT_BOLD
            )

            # 斜体字体
            self._fonts['italic'] = tkfont.Font(
                family=Fonts.DEFAULT_FAMILY,
                size=Fonts.SIZE_MEDIUM,
                slant=Fonts.SLANT_ITALIC
            )

        except Exception as e:
            print(f"字体创建失败，使用默认字体: {e}")
            # 使用系统默认字体作为后备
            self._create_fallback_fonts()

    def _create_fallback_fonts(self):
        """创建后备字体"""
        self._fonts['title'] = tkfont.Font(size=Fonts.SIZE_LARGE, weight=Fonts.WEIGHT_BOLD)
        self._fonts['body'] = tkfont.Font(size=Fonts.SIZE_MEDIUM)
        self._fonts['button'] = tkfont.Font(size=Fonts.SIZE_SMALL)
        self._fonts['small'] = tkfont.Font(size=Fonts.SIZE_TINY)
        self._fonts['bold'] = tkfont.Font(size=Fonts.SIZE_MEDIUM, weight=Fonts.WEIGHT_BOLD)
        self._fonts['italic'] = tkfont.Font(size=Fonts.SIZE_MEDIUM, slant=Fonts.SLANT_ITALIC)

    def get_font(self, font_type: str) -> tkfont.Font:
        """获取字体对象"""
        self._ensure_initialized()
        return self._fonts.get(font_type, self._fonts['body'])

    def get_title_font(self) -> tkfont.Font:
        """获取标题字体"""
        return self.get_font('title')

    def get_body_font(self) -> tkfont.Font:
        """获取正文字体"""
        return self.get_font('body')

    def get_button_font(self) -> tkfont.Font:
        """获取按钮字体"""
        return self.get_font('button')

    def get_small_font(self) -> tkfont.Font:
        """获取小字体"""
        return self.get_font('small')

    def get_bold_font(self) -> tkfont.Font:
        """获取粗体字体"""
        return self.get_font('bold')

    def get_italic_font(self) -> tkfont.Font:
        """获取斜体字体"""
        return self.get_font('italic')

# 全局字体管理器实例
font_manager = FontManager()