{"cells": [{"cell_type": "code", "execution_count": 1, "id": "6de8d217", "metadata": {}, "outputs": [], "source": ["from src.ai.llm_integration import OpenAIProvider\n", "from src.config.llm_config import llm_config_manager\n", "\n", "llm = OpenAIProvider(llm_config_manager.get_config('qwen3_30b'))"]}, {"cell_type": "code", "execution_count": 3, "id": "fcb6e184", "metadata": {}, "outputs": [{"data": {"text/plain": ["'\\n\\n你好！很高兴见到你！😊 有什么我可以帮你的吗？'"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["llm.generate_text_sync(\"你好\")"]}], "metadata": {"kernelspec": {"display_name": "werewolf", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}