#!/usr/bin/env python3
"""
GUI组件测试脚本
验证GUI组件的功能和正确性
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_imports():
    """测试导入"""
    print("测试导入...")

    try:
        from src.ui.styles.colors import Colors, RoleColors, ThemeColors
        print("✅ 颜色模块导入成功")

        from src.ui.styles.fonts import Fonts, FontManager, font_manager
        print("✅ 字体模块导入成功")

        # 测试颜色功能
        print(f"主色调: {Colors.PRIMARY}")
        print(f"村民颜色: {RoleColors.get_role_color('VILLAGER')}")

        print("✅ 所有导入测试通过")
        return True

    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_structure():
    """测试GUI结构"""
    print("\n测试GUI结构...")

    try:
        # 检查文件是否存在
        gui_files = [
            'src/ui/gui/main_window.py',
            'src/ui/gui/game_board.py',
            'src/ui/gui/components/player_card.py',
            'src/ui/styles/colors.py',
            'src/ui/styles/fonts.py'
        ]

        for file_path in gui_files:
            if os.path.exists(file_path):
                print(f"✅ {file_path} 存在")
            else:
                print(f"❌ {file_path} 不存在")
                return False

        print("✅ GUI结构测试通过")
        return True

    except Exception as e:
        print(f"❌ GUI结构测试失败: {e}")
        return False

def test_component_classes():
    """测试组件类"""
    print("\n测试组件类...")

    try:
        # 测试不需要tkinter的部分
        from src.ui.styles.colors import Colors
        from src.models.enums import Role
        from src.models.player import PlayerInfo

        # 创建测试玩家信息
        player_info = PlayerInfo(
            player_id=1,
            name="测试玩家",
            role=Role.VILLAGER
        )

        print(f"✅ 创建测试玩家: {player_info.name}")
        print(f"✅ 玩家角色: {player_info.role}")
        print(f"✅ 玩家存活状态: {player_info.is_alive()}")

        print("✅ 组件类测试通过")
        return True

    except Exception as e:
        print(f"❌ 组件类测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_game_integration():
    """测试游戏集成"""
    print("\n测试游戏集成...")

    try:
        from src.models.game_state import GameConfig
        from src.models.enums import Role
        from src.engine.game_engine import GameEngine

        # 创建游戏配置
        config = GameConfig(
            total_players=6,
            role_distribution={
                Role.VILLAGER: 2,
                Role.WEREWOLF: 2,
                Role.SEER: 1,
                Role.WITCH: 1
            }
        )

        # 创建游戏引擎
        game_engine = GameEngine(config)

        # 创建游戏
        player_names = [f"AI玩家{i+1}" for i in range(6)]
        game_state = game_engine.create_game("test_game", player_names)

        print(f"✅ 游戏创建成功: {game_state.game_id}")
        print(f"✅ 玩家数量: {len(game_state.players)}")
        print(f"✅ 当前阶段: {game_state.current_phase}")

        print("✅ 游戏集成测试通过")
        return True

    except Exception as e:
        print(f"❌ 游戏集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("GUI组件测试")
    print("=" * 50)

    tests = [
        test_imports,
        test_gui_structure,
        test_component_classes,
        test_game_integration
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        if test():
            passed += 1

    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")

    if passed == total:
        print("🎉 所有测试通过！GUI组件准备就绪。")
        print("\n使用说明:")
        print("1. 在有图形界面的环境中运行: python main_gui.py")
        print("2. 或者使用控制台版本: python main.py")
    else:
        print("❌ 部分测试失败，请检查错误信息")

    print("=" * 50)

if __name__ == "__main__":
    main()