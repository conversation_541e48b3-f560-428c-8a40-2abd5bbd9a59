#!/usr/bin/env python3
"""
详细的大模型调用测试
测试Qwen3-30B在各种狼人杀场景下的表现
"""

import sys
import os
import time

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.models.enums import Role, GamePhase
from src.models.game_state import GameConfig, GameState
from src.ai.llm_integration import create_llm_manager
from src.ai.ai_strategy_manager import AIStrategyManager
from src.ai.ai_personality import DifficultyLevel, PersonalityType


def print_separator(char='-', length=80):
    """打印分隔线"""
    print(char * length)


def print_header(title):
    """打印标题"""
    print_separator('=')
    print(f" {title} ".center(80, '='))
    print_separator('=')


def test_basic_text_generation():
    """测试基础文本生成"""
    print_header("测试基础文本生成")
    
    llm_manager = create_llm_manager("qwen3_30b")
    
    test_prompts = [
        "请简单介绍狼人杀游戏",
        "作为村民，你会如何分析局势？",
        "作为狼人，你会如何隐藏身份？",
        "作为预言家，你会如何引导讨论？",
        "请用一句话总结狼人杀的核心策略"
    ]
    
    for i, prompt in enumerate(test_prompts, 1):
        print(f"\n测试 {i}: {prompt}")
        try:
            response = llm_manager.provider.generate_text_sync(prompt)
            print(f"回答: {response}")
            time.sleep(1)  # 避免请求过快
        except Exception as e:
            print(f"生成失败: {e}")
    
    print("\n基础文本生成测试完成")


def test_role_specific_speech():
    """测试角色专用发言"""
    print_header("测试角色专用发言")
    
    llm_manager = create_llm_manager("qwen3_30b")
    
    # 创建测试游戏状态
    config = GameConfig(
        total_players=6,
        role_distribution={Role.VILLAGER: 3, Role.WEREWOLF: 2, Role.SEER: 1}
    )
    
    game_state = GameState(
        game_id="test_game",
        config=config,
        players={},
        current_phase=GamePhase.DAY_DISCUSSION
    )
    
    # 测试不同角色和情境
    test_scenarios = [
        {
            "role": Role.VILLAGER,
            "context": {"player_id": 1, "situation": "first_day"},
            "description": "村民第一天发言"
        },
        {
            "role": Role.WEREWOLF,
            "context": {"player_id": 2, "situation": "under_suspicion"},
            "description": "狼人被怀疑时的发言"
        },
        {
            "role": Role.SEER,
            "context": {"player_id": 3, "situation": "reveal_info", "checked_player": "player_2", "is_werewolf": True},
            "description": "预言家公布查验结果"
        },
        {
            "role": Role.VILLAGER,
            "context": {"player_id": 4, "situation": "voting_phase"},
            "description": "村民投票阶段发言"
        },
        {
            "role": Role.WEREWOLF,
            "context": {"player_id": 5, "situation": "teammate_exposed"},
            "description": "狼人队友暴露后的发言"
        }
    ]
    
    for scenario in test_scenarios:
        print(f"\n场景: {scenario['description']}")
        print(f"角色: {scenario['role'].name}")
        
        try:
            speech = llm_manager.generate_speech(
                scenario['role'], 
                game_state, 
                scenario['context']
            )
            print(f"发言: {speech}")
            time.sleep(1)
        except Exception as e:
            print(f"发言生成失败: {e}")
    
    print("\n角色专用发言测试完成")


def test_strategy_analysis():
    """测试策略分析"""
    print_header("测试策略分析")
    
    llm_manager = create_llm_manager("qwen3_30b")
    
    # 创建测试游戏状态
    config = GameConfig(
        total_players=6,
        role_distribution={Role.VILLAGER: 3, Role.WEREWOLF: 2, Role.SEER: 1}
    )
    
    game_state = GameState(
        game_id="test_game",
        config=config,
        players={},
        current_phase=GamePhase.DAY_VOTING
    )
    
    # 测试不同角色的策略分析
    strategy_tests = [
        {
            "role": Role.WEREWOLF,
            "actions": ["kill_player_1", "kill_player_3", "kill_player_4"],
            "description": "狼人夜晚杀人选择"
        },
        {
            "role": Role.VILLAGER,
            "actions": ["vote_player_2", "vote_player_5", "abstain"],
            "description": "村民投票选择"
        },
        {
            "role": Role.SEER,
            "actions": ["check_player_2", "check_player_5", "check_player_6"],
            "description": "预言家查验选择"
        }
    ]
    
    for test in strategy_tests:
        print(f"\n策略分析: {test['description']}")
        print(f"角色: {test['role'].name}")
        print(f"可选行动: {test['actions']}")
        
        try:
            analysis = llm_manager.generate_strategy_analysis(
                test['role'],
                game_state,
                test['actions']
            )
            
            print(f"推荐行动: {analysis.get('recommended_action', 'unknown')}")
            print(f"信心度: {analysis.get('confidence', 0):.2f}")
            print(f"推理过程: {analysis.get('reasoning', 'no reasoning')}")
            time.sleep(1)
        except Exception as e:
            print(f"策略分析失败: {e}")
    
    print("\n策略分析测试完成")


def test_ai_player_integration():
    """测试AI玩家集成"""
    print_header("测试AI玩家集成")
    
    ai_manager = AIStrategyManager()
    
    # 创建不同类型的AI玩家
    ai_configs = [
        ("智能村民", PersonalityType.ANALYTICAL, DifficultyLevel.EXPERT),
        ("狡猾狼人", PersonalityType.DECEPTIVE, DifficultyLevel.HARD),
        ("领导预言家", PersonalityType.SOCIAL, DifficultyLevel.EXPERT),
        ("谨慎守卫", PersonalityType.DEFENSIVE, DifficultyLevel.NORMAL)
    ]
    
    ai_players = []
    for name, personality, difficulty in ai_configs:
        ai_player = ai_manager.create_ai_player(
            player_id=len(ai_players) + 1,
            name=name,
            difficulty=difficulty,
            personality_type=personality,
            use_llm=True,
            llm_config="qwen3_30b"
        )
        ai_players.append(ai_player)
        print(f"创建AI玩家: {name} ({personality.value}, {difficulty.value})")
    
    # 创建测试游戏状态
    config = GameConfig(
        total_players=4,
        role_distribution={Role.VILLAGER: 1, Role.WEREWOLF: 1, Role.SEER: 1, Role.GUARD: 1}
    )
    
    from src.engine.game_engine import GameEngine
    engine = GameEngine(config)
    player_names = [ai.name for ai in ai_players]
    game_state = engine.create_game("llm_test", player_names)
    
    # 为AI分配角色
    roles = [Role.VILLAGER, Role.WEREWOLF, Role.SEER, Role.GUARD]
    for i, ai_player in enumerate(ai_players):
        ai_player.set_role(roles[i])
        print(f"{ai_player.name} 分配角色: {roles[i].name}")
    
    # 测试AI发言
    print(f"\n测试AI发言:")
    for ai_player in ai_players:
        print(f"\n{ai_player.name} ({ai_player.role.name}) 发言:")
        try:
            speech = ai_player.generate_speech(game_state, GamePhase.DAY_DISCUSSION)
            print(f"  {speech}")
            time.sleep(1)
        except Exception as e:
            print(f"  发言生成失败: {e}")
    
    print("\nAI玩家集成测试完成")


def test_performance_and_quality():
    """测试性能和质量"""
    print_header("测试性能和质量")
    
    llm_manager = create_llm_manager("qwen3_30b")
    
    # 性能测试
    print("性能测试:")
    test_prompt = "作为村民，你觉得谁最可疑？"
    
    response_times = []
    for i in range(3):
        start_time = time.time()
        try:
            response = llm_manager.provider.generate_text_sync(test_prompt)
            end_time = time.time()
            response_time = end_time - start_time
            response_times.append(response_time)
            print(f"  测试 {i+1}: {response_time:.2f}秒 - {response}")
        except Exception as e:
            print(f"  测试 {i+1}: 失败 - {e}")
        
        time.sleep(0.5)
    
    if response_times:
        avg_time = sum(response_times) / len(response_times)
        print(f"\n平均响应时间: {avg_time:.2f}秒")
        print(f"最快响应: {min(response_times):.2f}秒")
        print(f"最慢响应: {max(response_times):.2f}秒")
    
    # 质量测试
    print(f"\n质量测试:")
    quality_prompts = [
        "请分析当前局势，给出你的推理过程",
        "如果你是狼人，你会如何为自己辩护？",
        "作为预言家，你会如何说服大家相信你的身份？"
    ]
    
    for i, prompt in enumerate(quality_prompts, 1):
        print(f"\n质量测试 {i}: {prompt}")
        try:
            response = llm_manager.provider.generate_text_sync(prompt)
            print(f"回答: {response}")
            print(f"长度: {len(response)} 字符")
            time.sleep(1)
        except Exception as e:
            print(f"生成失败: {e}")
    
    print("\n性能和质量测试完成")


def main():
    """主测试函数"""
    print("狼人杀AI - 详细大模型调用测试")
    print("=" * 80)
    
    try:
        # 基础文本生成测试
        test_basic_text_generation()
        input("\n按回车键继续下一项测试...")
        
        # 角色专用发言测试
        test_role_specific_speech()
        input("\n按回车键继续下一项测试...")
        
        # 策略分析测试
        test_strategy_analysis()
        input("\n按回车键继续下一项测试...")
        
        # AI玩家集成测试
        test_ai_player_integration()
        input("\n按回车键继续下一项测试...")
        
        # 性能和质量测试
        test_performance_and_quality()
        
        print("\n" + "=" * 80)
        print("🎉 所有大模型调用测试完成!")
        print("测试项目:")
        print("✅ 基础文本生成")
        print("✅ 角色专用发言")
        print("✅ 策略分析")
        print("✅ AI玩家集成")
        print("✅ 性能和质量")
        print("=" * 80)
        
    except Exception as e:
        print(f"\n测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
