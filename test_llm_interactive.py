#!/usr/bin/env python3
"""
交互式大模型测试
允许用户直接与Qwen3-30B进行对话测试
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.ai.llm_integration import create_llm_manager


def test_direct_conversation():
    """直接对话测试"""
    print("=== Qwen3-30B 直接对话测试 ===")
    print("输入 'quit' 或 'exit' 退出")
    print("输入 'test' 运行预设测试")
    print("-" * 50)
    
    llm_manager = create_llm_manager("qwen3_30b")
    
    while True:
        try:
            user_input = input("\n你: ").strip()
            
            if user_input.lower() in ['quit', 'exit', '退出']:
                print("再见!")
                break
            
            if user_input.lower() == 'test':
                run_preset_tests(llm_manager)
                continue
            
            if not user_input:
                continue
            
            print("Qwen3-30B 思考中...")
            response = llm_manager.provider.generate_text_sync(user_input)
            print(f"Qwen3-30B: {response}")
            
        except KeyboardInterrupt:
            print("\n\n程序被用户中断")
            break
        except Exception as e:
            print(f"错误: {e}")


def run_preset_tests(llm_manager):
    """运行预设测试"""
    print("\n=== 运行预设测试 ===")
    
    tests = [
        "你好，请介绍一下自己",
        "什么是狼人杀游戏？",
        "作为村民，应该如何分析局势？",
        "作为狼人，如何隐藏身份？",
        "预言家的最佳策略是什么？",
        "请用一句话总结狼人杀的核心"
    ]
    
    for i, test in enumerate(tests, 1):
        print(f"\n测试 {i}: {test}")
        try:
            response = llm_manager.provider.generate_text_sync(test)
            print(f"回答: {response}")
        except Exception as e:
            print(f"错误: {e}")
    
    print("\n预设测试完成")


def main():
    """主函数"""
    print("狼人杀AI - Qwen3-30B 交互式测试")
    print("=" * 50)
    
    try:
        # 检查连接
        print("检查Qwen3-30B连接...")
        llm_manager = create_llm_manager("qwen3_30b")
        test_response = llm_manager.provider.generate_text_sync("测试连接")
        print(f"连接成功! 测试响应: {test_response}")
        
        # 开始交互式测试
        test_direct_conversation()
        
    except Exception as e:
        print(f"初始化失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
