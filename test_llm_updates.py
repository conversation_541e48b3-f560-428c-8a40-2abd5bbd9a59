#!/usr/bin/env python3
"""
测试LLM更新后的功能
验证history和system参数是否正常工作
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.ai.llm_integration import create_llm_manager, LLMConfig, OpenAIProvider, MockLLMProvider
from src.models.enums import Role, GamePhase
from src.models.game_state import GameState
from src.engine.game_engine import GameEngine
from src.config.game_modes import GameConfig


def print_header(title):
    """打印测试标题"""
    print("\n" + "=" * 60)
    print(f" {title}")
    print("=" * 60)


def test_basic_provider_functionality():
    """测试基础Provider功能"""
    print_header("测试基础Provider功能")
    
    # 测试MockLLMProvider
    print("\n1. 测试MockLLMProvider...")
    mock_config = LLMConfig(provider="mock", model_name="mock-model")
    mock_provider = MockLLMProvider(mock_config)
    
    # 测试不带参数
    response1 = mock_provider.generate_text_sync("你好")
    print(f"   无参数调用: {response1}")
    
    # 测试带history参数
    history = [
        {"role": "user", "content": "我是村民"},
        {"role": "assistant", "content": "我理解你的身份"}
    ]
    response2 = mock_provider.generate_text_sync("现在怎么办？", history=history)
    print(f"   带历史调用: {response2}")
    
    # 测试带system参数
    response3 = mock_provider.generate_text_sync(
        "分析局势", 
        system="你是狼人杀游戏中的预言家"
    )
    print(f"   带系统提示: {response3}")
    
    print("   MockLLMProvider测试完成 ✓")


def test_openai_provider_functionality():
    """测试OpenAIProvider功能"""
    print_header("测试OpenAIProvider功能")
    
    try:
        # 创建OpenAI Provider (使用qwen3配置)
        print("\n1. 测试OpenAIProvider...")
        llm_manager = create_llm_manager("qwen3_30b")
        provider = llm_manager.provider
        
        if not isinstance(provider, OpenAIProvider):
            print("   跳过OpenAI测试 - 当前使用的不是OpenAI Provider")
            return
        
        # 测试基础调用
        response1 = provider.generate_text_sync("你好，请简单回复")
        print(f"   基础调用: {response1}")
        
        # 测试带history的调用
        history = [
            {"role": "user", "content": "我是狼人杀游戏中的村民"},
            {"role": "assistant", "content": "我明白了，你需要找出狼人"}
        ]
        response2 = provider.generate_text_sync(
            "现在应该怎么分析？", 
            history=history
        )
        print(f"   带历史调用: {response2}")
        
        # 测试带system的调用
        response3 = provider.generate_text_sync(
            "请分析当前局势",
            system="你是狼人杀游戏中的预言家，拥有查验能力"
        )
        print(f"   带系统提示: {response3}")
        
        print("   OpenAIProvider测试完成 ✓")
        
    except Exception as e:
        print(f"   OpenAI测试失败: {e}")
        print("   这可能是因为服务未启动或配置问题")


def test_llm_manager_functionality():
    """测试LLMManager功能"""
    print_header("测试LLMManager功能")
    
    # 创建测试游戏状态
    config = GameConfig(
        total_players=4,
        role_distribution={Role.VILLAGER: 2, Role.WEREWOLF: 1, Role.SEER: 1}
    )
    
    engine = GameEngine(config)
    game_state = engine.create_game("test", ["Alice", "Bob", "Charlie", "David"])
    
    # 使用Mock LLM进行测试
    llm_manager = create_llm_manager("mock")
    
    print("\n1. 测试角色发言生成...")
    
    # 测试村民发言
    context = {"player_id": 1, "suspicions": {2: 0.7, 3: 0.3}}
    speech1 = llm_manager.generate_speech(Role.VILLAGER, game_state, context)
    print(f"   村民发言: {speech1}")
    
    # 测试狼人发言
    context = {"player_id": 2, "fake_identity": "VILLAGER", "teammates": [3]}
    speech2 = llm_manager.generate_speech(Role.WEREWOLF, game_state, context)
    print(f"   狼人发言: {speech2}")
    
    # 测试预言家发言
    context = {"player_id": 3, "check_results": [{"target_name": "Alice", "is_werewolf": False}]}
    speech3 = llm_manager.generate_speech(Role.SEER, game_state, context)
    print(f"   预言家发言: {speech3}")
    
    print("\n2. 测试策略分析...")
    
    # 测试策略分析
    actions = ["vote_1", "vote_2", "abstain"]
    analysis = llm_manager.generate_strategy_analysis(Role.VILLAGER, game_state, actions)
    print(f"   策略分析: {analysis}")
    
    print("   LLMManager测试完成 ✓")


def test_history_functionality():
    """测试历史对话功能"""
    print_header("测试历史对话功能")
    
    llm_manager = create_llm_manager("mock")
    
    # 模拟多轮对话
    history = []
    
    print("\n模拟多轮对话:")
    
    # 第一轮
    prompt1 = "我是村民，现在是第一天"
    response1 = llm_manager.provider.generate_text_sync(
        prompt1, 
        history=history,
        system="你是狼人杀游戏助手"
    )
    print(f"   用户: {prompt1}")
    print(f"   AI: {response1}")
    
    # 更新历史
    history.append({"role": "user", "content": prompt1})
    history.append({"role": "assistant", "content": response1})
    
    # 第二轮
    prompt2 = "我应该怀疑谁？"
    response2 = llm_manager.provider.generate_text_sync(
        prompt2,
        history=history,
        system="你是狼人杀游戏助手"
    )
    print(f"   用户: {prompt2}")
    print(f"   AI: {response2}")
    
    # 更新历史
    history.append({"role": "user", "content": prompt2})
    history.append({"role": "assistant", "content": response2})
    
    # 第三轮
    prompt3 = "基于之前的对话，给我最终建议"
    response3 = llm_manager.provider.generate_text_sync(
        prompt3,
        history=history,
        system="你是狼人杀游戏助手"
    )
    print(f"   用户: {prompt3}")
    print(f"   AI: {response3}")
    
    print("   历史对话测试完成 ✓")


def main():
    """主函数"""
    print("狼人杀AI - LLM更新功能测试")
    print("测试history和system参数的支持")
    
    try:
        # 运行所有测试
        test_basic_provider_functionality()
        test_openai_provider_functionality()
        test_llm_manager_functionality()
        test_history_functionality()
        
        print_header("所有测试完成")
        print("✅ LLM更新功能测试通过!")
        print("\n主要改进:")
        print("1. ✅ 基类BaseLLMProvider支持history和system参数")
        print("2. ✅ MockLLMProvider支持新参数")
        print("3. ✅ OpenAIProvider支持新参数并针对qwen3优化")
        print("4. ✅ LLMManager提供角色专用的system prompt")
        print("5. ✅ 各AI策略类支持历史对话构建")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
