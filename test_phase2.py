#!/usr/bin/env python3
"""
Phase 2 功能测试
验证新增的核心游戏逻辑功能
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.models.enums import Role, GamePhase, GameResult, VoteType
from src.models.game_state import GameConfig
from src.engine.game_engine import GameEngine
from src.engine.day_phase import DayPhaseManager
from src.engine.night_phase import NightPhaseManager
from src.engine.victory_conditions import VictoryConditionChecker
from src.engine.game_history import GameHistoryManager
from src.config.game_modes import GameModeManager
from src.players.ai_player import AIPlayer


def test_game_modes():
    """测试游戏模式系统"""
    print("=== 测试游戏模式系统 ===")
    
    mode_manager = GameModeManager()
    
    # 测试获取模式
    classic_mode = mode_manager.get_mode("classic")
    assert classic_mode is not None, "经典模式应该存在"
    print(f"经典模式: {classic_mode.name}")
    
    # 测试为不同玩家数创建配置
    config_8 = classic_mode.create_config_for_players(8)
    assert config_8 is not None, "应该能为8人创建配置"
    assert config_8.total_players == 8, "玩家数应该是8"
    print(f"8人局配置: {dict((role.name, count) for role, count in config_8.role_distribution.items())}")
    
    # 测试适合的模式
    suitable_modes = mode_manager.get_modes_for_player_count(6)
    assert len(suitable_modes) > 0, "应该有适合6人的模式"
    print(f"6人适合的模式: {suitable_modes}")
    
    # 测试自定义配置验证
    validation = mode_manager.validate_custom_config(6, {Role.VILLAGER: 3, Role.WEREWOLF: 2, Role.SEER: 1})
    assert validation['valid'], "配置应该有效"
    print(f"配置验证: {validation}")
    
    print("游戏模式系统测试通过!\n")
    return config_8


def test_enhanced_game_engine():
    """测试增强的游戏引擎"""
    print("=== 测试增强游戏引擎 ===")
    
    config = GameConfig(
        total_players=6,
        role_distribution={Role.VILLAGER: 2, Role.WEREWOLF: 2, Role.SEER: 1, Role.WITCH: 1}
    )
    
    engine = GameEngine(config)
    game_state = engine.create_game("test_game", ["A", "B", "C", "D", "E", "F"])
    
    # 测试管理器初始化
    assert engine.get_day_phase_manager() is not None, "白天阶段管理器应该初始化"
    assert engine.get_night_phase_manager() is not None, "夜晚阶段管理器应该初始化"
    assert engine.get_victory_checker() is not None, "胜负条件检查器应该初始化"
    assert engine.get_history_manager() is not None, "历史管理器应该初始化"
    
    print(f"游戏创建成功: {game_state.game_id}")
    print(f"管理器初始化完成")
    
    print("增强游戏引擎测试通过!\n")
    return engine, game_state


def test_night_phase_logic(engine, game_state):
    """测试夜晚阶段逻辑"""
    print("=== 测试夜晚阶段逻辑 ===")
    
    engine.start_game()
    night_result = engine.start_night_phase()
    
    assert game_state.current_phase == GamePhase.NIGHT, "应该进入夜晚阶段"
    print(f"夜晚阶段开始: {night_result}")
    
    night_manager = engine.get_night_phase_manager()
    
    # 测试狼人行动
    werewolves = game_state.get_werewolves()
    if werewolves:
        werewolf_votes = {werewolves[0].player_id: 1}  # 投票杀死玩家1
        werewolf_result = engine.submit_werewolf_action(werewolf_votes)
        assert werewolf_result['success'], "狼人行动应该成功"
        print(f"狼人行动成功: {werewolf_result}")
    
    # 测试预言家行动
    seers = game_state.get_players_by_role(Role.SEER)
    if seers:
        seer_result = engine.submit_seer_action(seers[0].player_id, 2)
        assert seer_result['success'], "预言家行动应该成功"
        print(f"预言家行动成功: {seer_result}")
    
    # 测试女巫行动
    witches = game_state.get_players_by_role(Role.WITCH)
    if witches:
        witch_result = engine.submit_witch_action(witches[0].player_id, save_target=1)
        assert witch_result['success'], "女巫行动应该成功"
        print(f"女巫行动成功: {witch_result}")
    
    # 测试夜晚完成状态
    night_status = night_manager.get_night_status()
    print(f"夜晚状态: {night_status}")
    
    if night_manager.is_night_complete():
        resolution = engine.resolve_night_actions()
        assert resolution['success'], "夜晚解决应该成功"
        print(f"夜晚解决成功: {len(resolution.get('deaths', []))}人死亡")
    
    print("夜晚阶段逻辑测试通过!\n")


def test_day_phase_logic(engine, game_state):
    """测试白天阶段逻辑"""
    print("=== 测试白天阶段逻辑 ===")
    
    # 测试讨论阶段
    discussion_result = engine.start_day_discussion()
    assert game_state.current_phase == GamePhase.DAY_DISCUSSION, "应该进入讨论阶段"
    print(f"讨论阶段开始: {discussion_result}")
    
    # 测试发言
    alive_players = game_state.get_alive_players()
    if alive_players:
        speech_result = engine.add_player_speech(alive_players[0].player_id, "测试发言")
        assert speech_result['success'], "发言应该成功"
        print(f"发言成功")
    
    # 测试投票阶段
    voting_result = engine.start_day_voting()
    assert game_state.current_phase == GamePhase.DAY_VOTING, "应该进入投票阶段"
    print(f"投票阶段开始: {voting_result}")
    
    # 测试投票
    if len(alive_players) >= 2:
        vote_result = engine.cast_elimination_vote(alive_players[0].player_id, alive_players[1].player_id, "测试投票")
        assert vote_result['success'], "投票应该成功"
        print(f"投票成功")
    
    # 测试投票解决
    day_manager = engine.get_day_phase_manager()
    voting_summary = day_manager.get_voting_summary()
    print(f"投票统计: {voting_summary}")
    
    print("白天阶段逻辑测试通过!\n")


def test_victory_conditions(engine, game_state):
    """测试胜负条件"""
    print("=== 测试胜负条件 ===")
    
    victory_checker = engine.get_victory_checker()
    
    # 测试当前胜负状态
    victory_result = engine.check_victory_conditions_detailed()
    print(f"当前胜负状态: {victory_result['result']}")
    
    # 测试胜利概率
    victory_prob = engine.get_victory_probability()
    assert 'villagers' in victory_prob, "应该有村民胜利概率"
    assert 'werewolves' in victory_prob, "应该有狼人胜利概率"
    print(f"胜利概率: 村民 {victory_prob['villagers']:.2%}, 狼人 {victory_prob['werewolves']:.2%}")
    
    # 测试游戏平衡信息
    balance_info = engine.get_game_balance_info()
    assert 'faction_counts' in balance_info, "应该有阵营统计"
    print(f"游戏平衡: {balance_info}")
    
    print("胜负条件测试通过!\n")


def test_game_history(engine):
    """测试游戏历史"""
    print("=== 测试游戏历史 ===")
    
    history_manager = engine.get_history_manager()
    assert history_manager is not None, "历史管理器应该存在"
    
    # 测试游戏总结
    summary = engine.get_game_summary()
    assert 'game_info' in summary, "应该有游戏信息"
    assert 'player_stats' in summary, "应该有玩家统计"
    assert 'event_summary' in summary, "应该有事件总结"
    
    print(f"游戏总结:")
    print(f"  总事件数: {summary['game_info']['total_events']}")
    print(f"  总回合数: {summary['game_info']['total_rounds']}")
    print(f"  事件统计: {summary['event_summary']}")
    
    # 测试历史导出
    filename = engine.export_game_history()
    assert filename.endswith('.json'), "应该导出JSON文件"
    print(f"历史导出成功: {filename}")
    
    # 清理测试文件
    if os.path.exists(filename):
        os.remove(filename)
        print(f"测试文件已清理")
    
    print("游戏历史测试通过!\n")


def test_ai_integration():
    """测试AI集成"""
    print("=== 测试AI集成 ===")
    
    config = GameConfig(
        total_players=4,
        role_distribution={Role.VILLAGER: 2, Role.WEREWOLF: 1, Role.SEER: 1}
    )
    
    engine = GameEngine(config)
    game_state = engine.create_game("ai_test", ["AI1", "AI2", "AI3", "AI4"])
    
    # 创建AI玩家
    ai_players = {}
    for player_id, player_info in game_state.players.items():
        ai_player = AIPlayer(player_id, player_info.name)
        ai_player.set_role(player_info.role)
        ai_players[player_id] = ai_player
    
    print(f"创建了 {len(ai_players)} 个AI玩家")
    
    # 测试AI决策
    engine.start_game()
    engine.start_night_phase()
    
    # AI狼人决策
    werewolves = game_state.get_werewolves()
    if werewolves:
        werewolf = werewolves[0]
        ai_werewolf = ai_players[werewolf.player_id]
        target = ai_werewolf.make_vote_decision(game_state, VoteType.WEREWOLF_KILL)
        assert target is not None, "AI狼人应该做出决策"
        print(f"AI狼人决策: 杀死玩家 {target}")
    
    # AI预言家决策
    seers = game_state.get_players_by_role(Role.SEER)
    if seers:
        seer = seers[0]
        ai_seer = ai_players[seer.player_id]
        target = ai_seer.make_special_action_decision(game_state, "seer_check")
        assert target is not None, "AI预言家应该做出决策"
        print(f"AI预言家决策: 查验玩家 {target}")
    
    print("AI集成测试通过!\n")


def main():
    """主测试函数"""
    print("狼人杀AI游戏 - Phase 2 功能测试")
    print("=" * 50)
    
    try:
        # 测试游戏模式
        config = test_game_modes()
        
        # 测试增强游戏引擎
        engine, game_state = test_enhanced_game_engine()
        
        # 测试夜晚阶段
        test_night_phase_logic(engine, game_state)
        
        # 测试白天阶段
        test_day_phase_logic(engine, game_state)
        
        # 测试胜负条件
        test_victory_conditions(engine, game_state)
        
        # 测试游戏历史
        test_game_history(engine)
        
        # 测试AI集成
        test_ai_integration()
        
        print("=" * 50)
        print("所有 Phase 2 测试通过!")
        print("✅ 游戏模式系统")
        print("✅ 增强游戏引擎")
        print("✅ 夜晚阶段逻辑")
        print("✅ 白天阶段逻辑")
        print("✅ 胜负条件判定")
        print("✅ 游戏历史记录")
        print("✅ AI集成")
        print("=" * 50)
        
    except Exception as e:
        print(f"\n测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
