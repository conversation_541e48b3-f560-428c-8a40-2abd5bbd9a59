#!/usr/bin/env python3
"""
Phase 3 功能测试
验证AI策略系统的所有功能
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.models.enums import Role, GamePhase, GameResult, VoteType
from src.models.game_state import GameConfig
from src.engine.game_engine import GameEngine
from src.ai.ai_strategy_manager import AIStrategyManager, EnhancedAIPlayer
from src.ai.ai_personality import DifficultyLevel, PersonalityType, AIPersonalityManager
from src.ai.llm_integration import create_llm_manager
from src.ai.villager_ai import VillagerAI
from src.ai.werewolf_ai import WerewolfAI
from src.ai.seer_ai import SeerAI
from src.ai.special_roles_ai import WitchAI, GuardAI, HunterAI


def test_llm_integration():
    """测试大模型集成"""
    print("=== 测试大模型集成 ===")

    # 测试Qwen3-30B LLM
    llm_manager = create_llm_manager("qwen3_30b")
    assert llm_manager is not None, "LLM管理器应该创建成功"
    
    # 创建测试游戏状态
    config = GameConfig(
        total_players=4,
        role_distribution={Role.VILLAGER: 2, Role.WEREWOLF: 1, Role.SEER: 1}
    )
    
    engine = GameEngine(config)
    game_state = engine.create_game("test", ["A", "B", "C", "D"])
    
    # 测试发言生成
    speech = llm_manager.generate_speech(Role.VILLAGER, game_state, {})
    assert isinstance(speech, str), "应该生成字符串发言"
    assert len(speech) > 0, "发言不应为空"
    print(f"村民发言: {speech}")
    
    # 测试策略分析
    actions = ["vote_1", "vote_2", "abstain"]
    analysis = llm_manager.generate_strategy_analysis(Role.VILLAGER, game_state, actions)
    assert isinstance(analysis, dict), "应该返回字典格式的分析"
    assert "recommended_action" in analysis, "应该包含推荐行动"
    print(f"策略分析: {analysis}")
    
    print("大模型集成测试通过!\n")


def test_ai_personality_system():
    """测试AI个性系统"""
    print("=== 测试AI个性系统 ===")
    
    personality_manager = AIPersonalityManager()
    
    # 测试个性生成
    config = personality_manager.create_ai_configuration(
        difficulty=DifficultyLevel.NORMAL,
        personality_type=PersonalityType.AGGRESSIVE
    )
    
    assert config.difficulty == DifficultyLevel.NORMAL, "难度应该正确设置"
    assert config.personality_type == PersonalityType.AGGRESSIVE, "个性类型应该正确设置"
    assert 0.0 <= config.traits.aggressiveness <= 1.0, "攻击性应该在0-1范围内"
    print(f"攻击型个性 - 攻击性: {config.traits.aggressiveness:.2f}")
    
    # 测试平衡团队创建
    team_configs = personality_manager.create_balanced_team(5, DifficultyLevel.HARD)
    assert len(team_configs) == 5, "应该创建5个配置"
    
    personality_types = [config.personality_type for config in team_configs]
    unique_types = set(personality_types)
    assert len(unique_types) >= 3, "团队应该有多样化的个性"
    print(f"团队个性分布: {[pt.value for pt in personality_types]}")
    
    # 测试团队分析
    analysis = personality_manager.analyze_team_composition(team_configs)
    assert "personality_distribution" in analysis, "应该包含个性分布"
    assert "team_balance_score" in analysis, "应该包含平衡分数"
    assert 0.0 <= analysis["team_balance_score"] <= 1.0, "平衡分数应该在0-1范围内"
    print(f"团队平衡分数: {analysis['team_balance_score']:.2f}")
    
    print("AI个性系统测试通过!\n")


def test_role_specific_ai():
    """测试角色专用AI"""
    print("=== 测试角色专用AI ===")
    
    # 创建测试AI玩家
    ai_player = EnhancedAIPlayer(1, "TestAI")
    
    # 创建测试游戏状态
    config = GameConfig(
        total_players=6,
        role_distribution={Role.VILLAGER: 2, Role.WEREWOLF: 2, Role.SEER: 1, Role.WITCH: 1}
    )
    
    engine = GameEngine(config)
    game_state = engine.create_game("test", ["A", "B", "C", "D", "E", "F"])
    
    # 测试村民AI
    ai_player.set_role(Role.VILLAGER)
    assert ai_player.role_ai is not None, "应该创建村民AI"
    assert isinstance(ai_player.role_ai, VillagerAI), "应该是VillagerAI实例"
    
    vote_target = ai_player.make_vote_decision(game_state, VoteType.ELIMINATION)
    assert vote_target is None or isinstance(vote_target, int), "投票目标应该是整数或None"
    print(f"村民AI投票决策: {vote_target}")
    
    # 测试狼人AI
    ai_player.set_role(Role.WEREWOLF)
    assert isinstance(ai_player.role_ai, WerewolfAI), "应该是WerewolfAI实例"
    
    kill_target = ai_player.make_vote_decision(game_state, VoteType.WEREWOLF_KILL)
    assert kill_target is None or isinstance(kill_target, int), "杀人目标应该是整数或None"
    print(f"狼人AI杀人决策: {kill_target}")
    
    # 测试预言家AI
    ai_player.set_role(Role.SEER)
    assert isinstance(ai_player.role_ai, SeerAI), "应该是SeerAI实例"
    
    check_target = ai_player.make_special_action_decision(game_state, "seer_check")
    assert check_target is None or isinstance(check_target, int), "查验目标应该是整数或None"
    print(f"预言家AI查验决策: {check_target}")
    
    # 测试女巫AI
    ai_player.set_role(Role.WITCH)
    assert isinstance(ai_player.role_ai, WitchAI), "应该是WitchAI实例"
    
    # 测试守卫AI
    ai_player.set_role(Role.GUARD)
    assert isinstance(ai_player.role_ai, GuardAI), "应该是GuardAI实例"
    
    protect_target = ai_player.make_special_action_decision(game_state, "guard_protect")
    assert protect_target is None or isinstance(protect_target, int), "保护目标应该是整数或None"
    print(f"守卫AI保护决策: {protect_target}")
    
    # 测试猎人AI
    ai_player.set_role(Role.HUNTER)
    assert isinstance(ai_player.role_ai, HunterAI), "应该是HunterAI实例"
    
    print("角色专用AI测试通过!\n")


def test_ai_strategy_manager():
    """测试AI策略管理器"""
    print("=== 测试AI策略管理器 ===")
    
    ai_manager = AIStrategyManager()
    
    # 测试创建AI玩家
    ai_player = ai_manager.create_ai_player(
        player_id=1,
        name="TestPlayer",
        difficulty=DifficultyLevel.HARD,
        personality_type=PersonalityType.ANALYTICAL
    )
    
    assert isinstance(ai_player, EnhancedAIPlayer), "应该创建EnhancedAIPlayer实例"
    assert ai_player.player_id == 1, "玩家ID应该正确"
    assert ai_player.name == "TestPlayer", "玩家名称应该正确"
    assert ai_player.ai_config.difficulty == DifficultyLevel.HARD, "难度应该正确"
    print(f"创建AI玩家: {ai_player.name} ({ai_player.ai_config.difficulty.value})")
    
    # 测试创建平衡团队
    player_names = ["Alpha", "Beta", "Gamma", "Delta"]
    team = ai_manager.create_balanced_team(player_names, DifficultyLevel.NORMAL)
    
    assert len(team) == 4, "应该创建4个AI玩家"
    assert all(isinstance(ai, EnhancedAIPlayer) for ai in team), "所有成员都应该是EnhancedAIPlayer"
    
    team_names = [ai.name for ai in team]
    assert team_names == player_names, "玩家名称应该匹配"
    print(f"创建平衡团队: {team_names}")
    
    # 测试团队分析
    analysis = ai_manager.get_team_analysis()
    assert isinstance(analysis, dict), "团队分析应该返回字典"
    assert "personality_distribution" in analysis, "应该包含个性分布"
    print(f"团队分析完成: {len(analysis)} 项指标")
    
    # 测试获取AI状态
    all_status = ai_manager.get_all_ai_status()
    assert len(all_status) >= 4, f"应该至少有4个AI状态，实际有{len(all_status)}个"
    
    for status in all_status:
        assert "player_id" in status, "状态应该包含玩家ID"
        assert "name" in status, "状态应该包含玩家名称"
        assert "difficulty" in status, "状态应该包含难度"
        assert "personality_type" in status, "状态应该包含个性类型"
    
    print("AI策略管理器测试通过!\n")


def test_enhanced_ai_player():
    """测试增强AI玩家"""
    print("=== 测试增强AI玩家 ===")
    
    ai_manager = AIStrategyManager()
    ai_player = ai_manager.create_ai_player(
        player_id=1,
        name="EnhancedAI",
        difficulty=DifficultyLevel.EXPERT,
        personality_type=PersonalityType.SOCIAL
    )
    
    # 测试角色设置
    ai_player.set_role(Role.VILLAGER)
    assert ai_player.role == Role.VILLAGER, "角色应该正确设置"
    assert ai_player.role_ai is not None, "应该创建角色AI"
    
    # 创建测试游戏状态
    config = GameConfig(
        total_players=4,
        role_distribution={Role.VILLAGER: 2, Role.WEREWOLF: 1, Role.SEER: 1}
    )
    
    engine = GameEngine(config)
    game_state = engine.create_game("test", ["A", "B", "C", "D"])
    
    # 测试决策制定
    vote_decision = ai_player.make_vote_decision(game_state, VoteType.ELIMINATION)
    assert vote_decision is None or isinstance(vote_decision, int), "投票决策应该是整数或None"
    
    # 测试发言生成
    speech = ai_player.generate_speech(game_state, GamePhase.DAY_DISCUSSION)
    assert isinstance(speech, str), "发言应该是字符串"
    print(f"AI发言: {speech}")
    
    # 测试状态获取
    status = ai_player.get_ai_status()
    assert isinstance(status, dict), "状态应该是字典"
    assert status["difficulty"] == "expert", "难度应该正确"
    assert status["personality_type"] == "social", "个性类型应该正确"
    print(f"AI状态: {status['name']} - {status['difficulty']} - {status['personality_type']}")
    
    # 测试事件处理
    ai_player.process_game_event("vote_cast", {"voter_id": 2, "target_id": 1})
    # 应该不抛出异常
    
    print("增强AI玩家测试通过!\n")


def test_adaptive_learning():
    """测试适应性学习"""
    print("=== 测试适应性学习 ===")
    
    ai_manager = AIStrategyManager()
    ai_player = ai_manager.create_ai_player(
        player_id=1,
        name="LearningAI",
        difficulty=DifficultyLevel.NORMAL,
        personality_type=PersonalityType.ANALYTICAL
    )
    
    # 启用学习
    ai_player.ai_config.learning_enabled = True
    ai_player.ai_config.adaptation_rate = 0.3
    
    # 记录初始特征
    initial_cautiousness = ai_player.ai_config.traits.cautiousness
    initial_confidence = ai_player.ai_config.traits.confidence
    
    # 模拟被淘汰事件
    ai_player.process_game_event("player_eliminated", {"player_id": 1})
    
    # 检查谨慎性是否增加
    new_cautiousness = ai_player.ai_config.traits.cautiousness
    assert new_cautiousness >= initial_cautiousness, "被淘汰后谨慎性应该增加或保持"
    print(f"谨慎性变化: {initial_cautiousness:.2f} -> {new_cautiousness:.2f}")
    
    # 模拟胜利事件
    ai_player.set_role(Role.VILLAGER)
    ai_player.process_game_event("game_ended", {"result": GameResult.VILLAGERS_WIN})
    
    # 检查自信心是否增加
    new_confidence = ai_player.ai_config.traits.confidence
    assert new_confidence >= initial_confidence, "胜利后自信心应该增加或保持"
    print(f"自信心变化: {initial_confidence:.2f} -> {new_confidence:.2f}")
    
    print("适应性学习测试通过!\n")


def test_integration():
    """测试整体集成"""
    print("=== 测试整体集成 ===")
    
    # 创建完整的AI游戏
    ai_manager = AIStrategyManager()
    
    # 创建AI团队
    player_names = ["Alice", "Bob", "Charlie", "David", "Eve", "Frank"]
    ai_team = ai_manager.create_balanced_team(player_names, DifficultyLevel.NORMAL)
    
    # 创建游戏
    config = GameConfig(
        total_players=6,
        role_distribution={
            Role.VILLAGER: 2,
            Role.WEREWOLF: 2,
            Role.SEER: 1,
            Role.WITCH: 1
        }
    )
    
    engine = GameEngine(config)
    game_state = engine.create_game("integration_test", player_names)
    
    # 为AI分配角色
    for player_id, player_info in game_state.players.items():
        if player_id <= len(ai_team):
            ai_player = ai_team[player_id - 1]
            ai_player.set_role(player_info.role)
    
    # 开始游戏
    engine.start_game()
    
    # 测试一轮完整流程
    engine.start_night_phase()
    
    # 夜晚行动
    for ai_player in ai_team:
        if not game_state.players[ai_player.player_id].is_alive():
            continue
            
        if ai_player.role == Role.WEREWOLF:
            target = ai_player.make_vote_decision(game_state, VoteType.WEREWOLF_KILL)
            if target:
                print(f"狼人 {ai_player.name} 选择杀死玩家 {target}")
        
        elif ai_player.role == Role.SEER:
            target = ai_player.make_special_action_decision(game_state, "seer_check")
            if target:
                print(f"预言家 {ai_player.name} 选择查验玩家 {target}")
    
    # 白天讨论
    engine.start_day_discussion()
    
    speech_count = 0
    for ai_player in ai_team:
        if not game_state.players[ai_player.player_id].is_alive():
            continue
            
        speech = ai_player.generate_speech(game_state, GamePhase.DAY_DISCUSSION)
        if speech:
            speech_count += 1
            print(f"{ai_player.name}: {speech}")
    
    assert speech_count > 0, "应该有AI发言"
    
    # 白天投票
    engine.start_day_voting()
    
    vote_count = 0
    for ai_player in ai_team:
        if not game_state.players[ai_player.player_id].is_alive():
            continue
            
        target = ai_player.make_vote_decision(game_state, VoteType.ELIMINATION)
        if target:
            vote_count += 1
            print(f"{ai_player.name} 投票给玩家 {target}")
    
    assert vote_count > 0, "应该有AI投票"
    
    print("整体集成测试通过!\n")


def main():
    """主测试函数"""
    print("狼人杀AI游戏 - Phase 3 AI策略系统测试")
    print("=" * 50)
    
    try:
        test_llm_integration()
        test_ai_personality_system()
        test_role_specific_ai()
        test_ai_strategy_manager()
        test_enhanced_ai_player()
        test_adaptive_learning()
        test_integration()
        
        print("=" * 50)
        print("所有 Phase 3 测试通过!")
        print("✅ 大模型集成")
        print("✅ AI个性系统")
        print("✅ 角色专用AI")
        print("✅ AI策略管理器")
        print("✅ 增强AI玩家")
        print("✅ 适应性学习")
        print("✅ 整体集成")
        print("=" * 50)
        
    except Exception as e:
        print(f"\n测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
