#!/usr/bin/env python3
"""
Qwen3-30B集成测试脚本
测试Qwen3-30B大模型与狼人杀AI系统的集成
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.models.enums import Role, GamePhase
from src.models.game_state import GameConfig, GameState
from src.ai.llm_integration import create_llm_manager
from src.config.llm_config import update_qwen3_service_config, validate_llm_service, get_service_status


def print_separator(char='-', length=60):
    """打印分隔线"""
    print(char * length)


def print_header(title):
    """打印标题"""
    print_separator('=')
    print(f" {title} ".center(60, '='))
    print_separator('=')


def test_qwen3_connection():
    """测试Qwen3-30B连接"""
    print_header("测试Qwen3-30B连接")
    
    # 更新Qwen3-30B配置
    update_qwen3_service_config(
        base_url="http://localhost:8005/v1",
        api_key="EMPTY",
        model_name="ckpt/Qwen/Qwen3-30B-A3B"
    )
    
    # 检查服务状态
    status = get_service_status()
    print(f"Qwen3-30B服务状态: {'可用' if status['qwen3_30b']['available'] else '不可用'}")
    
    if not status['qwen3_30b']['available']:
        print(f"错误: {status['qwen3_30b']['error']}")
        return False
    
    print("Qwen3-30B连接测试通过!")
    return True


def test_text_generation():
    """测试文本生成"""
    print_header("测试文本生成")
    
    # 创建LLM管理器
    llm_manager = create_llm_manager("qwen3_30b")
    
    # 测试简单文本生成
    test_prompt = "请用一句话描述狼人杀游戏"
    print(f"提示词: {test_prompt}")
    
    try:
        response = llm_manager.provider.generate_text_sync(test_prompt)
        print(f"响应: {response}")
        assert len(response) > 0, "响应不应为空"
        print("文本生成测试通过!")
        return True
    except Exception as e:
        print(f"文本生成测试失败: {e}")
        return False


def test_role_speech_generation():
    """测试角色发言生成"""
    print_header("测试角色发言生成")
    
    # 创建LLM管理器
    llm_manager = create_llm_manager("qwen3_30b")
    
    # 创建测试游戏状态
    config = GameConfig(
        total_players=6,
        role_distribution={Role.VILLAGER: 3, Role.WEREWOLF: 2, Role.SEER: 1}
    )
    
    game_state = GameState(
        game_id="test_game",
        config=config,
        players={},
        current_phase=GamePhase.DAY_DISCUSSION
    )
    
    # 测试不同角色的发言生成
    roles_to_test = [Role.VILLAGER, Role.WEREWOLF, Role.SEER]
    
    for role in roles_to_test:
        print(f"\n测试 {role.name} 发言:")
        context = {"player_id": 1}
        
        try:
            speech = llm_manager.generate_speech(role, game_state, context)
            print(f"发言: {speech}")
            assert len(speech) > 0, "发言不应为空"
        except Exception as e:
            print(f"发言生成失败: {e}")
            return False
    
    print("\n角色发言生成测试通过!")
    return True


def test_strategy_analysis():
    """测试策略分析"""
    print_header("测试策略分析")
    
    # 创建LLM管理器
    llm_manager = create_llm_manager("qwen3_30b")
    
    # 创建测试游戏状态
    config = GameConfig(
        total_players=6,
        role_distribution={Role.VILLAGER: 3, Role.WEREWOLF: 2, Role.SEER: 1}
    )
    
    game_state = GameState(
        game_id="test_game",
        config=config,
        players={},
        current_phase=GamePhase.DAY_VOTING
    )
    
    # 测试策略分析
    print("\n测试狼人策略分析:")
    possible_actions = ["kill_player_1", "kill_player_2", "kill_player_3"]
    
    try:
        analysis = llm_manager.generate_strategy_analysis(Role.WEREWOLF, game_state, possible_actions)
        print(f"推荐行动: {analysis.get('recommended_action', 'unknown')}")
        print(f"信心度: {analysis.get('confidence', 0)}")
        print(f"推理: {analysis.get('reasoning', 'no reasoning')}")
        
        assert "recommended_action" in analysis, "分析应包含推荐行动"
        print("策略分析测试通过!")
        return True
    except Exception as e:
        print(f"策略分析测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("狼人杀AI - Qwen3-30B集成测试")
    print("=" * 60)
    
    try:
        # 测试连接
        if not test_qwen3_connection():
            print("\n连接测试失败，请检查Qwen3-30B服务是否正常运行")
            return
        
        # 测试文本生成
        if not test_text_generation():
            print("\n文本生成测试失败")
            return
        
        # 测试角色发言生成
        if not test_role_speech_generation():
            print("\n角色发言生成测试失败")
            return
        
        # 测试策略分析
        if not test_strategy_analysis():
            print("\n策略分析测试失败")
            return
        
        print("\n" + "=" * 60)
        print("所有Qwen3-30B集成测试通过!")
        print("Qwen3-30B已成功集成到狼人杀AI系统中")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
